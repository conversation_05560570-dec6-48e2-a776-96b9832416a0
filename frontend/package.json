{"name": "library-management-frontend", "version": "0.1.0", "private": true, "dependencies": {"@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "antd": "^5.12.8", "axios": "^1.6.2", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "js-md5": "^0.8.3", "@types/js-md5": "^0.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.2"}, "proxy": "http://localhost:8080"}