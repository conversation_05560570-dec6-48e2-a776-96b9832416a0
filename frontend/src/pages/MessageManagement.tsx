import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Card,
  Tag,
  Tooltip,
  Alert,
  Select,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  MessageOutlined,
  CommentOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { Message, PageParams } from '@/types';
import { messageService } from '@/services/messageService';

const { TextArea } = Input;
const { Option } = Select;

const MessageManagement: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [replyingMessage, setReplyingMessage] = useState<Message | null>(null);
  const [viewingMessage, setViewingMessage] = useState<Message | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    userName: '',
    status: undefined as string | undefined,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    fetchMessages();
  }, [pagination.current, pagination.pageSize]);

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const params: PageParams = {
        current: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      const response = await messageService.getMessages(params);
      setMessages(response.records);
      setPagination(prev => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      message.error('获取留言列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReply = (record: Message) => {
    setReplyingMessage(record);
    setReplyModalVisible(true);
    form.resetFields();
  };

  const handleView = (record: Message) => {
    setViewingMessage(record);
    setDetailModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await messageService.deleteMessage(id);
      message.success('删除成功');
      fetchMessages();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleReplySubmit = async (values: any) => {
    if (!replyingMessage) return;

    try {
      await messageService.replyMessage(replyingMessage.id, values.reply);
      message.success('回复成功');
      setReplyModalVisible(false);
      fetchMessages();
    } catch (error) {
      message.error('回复失败');
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchMessages();
  };

  const handleReset = () => {
    setSearchParams({
      userName: '',
      status: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchMessages, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已回复':
        return 'green';
      case '待回复':
        return 'orange';
      case '已关闭':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content: string) => (
        <div style={{ maxWidth: 300 }}>
          {content.length > 50 ? `${content.substring(0, 50)}...` : content}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Message) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<MessageOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          {record.status === '待回复' && (
            <Tooltip title="回复">
              <Button
                type="link"
                size="small"
                icon={<CommentOutlined />}
                onClick={() => handleReply(record)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleReply(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条留言吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      {/* 提示信息 */}
      <Alert
        message="留言板管理说明"
        description="管理用户留言，包括查看留言内容、回复用户留言等。及时回复用户留言可以提升用户体验。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 搜索区域 */}
      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <Space wrap>
          <Input
            placeholder="用户姓名"
            value={searchParams.userName}
            onChange={(e) => setSearchParams(prev => ({ ...prev, userName: e.target.value }))}
            style={{ width: 200 }}
          />
          <Select
            placeholder="选择状态"
            value={searchParams.status}
            onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
            style={{ width: 200 }}
            allowClear
          >
            <Option value="待回复">待回复</Option>
            <Option value="已回复">已回复</Option>
            <Option value="已关闭">已关闭</Option>
          </Select>
          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </div>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={messages}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || 10,
            }));
          },
        }}
        rowKey="id"
        scroll={{ x: 1000 }}
      />

      {/* 回复模态框 */}
      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        {replyingMessage && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
              <p><strong>用户：</strong>{replyingMessage.userName}</p>
              <p><strong>留言时间：</strong>{dayjs(replyingMessage.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
              <p><strong>留言内容：</strong></p>
              <div style={{ padding: 8, background: 'white', borderRadius: 4, marginTop: 8 }}>
                {replyingMessage.content}
              </div>
            </div>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleReplySubmit}
            >
              <Form.Item
                name="reply"
                label="回复内容"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  placeholder="请输入回复内容"
                  rows={4}
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title="留言详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {viewingMessage && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <p><strong>用户：</strong>{viewingMessage.userName}</p>
              <p><strong>状态：</strong><Tag color={getStatusColor(viewingMessage.status)}>{viewingMessage.status}</Tag></p>
              <p><strong>留言时间：</strong>{dayjs(viewingMessage.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            </div>

            <div style={{ marginBottom: 16 }}>
              <h4>留言内容：</h4>
              <div style={{ padding: 12, background: '#f5f5f5', borderRadius: 4, marginTop: 8 }}>
                {viewingMessage.content}
              </div>
            </div>

            {viewingMessage.reply && (
              <div>
                <h4>回复内容：</h4>
                <div style={{ padding: 12, background: '#e6f7ff', borderRadius: 4, marginTop: 8 }}>
                  {viewingMessage.reply}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default MessageManagement;
