import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Table, List, Avatar } from 'antd';
import {
  UserOutlined,
  BookOutlined,
  ReadOutlined,
  PayCircleOutlined,
} from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBooks: 0,
    totalBorrows: 0,
    totalFines: 0,
  });

  const [recentBorrows] = useState([
    {
      id: 1,
      userName: '张三',
      bookName: 'Java编程思想',
      borrowTime: '2023-12-01',
      status: '已借出',
    },
    {
      id: 2,
      userName: '李四',
      bookName: 'Spring Boot实战',
      borrowTime: '2023-12-02',
      status: '已归还',
    },
  ]);

  const [recentNews] = useState([
    {
      id: 1,
      title: '图书馆开放时间调整通知',
      createTime: '2023-12-01',
    },
    {
      id: 2,
      title: '新书上架通知',
      createTime: '2023-12-02',
    },
  ]);

  useEffect(() => {
    // 这里可以调用API获取统计数据
    setStats({
      totalUsers: 150,
      totalBooks: 1200,
      totalBorrows: 89,
      totalFines: 5,
    });
  }, []);

  const borrowColumns = [
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '图书名称',
      dataIndex: 'bookName',
      key: 'bookName',
    },
    {
      title: '借阅时间',
      dataIndex: 'borrowTime',
      key: 'borrowTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总图书数"
              value={stats.totalBooks}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前借阅"
              value={stats.totalBorrows}
              prefix={<ReadOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待缴罚金"
              value={stats.totalFines}
              prefix={<PayCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={16}>
          <Card title="最近借阅记录" style={{ height: 400 }}>
            <Table
              columns={borrowColumns}
              dataSource={recentBorrows}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="最新公告" style={{ height: 400 }}>
            <List
              itemLayout="horizontal"
              dataSource={recentNews}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<BookOutlined />} />}
                    title={item.title}
                    description={item.createTime}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="欢迎信息">
            <p>欢迎您，{user?.name || user?.username}！</p>
            <p>今天是 {new Date().toLocaleDateString('zh-CN')}，祝您工作愉快！</p>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
