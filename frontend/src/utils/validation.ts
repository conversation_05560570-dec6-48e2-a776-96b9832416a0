// 输入验证工具类

export const validation = {
  // 验证邮箱格式
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 验证手机号格式
  phone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 验证密码强度（至少6位，包含字母和数字）
  password: (password: string): boolean => {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
    return passwordRegex.test(password);
  },

  // 验证ISBN格式
  isbn: (isbn: string): boolean => {
    // 移除连字符和空格
    const cleanIsbn = isbn.replace(/[-\s]/g, '');
    
    // ISBN-10 或 ISBN-13
    if (cleanIsbn.length === 10) {
      return /^\d{9}[\dX]$/.test(cleanIsbn);
    } else if (cleanIsbn.length === 13) {
      return /^\d{13}$/.test(cleanIsbn);
    }
    
    return false;
  },

  // 验证用户名格式（3-20位字母数字下划线）
  username: (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  },

  // 验证中文姓名
  chineseName: (name: string): boolean => {
    const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
    return nameRegex.test(name);
  },

  // 验证价格格式（最多两位小数）
  price: (price: string | number): boolean => {
    const priceRegex = /^\d+(\.\d{1,2})?$/;
    return priceRegex.test(String(price));
  },

  // 验证正整数
  positiveInteger: (num: string | number): boolean => {
    const intRegex = /^[1-9]\d*$/;
    return intRegex.test(String(num));
  },

  // 验证非负整数
  nonNegativeInteger: (num: string | number): boolean => {
    const intRegex = /^(0|[1-9]\d*)$/;
    return intRegex.test(String(num));
  },

  // 验证字符串长度
  length: (str: string, min: number, max: number): boolean => {
    return str.length >= min && str.length <= max;
  },

  // 验证是否为空
  required: (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },

  // 验证日期格式
  date: (dateStr: string): boolean => {
    const date = new Date(dateStr);
    return !isNaN(date.getTime());
  },

  // 验证URL格式
  url: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
};

// Ant Design 表单验证规则生成器
export const createValidationRules = {
  required: (message: string = '此字段为必填项') => ({
    required: true,
    message,
  }),

  email: (message: string = '请输入有效的邮箱地址') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.email(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  phone: (message: string = '请输入有效的手机号') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.phone(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  password: (message: string = '密码至少6位，包含字母和数字') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.password(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  isbn: (message: string = '请输入有效的ISBN') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.isbn(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  username: (message: string = '用户名为3-20位字母数字下划线') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.username(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  chineseName: (message: string = '请输入2-10位中文姓名') => ({
    validator: (_: any, value: string) => {
      if (!value || validation.chineseName(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  price: (message: string = '请输入有效的价格') => ({
    validator: (_: any, value: string | number) => {
      if (value === undefined || value === null || validation.price(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  positiveInteger: (message: string = '请输入正整数') => ({
    validator: (_: any, value: string | number) => {
      if (value === undefined || value === null || validation.positiveInteger(value)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message));
    },
  }),

  length: (min: number, max: number, message?: string) => ({
    validator: (_: any, value: string) => {
      if (!value || validation.length(value, min, max)) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(message || `长度应在${min}-${max}个字符之间`));
    },
  }),
};
