// 性能监控工具

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private enabled: boolean = process.env.NODE_ENV === 'development';

  // 开始计时
  start(name: string, metadata?: Record<string, any>): void {
    if (!this.enabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata,
    });
  }

  // 结束计时
  end(name: string): number | null {
    if (!this.enabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`, metric.metadata);

    return duration;
  }

  // 测量函数执行时间
  measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.start(name, metadata);
    const result = fn();
    this.end(name);
    return result;
  }

  // 测量异步函数执行时间
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.start(name, metadata);
    const result = await fn();
    this.end(name);
    return result;
  }

  // 获取所有指标
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // 清空指标
  clear(): void {
    this.metrics.clear();
  }

  // 启用/禁用监控
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  // 获取页面性能指标
  getPageMetrics(): Record<string, number> {
    if (!window.performance || !window.performance.timing) {
      return {};
    }

    const timing = window.performance.timing;
    const navigation = timing.navigationStart;

    return {
      // DNS查询时间
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcpConnect: timing.connectEnd - timing.connectStart,
      // 请求时间
      request: timing.responseStart - timing.requestStart,
      // 响应时间
      response: timing.responseEnd - timing.responseStart,
      // DOM解析时间
      domParse: timing.domInteractive - timing.responseEnd,
      // 资源加载时间
      resourceLoad: timing.loadEventStart - timing.domContentLoadedEventEnd,
      // 总加载时间
      totalLoad: timing.loadEventEnd - navigation,
      // 首次渲染时间
      firstPaint: timing.domLoading - navigation,
      // DOM准备时间
      domReady: timing.domContentLoadedEventEnd - navigation,
    };
  }

  // 监控长任务
  observeLongTasks(): void {
    if (!this.enabled || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn(`🐌 Long task detected: ${entry.duration.toFixed(2)}ms`, entry);
          }
        }
      });

      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.warn('Long task observation not supported');
    }
  }

  // 监控内存使用
  getMemoryUsage(): Record<string, number> | null {
    if (!('memory' in performance)) return null;

    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
    };
  }

  // 监控FPS
  monitorFPS(callback: (fps: number) => void): () => void {
    if (!this.enabled) return () => {};

    let frames = 0;
    let lastTime = performance.now();
    let animationId: number;

    const countFrame = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime));
        callback(fps);
        frames = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(countFrame);
    };

    animationId = requestAnimationFrame(countFrame);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 装饰器：自动监控方法性能
export function monitored(name?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyName}`;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measure(metricName, () => method.apply(this, args));
    };

    return descriptor;
  };
}

// 装饰器：自动监控异步方法性能
export function monitoredAsync(name?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyName}`;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measureAsync(metricName, () => method.apply(this, args));
    };

    return descriptor;
  };
}

// 初始化性能监控
export function initPerformanceMonitoring(): void {
  if (process.env.NODE_ENV === 'development') {
    performanceMonitor.observeLongTasks();
    
    // 页面加载完成后输出性能指标
    window.addEventListener('load', () => {
      setTimeout(() => {
        const metrics = performanceMonitor.getPageMetrics();
        console.table(metrics);
        
        const memory = performanceMonitor.getMemoryUsage();
        if (memory) {
          console.log('Memory usage:', memory);
        }
      }, 1000);
    });
  }
}
