import request from '@/utils/request';
import { BookBorrow, PageParams, PageResponse } from '@/types';

export const borrowService = {
  // 获取借阅记录列表
  async getBorrows(params: PageParams): Promise<PageResponse<BookBorrow>> {
    return await request.get('/tushujieyue/page', { params });
  },

  // 获取借阅记录详情
  async getBorrow(id: number): Promise<BookBorrow> {
    return await request.get(`/tushujieyue/info/${id}`);
  },

  // 创建借阅记录
  async createBorrow(data: Omit<BookBorrow, 'id'>): Promise<void> {
    await request.post('/tushujieyue/save', data);
  },

  // 更新借阅记录
  async updateBorrow(data: BookBorrow): Promise<void> {
    await request.post('/tushujieyue/update', data);
  },

  // 删除借阅记录
  async deleteBorrow(id: number): Promise<void> {
    await request.post('/tushujieyue/delete', { ids: [id] });
  },

  // 批量删除借阅记录
  async deleteBorrows(ids: number[]): Promise<void> {
    await request.post('/tushujieyue/delete', { ids });
  },

  // 审核通过借阅申请
  async approveBorrow(id: number): Promise<void> {
    await request.post(`/tushujieyue/approve/${id}`);
  },

  // 拒绝借阅申请
  async rejectBorrow(id: number): Promise<void> {
    await request.post(`/tushujieyue/reject/${id}`);
  },

  // 归还图书
  async returnBook(borrowId: number, returnData: any): Promise<void> {
    await request.post('/tushuguihai/save', {
      borrowId,
      ...returnData,
    });
  },
};
