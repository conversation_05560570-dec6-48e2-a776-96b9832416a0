import request from '@/utils/request';
import { BookReturn, PageParams, PageResponse } from '@/types';

export const returnService = {
  // 获取归还记录列表
  async getReturns(params: PageParams): Promise<PageResponse<BookReturn>> {
    return await request.get('/tushuguihai/page', { params });
  },

  // 获取归还记录详情
  async getReturn(id: number): Promise<BookReturn> {
    return await request.get(`/tushuguihai/info/${id}`);
  },

  // 创建归还记录
  async createReturn(data: Omit<BookReturn, 'id'>): Promise<void> {
    await request.post('/tushuguihai/save', data);
  },

  // 更新归还记录
  async updateReturn(data: BookReturn): Promise<void> {
    await request.post('/tushuguihai/update', data);
  },

  // 删除归还记录
  async deleteReturn(id: number): Promise<void> {
    await request.post('/tushuguihai/delete', { ids: [id] });
  },

  // 批量删除归还记录
  async deleteReturns(ids: number[]): Promise<void> {
    await request.post('/tushuguihai/delete', { ids });
  },

  // 审核通过归还申请
  async approveReturn(id: number): Promise<void> {
    await request.post(`/tushuguihai/approve/${id}`);
  },

  // 拒绝归还申请
  async rejectReturn(id: number): Promise<void> {
    await request.post(`/tushuguihai/reject/${id}`);
  },
};
