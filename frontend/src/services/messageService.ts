import request from '@/utils/request';
import { Message, PageParams, PageResponse } from '@/types';

export const messageService = {
  // 获取留言列表
  async getMessages(params: PageParams): Promise<PageResponse<Message>> {
    return await request.get('/messages/page', { params });
  },

  // 获取留言详情
  async getMessage(id: number): Promise<Message> {
    return await request.get(`/messages/info/${id}`);
  },

  // 创建留言
  async createMessage(data: Omit<Message, 'id'>): Promise<void> {
    await request.post('/messages/save', data);
  },

  // 更新留言
  async updateMessage(data: Message): Promise<void> {
    await request.post('/messages/update', data);
  },

  // 删除留言
  async deleteMessage(id: number): Promise<void> {
    await request.post('/messages/delete', { ids: [id] });
  },

  // 批量删除留言
  async deleteMessages(ids: number[]): Promise<void> {
    await request.post('/messages/delete', { ids });
  },

  // 回复留言
  async replyMessage(id: number, reply: string): Promise<void> {
    await request.post(`/messages/reply/${id}`, { reply });
  },
};
