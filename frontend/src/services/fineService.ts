import request from '@/utils/request';
import { FinePayment, PageParams, PageResponse } from '@/types';

export const fineService = {
  // 获取罚金记录列表
  async getFines(params: PageParams): Promise<PageResponse<FinePayment>> {
    return await request.get('/jiaonafajin/page', { params });
  },

  // 获取罚金记录详情
  async getFine(id: number): Promise<FinePayment> {
    return await request.get(`/jiaonafajin/info/${id}`);
  },

  // 创建罚金记录
  async createFine(data: Omit<FinePayment, 'id'>): Promise<void> {
    await request.post('/jiaonafajin/save', data);
  },

  // 更新罚金记录
  async updateFine(data: FinePayment): Promise<void> {
    await request.post('/jiaonafajin/update', data);
  },

  // 删除罚金记录
  async deleteFine(id: number): Promise<void> {
    await request.post('/jiaonafajin/delete', { ids: [id] });
  },

  // 批量删除罚金记录
  async deleteFines(ids: number[]): Promise<void> {
    await request.post('/jiaonafajin/delete', { ids });
  },

  // 确认缴费
  async payFine(id: number): Promise<void> {
    await request.post(`/jiaonafajin/pay/${id}`);
  },

  // 获取罚金统计信息
  async getStatistics(): Promise<{
    totalFines: number;
    paidFines: number;
    unpaidFines: number;
  }> {
    return await request.get('/jiaonafajin/statistics');
  },
};
