import request from '@/utils/request';
import { User, PageParams, PageResponse } from '@/types';

export const userService = {
  // 获取用户列表
  async getUsers(params: PageParams): Promise<PageResponse<User>> {
    return await request.get('/yonghu/page', { params });
  },

  // 获取用户详情
  async getUser(id: number): Promise<User> {
    return await request.get(`/yonghu/info/${id}`);
  },

  // 创建用户
  async createUser(data: Omit<User, 'id'>): Promise<void> {
    await request.post('/yonghu/save', data);
  },

  // 更新用户
  async updateUser(data: User): Promise<void> {
    await request.post('/yonghu/update', data);
  },

  // 删除用户
  async deleteUser(id: number): Promise<void> {
    await request.post(`/yonghu/delete`, { ids: [id] });
  },

  // 批量删除用户
  async deleteUsers(ids: number[]): Promise<void> {
    await request.post('/yonghu/delete', { ids });
  },
};
