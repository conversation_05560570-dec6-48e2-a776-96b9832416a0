* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  height: 100vh;
}

.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.ant-layout-sider {
  background: #001529;
}

.ant-menu-dark {
  background: #001529;
}

.ant-layout-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
}
