// 用户相关类型
export interface User {
  id: number;
  username: string;
  password?: string;
  name: string;
  phone?: string;
  email?: string;
  role: string;
  avatar?: string;
  createTime?: string;
  updateTime?: string;
}

// 图书分类类型
export interface BookCategory {
  id: number;
  name: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

// 图书信息类型
export interface Book {
  id: number;
  name: string;
  author: string;
  publisher: string;
  isbn: string;
  categoryId: number;
  categoryName?: string;
  price: number;
  stock: number;
  description?: string;
  cover?: string;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 图书借阅类型
export interface BookBorrow {
  id: number;
  userId: number;
  userName?: string;
  bookId: number;
  bookName?: string;
  borrowTime: string;
  returnTime?: string;
  expectedReturnTime: string;
  status: string;
  fine?: number;
  createTime?: string;
  updateTime?: string;
}

// 图书归还类型
export interface BookReturn {
  id: number;
  borrowId: number;
  userId: number;
  userName?: string;
  bookId: number;
  bookName?: string;
  returnTime: string;
  fine: number;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 罚金缴纳类型
export interface FinePayment {
  id: number;
  userId: number;
  userName?: string;
  returnId: number;
  amount: number;
  paymentTime?: string;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 留言类型
export interface Message {
  id: number;
  userId: number;
  userName?: string;
  content: string;
  reply?: string;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 收藏类型
export interface Favorite {
  id: number;
  userId: number;
  bookId: number;
  bookName?: string;
  createTime?: string;
}

// 公告类型
export interface News {
  id: number;
  title: string;
  content: string;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 轮播图类型
export interface Config {
  id: number;
  name: string;
  value: string;
  type: string;
  createTime?: string;
  updateTime?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 分页请求参数
export interface PageParams {
  current?: number;
  size?: number;
  [key: string]: any;
}

// 登录请求参数
export interface LoginParams {
  username: string;
  password: string;
  role?: string;
}

// 登录响应
export interface LoginResponse {
  token: string;
  user: User;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
}
