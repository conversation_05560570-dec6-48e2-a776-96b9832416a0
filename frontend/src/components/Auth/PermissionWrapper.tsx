import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionWrapperProps {
  children: ReactNode;
  permission?: string;
  role?: string;
  fallback?: ReactNode;
}

const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  role,
  fallback = null,
}) => {
  const { user } = useAuth();

  // 如果没有用户信息，不显示
  if (!user) {
    return <>{fallback}</>;
  }

  // 如果指定了角色，检查用户角色
  if (role && user.role !== role) {
    return <>{fallback}</>;
  }

  // 如果指定了权限，检查用户权限
  if (permission) {
    // 这里可以根据实际的权限系统来实现
    // 目前简单地检查管理员权限
    if (user.role !== '管理员' && permission.includes('admin')) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

export default PermissionWrapper;
