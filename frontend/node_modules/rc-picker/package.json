{"name": "rc-picker", "version": "4.11.3", "description": "React date & time picker", "keywords": ["react", "react-component", "react-picker"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib"], "homepage": "https://react-component.github.io/picker", "repository": {"type": "git", "url": "**************:react-component/picker.git"}, "bugs": {"url": "http://github.com/react-component/picker/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "gh-pages": "npm run build && father doc deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "lint:tsc": "tsc -p tsconfig.json --noEmit", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "rc-test", "coverage": "father test --coverage", "now-build": "npm run build", "prepare": "npx husky"}, "dependencies": {"@babel/runtime": "^7.24.7", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.1", "rc-overflow": "^1.3.2", "rc-resize-observer": "^1.4.0", "rc-util": "^5.43.0"}, "engines": {"node": ">=8.x"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/react": "^16.0.0", "@types/classnames": "^2.2.9", "@types/jest": "^29.4.0", "@types/luxon": "^3.2.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.8", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "date-fns": "2.x", "dayjs": "1.x", "dumi": "^2.1.15", "eslint": "^8.56.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^28.8.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^55.0.0", "father": "^4.0.0", "glob": "^10.4.1", "husky": "^9.0.11", "less": "^4.2.0", "lint-staged": "^15.2.7", "luxon": "3.x", "mockdate": "^3.0.2", "moment": "^2.24.0", "moment-timezone": "^0.5.45", "np": "^10.0.2", "prettier": "^3.1.0", "rc-test": "^7.0.9", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0"}, "peerDependencies": {"date-fns": ">= 2.x", "dayjs": ">= 1.x", "luxon": ">= 3.x", "moment": ">= 2.x", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}}}