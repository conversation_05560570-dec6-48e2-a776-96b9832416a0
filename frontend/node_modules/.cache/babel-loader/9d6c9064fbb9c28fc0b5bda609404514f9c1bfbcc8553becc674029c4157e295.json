{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport Button from '../button';\nconst Operation = props => {\n  const {\n    disabled,\n    moveToLeft,\n    moveToRight,\n    leftArrowText = '',\n    rightArrowText = '',\n    leftActive,\n    rightActive,\n    className,\n    style,\n    direction,\n    oneWay\n  } = props;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !rightActive,\n    onClick: moveToRight,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null)\n  }, rightArrowText), !oneWay && (/*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !leftActive,\n    onClick: moveToLeft,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  }, leftArrowText)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Operation.displayName = 'Operation';\n}\nexport default Operation;", "map": {"version": 3, "names": ["React", "LeftOutlined", "RightOutlined", "<PERSON><PERSON>", "Operation", "props", "disabled", "moveToLeft", "moveToRight", "leftArrowText", "rightArrowText", "leftActive", "rightActive", "className", "style", "direction", "oneWay", "createElement", "type", "size", "onClick", "icon", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/transfer/operation.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport Button from '../button';\nconst Operation = props => {\n  const {\n    disabled,\n    moveToLeft,\n    moveToRight,\n    leftArrowText = '',\n    rightArrowText = '',\n    leftActive,\n    rightActive,\n    className,\n    style,\n    direction,\n    oneWay\n  } = props;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !rightActive,\n    onClick: moveToRight,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null)\n  }, rightArrowText), !oneWay && (/*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    size: \"small\",\n    disabled: disabled || !leftActive,\n    onClick: moveToLeft,\n    icon: direction !== 'rtl' ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  }, leftArrowText)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Operation.displayName = 'Operation';\n}\nexport default Operation;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,MAAM;IACJC,QAAQ;IACRC,UAAU;IACVC,WAAW;IACXC,aAAa,GAAG,EAAE;IAClBC,cAAc,GAAG,EAAE;IACnBC,UAAU;IACVC,WAAW;IACXC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC;EACF,CAAC,GAAGX,KAAK;EACT,OAAO,aAAaL,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC7CJ,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAad,KAAK,CAACiB,aAAa,CAACd,MAAM,EAAE;IAC1Ce,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,OAAO;IACbb,QAAQ,EAAEA,QAAQ,IAAI,CAACM,WAAW;IAClCQ,OAAO,EAAEZ,WAAW;IACpBa,IAAI,EAAEN,SAAS,KAAK,KAAK,GAAG,aAAaf,KAAK,CAACiB,aAAa,CAACf,aAAa,EAAE,IAAI,CAAC,GAAG,aAAaF,KAAK,CAACiB,aAAa,CAAChB,YAAY,EAAE,IAAI;EACzI,CAAC,EAAES,cAAc,CAAC,EAAE,CAACM,MAAM,KAAK,aAAahB,KAAK,CAACiB,aAAa,CAACd,MAAM,EAAE;IACvEe,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,OAAO;IACbb,QAAQ,EAAEA,QAAQ,IAAI,CAACK,UAAU;IACjCS,OAAO,EAAEb,UAAU;IACnBc,IAAI,EAAEN,SAAS,KAAK,KAAK,GAAG,aAAaf,KAAK,CAACiB,aAAa,CAAChB,YAAY,EAAE,IAAI,CAAC,GAAG,aAAaD,KAAK,CAACiB,aAAa,CAACf,aAAa,EAAE,IAAI;EACzI,CAAC,EAAEO,aAAa,CAAC,CAAC,CAAC;AACrB,CAAC;AACD,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpB,SAAS,CAACqB,WAAW,GAAG,WAAW;AACrC;AACA,eAAerB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}