{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { cloneElement } from '../_util/reactNode';\nimport { ConfigContext } from '../config-provider';\nimport SingleNumber from './SingleNumber';\nconst ScrollNumber = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      count,\n      className,\n      motionClassName,\n      style,\n      title,\n      show,\n      component: Component = 'sup',\n      children\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"count\", \"className\", \"motionClassName\", \"style\", \"title\", \"show\", \"component\", \"children\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('scroll-number', customizePrefixCls);\n  // ============================ Render ============================\n  const newProps = Object.assign(Object.assign({}, restProps), {\n    'data-show': show,\n    style,\n    className: classNames(prefixCls, className, motionClassName),\n    title: title\n  });\n  // Only integer need motion\n  let numberNodes = count;\n  if (count && Number(count) % 1 === 0) {\n    const numberList = String(count).split('');\n    numberNodes = /*#__PURE__*/React.createElement(\"bdi\", null, numberList.map((num, i) => (/*#__PURE__*/React.createElement(SingleNumber, {\n      prefixCls: prefixCls,\n      count: Number(count),\n      value: num,\n      // eslint-disable-next-line react/no-array-index-key\n      key: numberList.length - i\n    }))));\n  }\n  // allow specify the border\n  // mock border-color by box-shadow for compatible with old usage:\n  // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />\n  if (style === null || style === void 0 ? void 0 : style.borderColor) {\n    newProps.style = Object.assign(Object.assign({}, style), {\n      boxShadow: `0 0 0 1px ${style.borderColor} inset`\n    });\n  }\n  if (children) {\n    return cloneElement(children, oriProps => ({\n      className: classNames(`${prefixCls}-custom-component`, oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Component, Object.assign({}, newProps, {\n    ref: ref\n  }), numberNodes);\n});\nexport default ScrollNumber;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "cloneElement", "ConfigContext", "SingleNumber", "ScrollNumber", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "count", "className", "motionClassName", "style", "title", "show", "component", "Component", "children", "restProps", "getPrefixCls", "useContext", "newProps", "assign", "numberNodes", "Number", "numberList", "String", "split", "createElement", "map", "num", "value", "key", "borderColor", "boxShadow", "oriProps"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/badge/ScrollNumber.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { cloneElement } from '../_util/reactNode';\nimport { ConfigContext } from '../config-provider';\nimport SingleNumber from './SingleNumber';\nconst ScrollNumber = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      count,\n      className,\n      motionClassName,\n      style,\n      title,\n      show,\n      component: Component = 'sup',\n      children\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"count\", \"className\", \"motionClassName\", \"style\", \"title\", \"show\", \"component\", \"children\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('scroll-number', customizePrefixCls);\n  // ============================ Render ============================\n  const newProps = Object.assign(Object.assign({}, restProps), {\n    'data-show': show,\n    style,\n    className: classNames(prefixCls, className, motionClassName),\n    title: title\n  });\n  // Only integer need motion\n  let numberNodes = count;\n  if (count && Number(count) % 1 === 0) {\n    const numberList = String(count).split('');\n    numberNodes = /*#__PURE__*/React.createElement(\"bdi\", null, numberList.map((num, i) => (/*#__PURE__*/React.createElement(SingleNumber, {\n      prefixCls: prefixCls,\n      count: Number(count),\n      value: num,\n      // eslint-disable-next-line react/no-array-index-key\n      key: numberList.length - i\n    }))));\n  }\n  // allow specify the border\n  // mock border-color by box-shadow for compatible with old usage:\n  // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />\n  if (style === null || style === void 0 ? void 0 : style.borderColor) {\n    newProps.style = Object.assign(Object.assign({}, style), {\n      boxShadow: `0 0 0 1px ${style.borderColor} inset`\n    });\n  }\n  if (children) {\n    return cloneElement(children, oriProps => ({\n      className: classNames(`${prefixCls}-custom-component`, oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Component, Object.assign({}, newProps, {\n    ref: ref\n  }), numberNodes);\n});\nexport default ScrollNumber;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,MAAMC,YAAY,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLC,SAAS;MACTC,eAAe;MACfC,KAAK;MACLC,KAAK;MACLC,IAAI;MACJC,SAAS,EAAEC,SAAS,GAAG,KAAK;MAC5BC;IACF,CAAC,GAAGZ,KAAK;IACTa,SAAS,GAAGlC,MAAM,CAACqB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EACtI,MAAM;IACJc;EACF,CAAC,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,aAAa,CAAC;EACnC,MAAMM,SAAS,GAAGY,YAAY,CAAC,eAAe,EAAEX,kBAAkB,CAAC;EACnE;EACA,MAAMa,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,CAACjC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,EAAE;IAC3D,WAAW,EAAEJ,IAAI;IACjBF,KAAK;IACLF,SAAS,EAAEX,UAAU,CAACQ,SAAS,EAAEG,SAAS,EAAEC,eAAe,CAAC;IAC5DE,KAAK,EAAEA;EACT,CAAC,CAAC;EACF;EACA,IAAIU,WAAW,GAAGd,KAAK;EACvB,IAAIA,KAAK,IAAIe,MAAM,CAACf,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACpC,MAAMgB,UAAU,GAAGC,MAAM,CAACjB,KAAK,CAAC,CAACkB,KAAK,CAAC,EAAE,CAAC;IAC1CJ,WAAW,GAAG,aAAazB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEH,UAAU,CAACI,GAAG,CAAC,CAACC,GAAG,EAAEnC,CAAC,MAAM,aAAaG,KAAK,CAAC8B,aAAa,CAAC1B,YAAY,EAAE;MACrIK,SAAS,EAAEA,SAAS;MACpBE,KAAK,EAAEe,MAAM,CAACf,KAAK,CAAC;MACpBsB,KAAK,EAAED,GAAG;MACV;MACAE,GAAG,EAAEP,UAAU,CAAC7B,MAAM,GAAGD;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;EACP;EACA;EACA;EACA;EACA,IAAIiB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACqB,WAAW,EAAE;IACnEZ,QAAQ,CAACT,KAAK,GAAGvB,MAAM,CAACiC,MAAM,CAACjC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,CAAC,EAAE;MACvDsB,SAAS,EAAE,aAAatB,KAAK,CAACqB,WAAW;IAC3C,CAAC,CAAC;EACJ;EACA,IAAIhB,QAAQ,EAAE;IACZ,OAAOjB,YAAY,CAACiB,QAAQ,EAAEkB,QAAQ,KAAK;MACzCzB,SAAS,EAAEX,UAAU,CAAC,GAAGQ,SAAS,mBAAmB,EAAE4B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACzB,SAAS,EAAEC,eAAe;IAChJ,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAab,KAAK,CAAC8B,aAAa,CAACZ,SAAS,EAAE3B,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAED,QAAQ,EAAE;IAC7Ef,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEiB,WAAW,CAAC;AAClB,CAAC,CAAC;AACF,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}