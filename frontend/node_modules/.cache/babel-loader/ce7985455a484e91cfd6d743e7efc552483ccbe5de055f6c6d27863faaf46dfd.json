{"ast": null, "code": "\"use client\";\n\nimport InternalDropdown from './dropdown';\nimport DropdownButton from './dropdown-button';\nconst Dropdown = InternalDropdown;\nDropdown.Button = DropdownButton;\nexport default Dropdown;", "map": {"version": 3, "names": ["InternalDropdown", "DropdownButton", "Dropdown", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/dropdown/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalDropdown from './dropdown';\nimport DropdownButton from './dropdown-button';\nconst Dropdown = InternalDropdown;\nDropdown.Button = DropdownButton;\nexport default Dropdown;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,gBAAgB,MAAM,YAAY;AACzC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,MAAMC,QAAQ,GAAGF,gBAAgB;AACjCE,QAAQ,CAACC,MAAM,GAAGF,cAAc;AAChC,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}