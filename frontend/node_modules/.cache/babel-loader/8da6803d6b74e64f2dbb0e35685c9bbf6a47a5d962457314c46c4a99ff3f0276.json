{"ast": null, "code": "export function isPresetSize(size) {\n  return ['small', 'middle', 'large'].includes(size);\n}\nexport function isValidGapNumber(size) {\n  if (!size) {\n    // The case of size = 0 is deliberately excluded here, because the default value of the gap attribute in CSS is 0, so if the user passes 0 in, we can directly ignore it.\n    return false;\n  }\n  return typeof size === 'number' && !Number.isNaN(size);\n}", "map": {"version": 3, "names": ["isPresetSize", "size", "includes", "isValidGapNumber", "Number", "isNaN"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/gapSize.js"], "sourcesContent": ["export function isPresetSize(size) {\n  return ['small', 'middle', 'large'].includes(size);\n}\nexport function isValidGapNumber(size) {\n  if (!size) {\n    // The case of size = 0 is deliberately excluded here, because the default value of the gap attribute in CSS is 0, so if the user passes 0 in, we can directly ignore it.\n    return false;\n  }\n  return typeof size === 'number' && !Number.isNaN(size);\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,IAAI,CAAC;AACpD;AACA,OAAO,SAASE,gBAAgBA,CAACF,IAAI,EAAE;EACrC,IAAI,CAACA,IAAI,EAAE;IACT;IACA,OAAO,KAAK;EACd;EACA,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,IAAI,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}