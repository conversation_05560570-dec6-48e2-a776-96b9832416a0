{"ast": null, "code": "import React from 'react';\nimport { useToken } from '../theme/internal';\nimport { addMediaQueryListener, removeMediaQueryListener } from './mediaQueryUtil';\nexport const responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nconst getResponsiveMap = token => ({\n  xs: `(max-width: ${token.screenXSMax}px)`,\n  sm: `(min-width: ${token.screenSM}px)`,\n  md: `(min-width: ${token.screenMD}px)`,\n  lg: `(min-width: ${token.screenLG}px)`,\n  xl: `(min-width: ${token.screenXL}px)`,\n  xxl: `(min-width: ${token.screenXXL}px)`\n});\n/**\n * Ensures that the breakpoints token are valid, in good order\n * For each breakpoint : screenMin <= screen <= screenMax and screenMax <= nextScreenMin\n */\nconst validateBreakpoints = token => {\n  const indexableToken = token;\n  const revBreakpoints = [].concat(responsiveArray).reverse();\n  revBreakpoints.forEach((breakpoint, i) => {\n    const breakpointUpper = breakpoint.toUpperCase();\n    const screenMin = `screen${breakpointUpper}Min`;\n    const screen = `screen${breakpointUpper}`;\n    if (!(indexableToken[screenMin] <= indexableToken[screen])) {\n      throw new Error(`${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`);\n    }\n    if (i < revBreakpoints.length - 1) {\n      const screenMax = `screen${breakpointUpper}Max`;\n      if (!(indexableToken[screen] <= indexableToken[screenMax])) {\n        throw new Error(`${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`);\n      }\n      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();\n      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;\n      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {\n        throw new Error(`${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`);\n      }\n    }\n  });\n  return token;\n};\nexport const matchScreen = (screens, screenSizes) => {\n  if (!screenSizes) {\n    return;\n  }\n  for (const breakpoint of responsiveArray) {\n    if (screens[breakpoint] && (screenSizes === null || screenSizes === void 0 ? void 0 : screenSizes[breakpoint]) !== undefined) {\n      return screenSizes[breakpoint];\n    }\n  }\n};\nconst useResponsiveObserver = () => {\n  const [, token] = useToken();\n  const responsiveMap = getResponsiveMap(validateBreakpoints(token));\n  // To avoid repeat create instance, we add `useMemo` here.\n  return React.useMemo(() => {\n    const subscribers = new Map();\n    let subUid = -1;\n    let screens = {};\n    return {\n      responsiveMap,\n      matchHandlers: {},\n      dispatch(pointMap) {\n        screens = pointMap;\n        subscribers.forEach(func => func(screens));\n        return subscribers.size >= 1;\n      },\n      subscribe(func) {\n        if (!subscribers.size) {\n          this.register();\n        }\n        subUid += 1;\n        subscribers.set(subUid, func);\n        func(screens);\n        return subUid;\n      },\n      unsubscribe(paramToken) {\n        subscribers.delete(paramToken);\n        if (!subscribers.size) {\n          this.unregister();\n        }\n      },\n      register() {\n        Object.entries(responsiveMap).forEach(([screen, mediaQuery]) => {\n          const listener = ({\n            matches\n          }) => {\n            this.dispatch(Object.assign(Object.assign({}, screens), {\n              [screen]: matches\n            }));\n          };\n          const mql = window.matchMedia(mediaQuery);\n          addMediaQueryListener(mql, listener);\n          this.matchHandlers[mediaQuery] = {\n            mql,\n            listener\n          };\n          listener(mql);\n        });\n      },\n      unregister() {\n        Object.values(responsiveMap).forEach(mediaQuery => {\n          const handler = this.matchHandlers[mediaQuery];\n          removeMediaQueryListener(handler === null || handler === void 0 ? void 0 : handler.mql, handler === null || handler === void 0 ? void 0 : handler.listener);\n        });\n        subscribers.clear();\n      }\n    };\n  }, [token]);\n};\nexport default useResponsiveObserver;", "map": {"version": 3, "names": ["React", "useToken", "addMediaQueryListener", "removeMediaQueryListener", "responsiveArray", "getResponsiveMap", "token", "xs", "screenXSMax", "sm", "screenSM", "md", "screenMD", "lg", "screenLG", "xl", "screenXL", "xxl", "screenXXL", "validateBreakpoints", "indexableToken", "revBreakpoints", "concat", "reverse", "for<PERSON>ach", "breakpoint", "i", "breakpointUpper", "toUpperCase", "screenMin", "screen", "Error", "length", "screenMax", "nextBreakpointUpperMin", "nextScreenMin", "matchScreen", "screens", "screenSizes", "undefined", "useResponsiveObserver", "responsiveMap", "useMemo", "subscribers", "Map", "subUid", "matchHandlers", "dispatch", "pointMap", "func", "size", "subscribe", "register", "set", "unsubscribe", "paramToken", "delete", "unregister", "Object", "entries", "mediaQuery", "listener", "matches", "assign", "mql", "window", "matchMedia", "values", "handler", "clear"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/responsiveObserver.js"], "sourcesContent": ["import React from 'react';\nimport { useToken } from '../theme/internal';\nimport { addMediaQueryListener, removeMediaQueryListener } from './mediaQueryUtil';\nexport const responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nconst getResponsiveMap = token => ({\n  xs: `(max-width: ${token.screenXSMax}px)`,\n  sm: `(min-width: ${token.screenSM}px)`,\n  md: `(min-width: ${token.screenMD}px)`,\n  lg: `(min-width: ${token.screenLG}px)`,\n  xl: `(min-width: ${token.screenXL}px)`,\n  xxl: `(min-width: ${token.screenXXL}px)`\n});\n/**\n * Ensures that the breakpoints token are valid, in good order\n * For each breakpoint : screenMin <= screen <= screenMax and screenMax <= nextScreenMin\n */\nconst validateBreakpoints = token => {\n  const indexableToken = token;\n  const revBreakpoints = [].concat(responsiveArray).reverse();\n  revBreakpoints.forEach((breakpoint, i) => {\n    const breakpointUpper = breakpoint.toUpperCase();\n    const screenMin = `screen${breakpointUpper}Min`;\n    const screen = `screen${breakpointUpper}`;\n    if (!(indexableToken[screenMin] <= indexableToken[screen])) {\n      throw new Error(`${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`);\n    }\n    if (i < revBreakpoints.length - 1) {\n      const screenMax = `screen${breakpointUpper}Max`;\n      if (!(indexableToken[screen] <= indexableToken[screenMax])) {\n        throw new Error(`${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`);\n      }\n      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();\n      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;\n      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {\n        throw new Error(`${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`);\n      }\n    }\n  });\n  return token;\n};\nexport const matchScreen = (screens, screenSizes) => {\n  if (!screenSizes) {\n    return;\n  }\n  for (const breakpoint of responsiveArray) {\n    if (screens[breakpoint] && (screenSizes === null || screenSizes === void 0 ? void 0 : screenSizes[breakpoint]) !== undefined) {\n      return screenSizes[breakpoint];\n    }\n  }\n};\nconst useResponsiveObserver = () => {\n  const [, token] = useToken();\n  const responsiveMap = getResponsiveMap(validateBreakpoints(token));\n  // To avoid repeat create instance, we add `useMemo` here.\n  return React.useMemo(() => {\n    const subscribers = new Map();\n    let subUid = -1;\n    let screens = {};\n    return {\n      responsiveMap,\n      matchHandlers: {},\n      dispatch(pointMap) {\n        screens = pointMap;\n        subscribers.forEach(func => func(screens));\n        return subscribers.size >= 1;\n      },\n      subscribe(func) {\n        if (!subscribers.size) {\n          this.register();\n        }\n        subUid += 1;\n        subscribers.set(subUid, func);\n        func(screens);\n        return subUid;\n      },\n      unsubscribe(paramToken) {\n        subscribers.delete(paramToken);\n        if (!subscribers.size) {\n          this.unregister();\n        }\n      },\n      register() {\n        Object.entries(responsiveMap).forEach(([screen, mediaQuery]) => {\n          const listener = ({\n            matches\n          }) => {\n            this.dispatch(Object.assign(Object.assign({}, screens), {\n              [screen]: matches\n            }));\n          };\n          const mql = window.matchMedia(mediaQuery);\n          addMediaQueryListener(mql, listener);\n          this.matchHandlers[mediaQuery] = {\n            mql,\n            listener\n          };\n          listener(mql);\n        });\n      },\n      unregister() {\n        Object.values(responsiveMap).forEach(mediaQuery => {\n          const handler = this.matchHandlers[mediaQuery];\n          removeMediaQueryListener(handler === null || handler === void 0 ? void 0 : handler.mql, handler === null || handler === void 0 ? void 0 : handler.listener);\n        });\n        subscribers.clear();\n      }\n    };\n  }, [token]);\n};\nexport default useResponsiveObserver;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,kBAAkB;AAClF,OAAO,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACpE,MAAMC,gBAAgB,GAAGC,KAAK,KAAK;EACjCC,EAAE,EAAE,eAAeD,KAAK,CAACE,WAAW,KAAK;EACzCC,EAAE,EAAE,eAAeH,KAAK,CAACI,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeL,KAAK,CAACM,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeP,KAAK,CAACQ,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeT,KAAK,CAACU,QAAQ,KAAK;EACtCC,GAAG,EAAE,eAAeX,KAAK,CAACY,SAAS;AACrC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGb,KAAK,IAAI;EACnC,MAAMc,cAAc,GAAGd,KAAK;EAC5B,MAAMe,cAAc,GAAG,EAAE,CAACC,MAAM,CAAClB,eAAe,CAAC,CAACmB,OAAO,CAAC,CAAC;EAC3DF,cAAc,CAACG,OAAO,CAAC,CAACC,UAAU,EAAEC,CAAC,KAAK;IACxC,MAAMC,eAAe,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IAChD,MAAMC,SAAS,GAAG,SAASF,eAAe,KAAK;IAC/C,MAAMG,MAAM,GAAG,SAASH,eAAe,EAAE;IACzC,IAAI,EAAEP,cAAc,CAACS,SAAS,CAAC,IAAIT,cAAc,CAACU,MAAM,CAAC,CAAC,EAAE;MAC1D,MAAM,IAAIC,KAAK,CAAC,GAAGF,SAAS,KAAKC,MAAM,cAAcV,cAAc,CAACS,SAAS,CAAC,KAAKT,cAAc,CAACU,MAAM,CAAC,GAAG,CAAC;IAC/G;IACA,IAAIJ,CAAC,GAAGL,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMC,SAAS,GAAG,SAASN,eAAe,KAAK;MAC/C,IAAI,EAAEP,cAAc,CAACU,MAAM,CAAC,IAAIV,cAAc,CAACa,SAAS,CAAC,CAAC,EAAE;QAC1D,MAAM,IAAIF,KAAK,CAAC,GAAGD,MAAM,KAAKG,SAAS,cAAcb,cAAc,CAACU,MAAM,CAAC,KAAKV,cAAc,CAACa,SAAS,CAAC,GAAG,CAAC;MAC/G;MACA,MAAMC,sBAAsB,GAAGb,cAAc,CAACK,CAAC,GAAG,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MAClE,MAAMO,aAAa,GAAG,SAASD,sBAAsB,KAAK;MAC1D,IAAI,EAAEd,cAAc,CAACa,SAAS,CAAC,IAAIb,cAAc,CAACe,aAAa,CAAC,CAAC,EAAE;QACjE,MAAM,IAAIJ,KAAK,CAAC,GAAGE,SAAS,KAAKE,aAAa,cAAcf,cAAc,CAACa,SAAS,CAAC,KAAKb,cAAc,CAACe,aAAa,CAAC,GAAG,CAAC;MAC7H;IACF;EACF,CAAC,CAAC;EACF,OAAO7B,KAAK;AACd,CAAC;AACD,OAAO,MAAM8B,WAAW,GAAGA,CAACC,OAAO,EAAEC,WAAW,KAAK;EACnD,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EACA,KAAK,MAAMb,UAAU,IAAIrB,eAAe,EAAE;IACxC,IAAIiC,OAAO,CAACZ,UAAU,CAAC,IAAI,CAACa,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACb,UAAU,CAAC,MAAMc,SAAS,EAAE;MAC5H,OAAOD,WAAW,CAACb,UAAU,CAAC;IAChC;EACF;AACF,CAAC;AACD,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;EAClC,MAAM,GAAGlC,KAAK,CAAC,GAAGL,QAAQ,CAAC,CAAC;EAC5B,MAAMwC,aAAa,GAAGpC,gBAAgB,CAACc,mBAAmB,CAACb,KAAK,CAAC,CAAC;EAClE;EACA,OAAON,KAAK,CAAC0C,OAAO,CAAC,MAAM;IACzB,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,IAAIR,OAAO,GAAG,CAAC,CAAC;IAChB,OAAO;MACLI,aAAa;MACbK,aAAa,EAAE,CAAC,CAAC;MACjBC,QAAQA,CAACC,QAAQ,EAAE;QACjBX,OAAO,GAAGW,QAAQ;QAClBL,WAAW,CAACnB,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACZ,OAAO,CAAC,CAAC;QAC1C,OAAOM,WAAW,CAACO,IAAI,IAAI,CAAC;MAC9B,CAAC;MACDC,SAASA,CAACF,IAAI,EAAE;QACd,IAAI,CAACN,WAAW,CAACO,IAAI,EAAE;UACrB,IAAI,CAACE,QAAQ,CAAC,CAAC;QACjB;QACAP,MAAM,IAAI,CAAC;QACXF,WAAW,CAACU,GAAG,CAACR,MAAM,EAAEI,IAAI,CAAC;QAC7BA,IAAI,CAACZ,OAAO,CAAC;QACb,OAAOQ,MAAM;MACf,CAAC;MACDS,WAAWA,CAACC,UAAU,EAAE;QACtBZ,WAAW,CAACa,MAAM,CAACD,UAAU,CAAC;QAC9B,IAAI,CAACZ,WAAW,CAACO,IAAI,EAAE;UACrB,IAAI,CAACO,UAAU,CAAC,CAAC;QACnB;MACF,CAAC;MACDL,QAAQA,CAAA,EAAG;QACTM,MAAM,CAACC,OAAO,CAAClB,aAAa,CAAC,CAACjB,OAAO,CAAC,CAAC,CAACM,MAAM,EAAE8B,UAAU,CAAC,KAAK;UAC9D,MAAMC,QAAQ,GAAGA,CAAC;YAChBC;UACF,CAAC,KAAK;YACJ,IAAI,CAACf,QAAQ,CAACW,MAAM,CAACK,MAAM,CAACL,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,EAAE1B,OAAO,CAAC,EAAE;cACtD,CAACP,MAAM,GAAGgC;YACZ,CAAC,CAAC,CAAC;UACL,CAAC;UACD,MAAME,GAAG,GAAGC,MAAM,CAACC,UAAU,CAACN,UAAU,CAAC;UACzC1D,qBAAqB,CAAC8D,GAAG,EAAEH,QAAQ,CAAC;UACpC,IAAI,CAACf,aAAa,CAACc,UAAU,CAAC,GAAG;YAC/BI,GAAG;YACHH;UACF,CAAC;UACDA,QAAQ,CAACG,GAAG,CAAC;QACf,CAAC,CAAC;MACJ,CAAC;MACDP,UAAUA,CAAA,EAAG;QACXC,MAAM,CAACS,MAAM,CAAC1B,aAAa,CAAC,CAACjB,OAAO,CAACoC,UAAU,IAAI;UACjD,MAAMQ,OAAO,GAAG,IAAI,CAACtB,aAAa,CAACc,UAAU,CAAC;UAC9CzD,wBAAwB,CAACiE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACJ,GAAG,EAAEI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACP,QAAQ,CAAC;QAC7J,CAAC,CAAC;QACFlB,WAAW,CAAC0B,KAAK,CAAC,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAAC/D,KAAK,CAAC,CAAC;AACb,CAAC;AACD,eAAekC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}