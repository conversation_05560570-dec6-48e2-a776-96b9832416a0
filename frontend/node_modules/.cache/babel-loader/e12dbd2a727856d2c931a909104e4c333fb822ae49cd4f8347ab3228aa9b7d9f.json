{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\n// ZombieJ: We export single file here since\n// ConfigProvider use this which will make loop deps\n// to import whole `rc-field-form`\nexport default /*#__PURE__*/createContext(undefined);", "map": {"version": 3, "names": ["createContext", "undefined"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/form/validateMessagesContext.js"], "sourcesContent": ["\"use client\";\n\nimport { createContext } from 'react';\n// ZombieJ: We export single file here since\n// ConfigProvider use this which will make loop deps\n// to import whole `rc-field-form`\nexport default /*#__PURE__*/createContext(undefined);"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAa,QAAQ,OAAO;AACrC;AACA;AACA;AACA,eAAe,aAAaA,aAAa,CAACC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}