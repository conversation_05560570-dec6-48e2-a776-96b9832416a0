{"ast": null, "code": "export const defaultPresetColors = {\n  blue: '#1677FF',\n  purple: '#722ED1',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  magenta: '#EB2F96',\n  /**\n   * @deprecated Use magenta instead\n   */\n  pink: '#EB2F96',\n  red: '#F5222D',\n  orange: '#FA8C16',\n  yellow: '#FADB14',\n  volcano: '#FA541C',\n  geekblue: '#2F54EB',\n  gold: '#FAAD14',\n  lime: '#A0D911'\n};\nconst seedToken = Object.assign(Object.assign({}, defaultPresetColors), {\n  // Color\n  colorPrimary: '#1677ff',\n  colorSuccess: '#52c41a',\n  colorWarning: '#faad14',\n  colorError: '#ff4d4f',\n  colorInfo: '#1677ff',\n  colorLink: '',\n  colorTextBase: '',\n  colorBgBase: '',\n  // Font\n  fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'`,\n  fontFamilyCode: `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`,\n  fontSize: 14,\n  // Line\n  lineWidth: 1,\n  lineType: 'solid',\n  // Motion\n  motionUnit: 0.1,\n  motionBase: 0,\n  motionEaseOutCirc: 'cubic-bezier(0.08, 0.82, 0.17, 1)',\n  motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',\n  motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n  motionEaseOutBack: 'cubic-bezier(0.12, 0.4, 0.29, 1.46)',\n  motionEaseInBack: 'cubic-bezier(0.71, -0.46, 0.88, 0.6)',\n  motionEaseInQuint: 'cubic-bezier(0.755, 0.05, 0.855, 0.06)',\n  motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',\n  // Radius\n  borderRadius: 6,\n  // Size\n  sizeUnit: 4,\n  sizeStep: 4,\n  sizePopupArrow: 16,\n  // Control Base\n  controlHeight: 32,\n  // zIndex\n  zIndexBase: 0,\n  zIndexPopupBase: 1000,\n  // Image\n  opacityImage: 1,\n  // Wireframe\n  wireframe: false,\n  // Motion\n  motion: true\n});\nexport default seedToken;", "map": {"version": 3, "names": ["defaultPresetColors", "blue", "purple", "cyan", "green", "magenta", "pink", "red", "orange", "yellow", "volcano", "geekblue", "gold", "lime", "seedToken", "Object", "assign", "colorPrimary", "colorSuccess", "colorWarning", "colorError", "colorInfo", "colorLink", "colorTextBase", "colorBgBase", "fontFamily", "fontFamilyCode", "fontSize", "lineWidth", "lineType", "motionUnit", "motionBase", "motionEaseOutCirc", "motionEaseInOutCirc", "motionEaseOut", "motionEaseInOut", "motionEaseOutBack", "motionEaseInBack", "motionEaseInQuint", "motionEaseOutQuint", "borderRadius", "sizeUnit", "sizeStep", "sizePopupArrow", "controlHeight", "zIndexBase", "zIndexPopupBase", "opacityImage", "wireframe", "motion"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/theme/themes/seed.js"], "sourcesContent": ["export const defaultPresetColors = {\n  blue: '#1677FF',\n  purple: '#722ED1',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  magenta: '#EB2F96',\n  /**\n   * @deprecated Use magenta instead\n   */\n  pink: '#EB2F96',\n  red: '#F5222D',\n  orange: '#FA8C16',\n  yellow: '#FADB14',\n  volcano: '#FA541C',\n  geekblue: '#2F54EB',\n  gold: '#FAAD14',\n  lime: '#A0D911'\n};\nconst seedToken = Object.assign(Object.assign({}, defaultPresetColors), {\n  // Color\n  colorPrimary: '#1677ff',\n  colorSuccess: '#52c41a',\n  colorWarning: '#faad14',\n  colorError: '#ff4d4f',\n  colorInfo: '#1677ff',\n  colorLink: '',\n  colorTextBase: '',\n  colorBgBase: '',\n  // Font\n  fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'`,\n  fontFamilyCode: `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`,\n  fontSize: 14,\n  // Line\n  lineWidth: 1,\n  lineType: 'solid',\n  // Motion\n  motionUnit: 0.1,\n  motionBase: 0,\n  motionEaseOutCirc: 'cubic-bezier(0.08, 0.82, 0.17, 1)',\n  motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',\n  motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n  motionEaseOutBack: 'cubic-bezier(0.12, 0.4, 0.29, 1.46)',\n  motionEaseInBack: 'cubic-bezier(0.71, -0.46, 0.88, 0.6)',\n  motionEaseInQuint: 'cubic-bezier(0.755, 0.05, 0.855, 0.06)',\n  motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',\n  // Radius\n  borderRadius: 6,\n  // Size\n  sizeUnit: 4,\n  sizeStep: 4,\n  sizePopupArrow: 16,\n  // Control Base\n  controlHeight: 32,\n  // zIndex\n  zIndexBase: 0,\n  zIndexPopupBase: 1000,\n  // Image\n  opacityImage: 1,\n  // Wireframe\n  wireframe: false,\n  // Motion\n  motion: true\n});\nexport default seedToken;"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG;EACjCC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE,SAAS;EAClB;AACF;AACA;EACEC,IAAI,EAAE,SAAS;EACfC,GAAG,EAAE,SAAS;EACdC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,mBAAmB,CAAC,EAAE;EACtE;EACAiB,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE,SAAS;EACvBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,EAAE;EACbC,aAAa,EAAE,EAAE;EACjBC,WAAW,EAAE,EAAE;EACf;EACAC,UAAU,EAAE;AACd;AACA,mBAAmB;EACjBC,cAAc,EAAE,0EAA0E;EAC1FC,QAAQ,EAAE,EAAE;EACZ;EACAC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,OAAO;EACjB;EACAC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,CAAC;EACbC,iBAAiB,EAAE,mCAAmC;EACtDC,mBAAmB,EAAE,sCAAsC;EAC3DC,aAAa,EAAE,qCAAqC;EACpDC,eAAe,EAAE,sCAAsC;EACvDC,iBAAiB,EAAE,qCAAqC;EACxDC,gBAAgB,EAAE,sCAAsC;EACxDC,iBAAiB,EAAE,wCAAwC;EAC3DC,kBAAkB,EAAE,gCAAgC;EACpD;EACAC,YAAY,EAAE,CAAC;EACf;EACAC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,CAAC;EACXC,cAAc,EAAE,EAAE;EAClB;EACAC,aAAa,EAAE,EAAE;EACjB;EACAC,UAAU,EAAE,CAAC;EACbC,eAAe,EAAE,IAAI;EACrB;EACAC,YAAY,EAAE,CAAC;EACf;EACAC,SAAS,EAAE,KAAK;EAChB;EACAC,MAAM,EAAE;AACV,CAAC,CAAC;AACF,eAAenC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}