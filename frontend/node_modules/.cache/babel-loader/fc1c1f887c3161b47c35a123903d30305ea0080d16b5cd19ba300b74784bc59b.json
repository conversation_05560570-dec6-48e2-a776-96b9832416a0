{"ast": null, "code": "\"use client\";\n\nimport InternalAnchor from './Anchor';\nimport AnchorLink from './AnchorLink';\nconst Anchor = InternalAnchor;\nAnchor.Link = AnchorLink;\nexport default Anchor;", "map": {"version": 3, "names": ["InternalAnchor", "AnchorLink", "<PERSON><PERSON>", "Link"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/anchor/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalAnchor from './Anchor';\nimport AnchorLink from './AnchorLink';\nconst Anchor = InternalAnchor;\nAnchor.Link = AnchorLink;\nexport default Anchor;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,cAAc,MAAM,UAAU;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,MAAM,GAAGF,cAAc;AAC7BE,MAAM,CAACC,IAAI,GAAGF,UAAU;AACxB,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}