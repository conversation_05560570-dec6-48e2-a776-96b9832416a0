{"ast": null, "code": "import * as React from 'react';\nimport { groupKeysMap } from '../../_util/transKeys';\nconst useData = (dataSource, rowKey, targetKeys) => {\n  const mergedDataSource = React.useMemo(() => (dataSource || []).map(record => {\n    if (rowKey) {\n      return Object.assign(Object.assign({}, record), {\n        key: rowKey(record)\n      });\n    }\n    return record;\n  }), [dataSource, rowKey]);\n  const [leftDataSource, rightDataSource] = React.useMemo(() => {\n    var _a;\n    const leftData = [];\n    const rightData = Array.from({\n      length: (_a = targetKeys === null || targetKeys === void 0 ? void 0 : targetKeys.length) !== null && _a !== void 0 ? _a : 0\n    });\n    const targetKeysMap = groupKeysMap(targetKeys || []);\n    mergedDataSource.forEach(record => {\n      // rightData should be ordered by targetKeys\n      // leftData should be ordered by dataSource\n      if (targetKeysMap.has(record.key)) {\n        const idx = targetKeysMap.get(record.key);\n        rightData[idx] = record;\n      } else {\n        leftData.push(record);\n      }\n    });\n    return [leftData, rightData];\n  }, [mergedDataSource, targetKeys]);\n  return [mergedDataSource, leftDataSource.filter(Boolean), rightDataSource.filter(Boolean)];\n};\nexport default useData;", "map": {"version": 3, "names": ["React", "groupKeysMap", "useData", "dataSource", "<PERSON><PERSON><PERSON>", "targetKeys", "mergedDataSource", "useMemo", "map", "record", "Object", "assign", "key", "leftDataSource", "rightDataSource", "_a", "leftData", "rightData", "Array", "from", "length", "targetKeysMap", "for<PERSON>ach", "has", "idx", "get", "push", "filter", "Boolean"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/transfer/hooks/useData.js"], "sourcesContent": ["import * as React from 'react';\nimport { groupKeysMap } from '../../_util/transKeys';\nconst useData = (dataSource, rowKey, targetKeys) => {\n  const mergedDataSource = React.useMemo(() => (dataSource || []).map(record => {\n    if (rowKey) {\n      return Object.assign(Object.assign({}, record), {\n        key: rowKey(record)\n      });\n    }\n    return record;\n  }), [dataSource, rowKey]);\n  const [leftDataSource, rightDataSource] = React.useMemo(() => {\n    var _a;\n    const leftData = [];\n    const rightData = Array.from({\n      length: (_a = targetKeys === null || targetKeys === void 0 ? void 0 : targetKeys.length) !== null && _a !== void 0 ? _a : 0\n    });\n    const targetKeysMap = groupKeysMap(targetKeys || []);\n    mergedDataSource.forEach(record => {\n      // rightData should be ordered by targetKeys\n      // leftData should be ordered by dataSource\n      if (targetKeysMap.has(record.key)) {\n        const idx = targetKeysMap.get(record.key);\n        rightData[idx] = record;\n      } else {\n        leftData.push(record);\n      }\n    });\n    return [leftData, rightData];\n  }, [mergedDataSource, targetKeys]);\n  return [mergedDataSource, leftDataSource.filter(Boolean), rightDataSource.filter(Boolean)];\n};\nexport default useData;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,uBAAuB;AACpD,MAAMC,OAAO,GAAGA,CAACC,UAAU,EAAEC,MAAM,EAAEC,UAAU,KAAK;EAClD,MAAMC,gBAAgB,GAAGN,KAAK,CAACO,OAAO,CAAC,MAAM,CAACJ,UAAU,IAAI,EAAE,EAAEK,GAAG,CAACC,MAAM,IAAI;IAC5E,IAAIL,MAAM,EAAE;MACV,OAAOM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAE;QAC9CG,GAAG,EAAER,MAAM,CAACK,MAAM;MACpB,CAAC,CAAC;IACJ;IACA,OAAOA,MAAM;EACf,CAAC,CAAC,EAAE,CAACN,UAAU,EAAEC,MAAM,CAAC,CAAC;EACzB,MAAM,CAACS,cAAc,EAAEC,eAAe,CAAC,GAAGd,KAAK,CAACO,OAAO,CAAC,MAAM;IAC5D,IAAIQ,EAAE;IACN,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAC3BC,MAAM,EAAE,CAACL,EAAE,GAAGV,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACe,MAAM,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;IAC5H,CAAC,CAAC;IACF,MAAMM,aAAa,GAAGpB,YAAY,CAACI,UAAU,IAAI,EAAE,CAAC;IACpDC,gBAAgB,CAACgB,OAAO,CAACb,MAAM,IAAI;MACjC;MACA;MACA,IAAIY,aAAa,CAACE,GAAG,CAACd,MAAM,CAACG,GAAG,CAAC,EAAE;QACjC,MAAMY,GAAG,GAAGH,aAAa,CAACI,GAAG,CAAChB,MAAM,CAACG,GAAG,CAAC;QACzCK,SAAS,CAACO,GAAG,CAAC,GAAGf,MAAM;MACzB,CAAC,MAAM;QACLO,QAAQ,CAACU,IAAI,CAACjB,MAAM,CAAC;MACvB;IACF,CAAC,CAAC;IACF,OAAO,CAACO,QAAQ,EAAEC,SAAS,CAAC;EAC9B,CAAC,EAAE,CAACX,gBAAgB,EAAED,UAAU,CAAC,CAAC;EAClC,OAAO,CAACC,gBAAgB,EAAEO,cAAc,CAACc,MAAM,CAACC,OAAO,CAAC,EAAEd,eAAe,CAACa,MAAM,CAACC,OAAO,CAAC,CAAC;AAC5F,CAAC;AACD,eAAe1B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}