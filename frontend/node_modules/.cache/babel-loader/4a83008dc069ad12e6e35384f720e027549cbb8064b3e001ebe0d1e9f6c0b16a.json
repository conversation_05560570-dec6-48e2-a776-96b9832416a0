{"ast": null, "code": "export const groupKeysMap = keys => {\n  const map = new Map();\n  keys.forEach((key, index) => {\n    map.set(key, index);\n  });\n  return map;\n};\nexport const groupDisabledKeysMap = dataSource => {\n  const map = new Map();\n  dataSource.forEach(({\n    disabled,\n    key\n  }, index) => {\n    if (disabled) {\n      map.set(key, index);\n    }\n  });\n  return map;\n};", "map": {"version": 3, "names": ["groupKeysMap", "keys", "map", "Map", "for<PERSON>ach", "key", "index", "set", "groupDisabledKeysMap", "dataSource", "disabled"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/transKeys.js"], "sourcesContent": ["export const groupKeysMap = keys => {\n  const map = new Map();\n  keys.forEach((key, index) => {\n    map.set(key, index);\n  });\n  return map;\n};\nexport const groupDisabledKeysMap = dataSource => {\n  const map = new Map();\n  dataSource.forEach(({\n    disabled,\n    key\n  }, index) => {\n    if (disabled) {\n      map.set(key, index);\n    }\n  });\n  return map;\n};"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAGC,IAAI,IAAI;EAClC,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrBF,IAAI,CAACG,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3BJ,GAAG,CAACK,GAAG,CAACF,GAAG,EAAEC,KAAK,CAAC;EACrB,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ,CAAC;AACD,OAAO,MAAMM,oBAAoB,GAAGC,UAAU,IAAI;EAChD,MAAMP,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrBM,UAAU,CAACL,OAAO,CAAC,CAAC;IAClBM,QAAQ;IACRL;EACF,CAAC,EAAEC,KAAK,KAAK;IACX,IAAII,QAAQ,EAAE;MACZR,GAAG,CAACK,GAAG,CAACF,GAAG,EAAEC,KAAK,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}