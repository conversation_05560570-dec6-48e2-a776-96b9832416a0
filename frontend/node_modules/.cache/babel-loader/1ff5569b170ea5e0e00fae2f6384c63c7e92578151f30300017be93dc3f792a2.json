{"ast": null, "code": "export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nconst getScroll = target => {\n  var _a, _b;\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n  let result = 0;\n  if (isWindow(target)) {\n    result = target.pageYOffset;\n  } else if (target instanceof Document) {\n    result = target.documentElement.scrollTop;\n  } else if (target instanceof HTMLElement) {\n    result = target.scrollTop;\n  } else if (target) {\n    // According to the type inference, the `target` is `never` type.\n    // Since we configured the loose mode type checking, and supports mocking the target with such shape below::\n    //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,\n    //    the program may falls into this branch.\n    // Check the corresponding tests for details. Don't sure what is the real scenario this happens.\n    /* biome-ignore lint/complexity/useLiteralKeys: target is a never type */ /* eslint-disable-next-line dot-notation */\n    result = target['scrollTop'];\n  }\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_b = ((_a = target.ownerDocument) !== null && _a !== void 0 ? _a : target).documentElement) === null || _b === void 0 ? void 0 : _b.scrollTop;\n  }\n  return result;\n};\nexport default getScroll;", "map": {"version": 3, "names": ["isWindow", "obj", "undefined", "window", "getScroll", "target", "_a", "_b", "result", "pageYOffset", "Document", "documentElement", "scrollTop", "HTMLElement", "ownerDocument"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/getScroll.js"], "sourcesContent": ["export function isWindow(obj) {\n  return obj !== null && obj !== undefined && obj === obj.window;\n}\nconst getScroll = target => {\n  var _a, _b;\n  if (typeof window === 'undefined') {\n    return 0;\n  }\n  let result = 0;\n  if (isWindow(target)) {\n    result = target.pageYOffset;\n  } else if (target instanceof Document) {\n    result = target.documentElement.scrollTop;\n  } else if (target instanceof HTMLElement) {\n    result = target.scrollTop;\n  } else if (target) {\n    // According to the type inference, the `target` is `never` type.\n    // Since we configured the loose mode type checking, and supports mocking the target with such shape below::\n    //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,\n    //    the program may falls into this branch.\n    // Check the corresponding tests for details. Don't sure what is the real scenario this happens.\n    /* biome-ignore lint/complexity/useLiteralKeys: target is a never type */ /* eslint-disable-next-line dot-notation */\n    result = target['scrollTop'];\n  }\n  if (target && !isWindow(target) && typeof result !== 'number') {\n    result = (_b = ((_a = target.ownerDocument) !== null && _a !== void 0 ? _a : target).documentElement) === null || _b === void 0 ? void 0 : _b.scrollTop;\n  }\n  return result;\n};\nexport default getScroll;"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAKA,GAAG,CAACE,MAAM;AAChE;AACA,MAAMC,SAAS,GAAGC,MAAM,IAAI;EAC1B,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAI,OAAOJ,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,CAAC;EACV;EACA,IAAIK,MAAM,GAAG,CAAC;EACd,IAAIR,QAAQ,CAACK,MAAM,CAAC,EAAE;IACpBG,MAAM,GAAGH,MAAM,CAACI,WAAW;EAC7B,CAAC,MAAM,IAAIJ,MAAM,YAAYK,QAAQ,EAAE;IACrCF,MAAM,GAAGH,MAAM,CAACM,eAAe,CAACC,SAAS;EAC3C,CAAC,MAAM,IAAIP,MAAM,YAAYQ,WAAW,EAAE;IACxCL,MAAM,GAAGH,MAAM,CAACO,SAAS;EAC3B,CAAC,MAAM,IAAIP,MAAM,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA,0EAA0E;IAC1EG,MAAM,GAAGH,MAAM,CAAC,WAAW,CAAC;EAC9B;EACA,IAAIA,MAAM,IAAI,CAACL,QAAQ,CAACK,MAAM,CAAC,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;IAC7DA,MAAM,GAAG,CAACD,EAAE,GAAG,CAAC,CAACD,EAAE,GAAGD,MAAM,CAACS,aAAa,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,MAAM,EAAEM,eAAe,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,SAAS;EACzJ;EACA,OAAOJ,MAAM;AACf,CAAC;AACD,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}