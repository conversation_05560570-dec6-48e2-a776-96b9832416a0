{"ast": null, "code": "\"use client\";\n\nimport Countdown from './Countdown';\nimport Statistic from './Statistic';\nimport Timer from './Timer';\nStatistic.Timer = Timer;\nStatistic.Countdown = Countdown;\nexport default Statistic;", "map": {"version": 3, "names": ["Countdown", "Statistic", "Timer"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/statistic/index.js"], "sourcesContent": ["\"use client\";\n\nimport Countdown from './Countdown';\nimport Statistic from './Statistic';\nimport Timer from './Timer';\nStatistic.Timer = Timer;\nStatistic.Countdown = Countdown;\nexport default Statistic;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3BD,SAAS,CAACC,KAAK,GAAGA,KAAK;AACvBD,SAAS,CAACD,SAAS,GAAGA,SAAS;AAC/B,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}