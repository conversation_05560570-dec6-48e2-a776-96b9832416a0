{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport raf from \"rc-util/es/raf\";\nfunction throttleByAnimationFrame(fn) {\n  let requestId;\n  const later = args => () => {\n    requestId = null;\n    fn.apply(void 0, _toConsumableArray(args));\n  };\n  const throttled = (...args) => {\n    if (requestId == null) {\n      requestId = raf(later(args));\n    }\n  };\n  throttled.cancel = () => {\n    raf.cancel(requestId);\n    requestId = null;\n  };\n  return throttled;\n}\nexport default throttleByAnimationFrame;", "map": {"version": 3, "names": ["_toConsumableArray", "raf", "throttleByAnimationFrame", "fn", "requestId", "later", "args", "apply", "throttled", "cancel"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/throttleByAnimationFrame.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport raf from \"rc-util/es/raf\";\nfunction throttleByAnimationFrame(fn) {\n  let requestId;\n  const later = args => () => {\n    requestId = null;\n    fn.apply(void 0, _toConsumableArray(args));\n  };\n  const throttled = (...args) => {\n    if (requestId == null) {\n      requestId = raf(later(args));\n    }\n  };\n  throttled.cancel = () => {\n    raf.cancel(requestId);\n    requestId = null;\n  };\n  return throttled;\n}\nexport default throttleByAnimationFrame;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,wBAAwBA,CAACC,EAAE,EAAE;EACpC,IAAIC,SAAS;EACb,MAAMC,KAAK,GAAGC,IAAI,IAAI,MAAM;IAC1BF,SAAS,GAAG,IAAI;IAChBD,EAAE,CAACI,KAAK,CAAC,KAAK,CAAC,EAAEP,kBAAkB,CAACM,IAAI,CAAC,CAAC;EAC5C,CAAC;EACD,MAAME,SAAS,GAAGA,CAAC,GAAGF,IAAI,KAAK;IAC7B,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAGH,GAAG,CAACI,KAAK,CAACC,IAAI,CAAC,CAAC;IAC9B;EACF,CAAC;EACDE,SAAS,CAACC,MAAM,GAAG,MAAM;IACvBR,GAAG,CAACQ,MAAM,CAACL,SAAS,CAAC;IACrBA,SAAS,GAAG,IAAI;EAClB,CAAC;EACD,OAAOI,SAAS;AAClB;AACA,eAAeN,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}