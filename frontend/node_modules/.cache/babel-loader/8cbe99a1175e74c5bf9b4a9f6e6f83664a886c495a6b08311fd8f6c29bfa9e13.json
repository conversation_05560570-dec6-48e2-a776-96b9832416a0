{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst DisabledContext = /*#__PURE__*/React.createContext(false);\nexport const DisabledContextProvider = ({\n  children,\n  disabled\n}) => {\n  const originDisabled = React.useContext(DisabledContext);\n  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {\n    value: disabled !== null && disabled !== void 0 ? disabled : originDisabled\n  }, children);\n};\nexport default DisabledContext;", "map": {"version": 3, "names": ["React", "DisabledContext", "createContext", "DisabledContextProvider", "children", "disabled", "originDisabled", "useContext", "createElement", "Provider", "value"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/config-provider/DisabledContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst DisabledContext = /*#__PURE__*/React.createContext(false);\nexport const DisabledContextProvider = ({\n  children,\n  disabled\n}) => {\n  const originDisabled = React.useContext(DisabledContext);\n  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {\n    value: disabled !== null && disabled !== void 0 ? disabled : originDisabled\n  }, children);\n};\nexport default DisabledContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAC/D,OAAO,MAAMC,uBAAuB,GAAGA,CAAC;EACtCC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAGN,KAAK,CAACO,UAAU,CAACN,eAAe,CAAC;EACxD,OAAO,aAAaD,KAAK,CAACQ,aAAa,CAACP,eAAe,CAACQ,QAAQ,EAAE;IAChEC,KAAK,EAAEL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGC;EAC/D,CAAC,EAAEF,QAAQ,CAAC;AACd,CAAC;AACD,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}