{"ast": null, "code": "\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nexport const InternalPanel = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    prefixCls,\n    className,\n    children,\n    size,\n    style = {}\n  } = props;\n  const panelClassName = classNames(`${prefixCls}-panel`, {\n    [`${prefixCls}-panel-hidden`]: size === 0\n  }, className);\n  const hasSize = size !== undefined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: panelClassName,\n    style: Object.assign(Object.assign({}, style), {\n      // Use auto when start from ssr\n      flexBasis: hasSize ? size : 'auto',\n      flexGrow: hasSize ? 0 : 1\n    })\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  InternalPanel.displayName = 'Panel';\n}\nconst Panel = () => null;\nexport default Panel;", "map": {"version": 3, "names": ["React", "forwardRef", "classNames", "InternalPanel", "props", "ref", "prefixCls", "className", "children", "size", "style", "panelClassName", "hasSize", "undefined", "createElement", "Object", "assign", "flexBasis", "flexGrow", "process", "env", "NODE_ENV", "displayName", "Panel"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/splitter/Panel.js"], "sourcesContent": ["\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nexport const InternalPanel = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    prefixCls,\n    className,\n    children,\n    size,\n    style = {}\n  } = props;\n  const panelClassName = classNames(`${prefixCls}-panel`, {\n    [`${prefixCls}-panel-hidden`]: size === 0\n  }, className);\n  const hasSize = size !== undefined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: panelClassName,\n    style: Object.assign(Object.assign({}, style), {\n      // Use auto when start from ssr\n      flexBasis: hasSize ? size : 'auto',\n      flexGrow: hasSize ? 0 : 1\n    })\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  InternalPanel.displayName = 'Panel';\n}\nconst Panel = () => null;\nexport default Panel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,MAAMC,aAAa,GAAG,aAAaF,UAAU,CAAC,CAACG,KAAK,EAAEC,GAAG,KAAK;EACnE,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,IAAI;IACJC,KAAK,GAAG,CAAC;EACX,CAAC,GAAGN,KAAK;EACT,MAAMO,cAAc,GAAGT,UAAU,CAAC,GAAGI,SAAS,QAAQ,EAAE;IACtD,CAAC,GAAGA,SAAS,eAAe,GAAGG,IAAI,KAAK;EAC1C,CAAC,EAAEF,SAAS,CAAC;EACb,MAAMK,OAAO,GAAGH,IAAI,KAAKI,SAAS;EAClC,OAAO,aAAab,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAC7CT,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEI,cAAc;IACzBD,KAAK,EAAEK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAE;MAC7C;MACAO,SAAS,EAAEL,OAAO,GAAGH,IAAI,GAAG,MAAM;MAClCS,QAAQ,EAAEN,OAAO,GAAG,CAAC,GAAG;IAC1B,CAAC;EACH,CAAC,EAAEJ,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClB,aAAa,CAACmB,WAAW,GAAG,OAAO;AACrC;AACA,MAAMC,KAAK,GAAGA,CAAA,KAAM,IAAI;AACxB,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}