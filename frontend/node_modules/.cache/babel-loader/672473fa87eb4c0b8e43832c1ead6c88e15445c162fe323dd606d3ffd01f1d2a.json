{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport SkeletonNode from './Node';\nimport Paragraph from './Paragraph';\nimport useStyle from './style';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && typeof prop === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  const basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nconst Skeleton = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    loading,\n    className,\n    rootClassName,\n    style,\n    children,\n    avatar = false,\n    title = true,\n    paragraph = true,\n    active,\n    round\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('skeleton');\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (loading || !('loading' in props)) {\n    const hasAvatar = !!avatar;\n    const hasTitle = !!title;\n    const hasParagraph = !!paragraph;\n    // Avatar\n    let avatarNode;\n    if (hasAvatar) {\n      const avatarProps = Object.assign(Object.assign({\n        prefixCls: `${prefixCls}-avatar`\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-header`\n      }, /*#__PURE__*/React.createElement(Element, Object.assign({}, avatarProps)));\n    }\n    let contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      let $title;\n      if (hasTitle) {\n        const titleProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-title`\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, Object.assign({}, titleProps));\n      }\n      // Paragraph\n      let paragraphNode;\n      if (hasParagraph) {\n        const paragraphProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-paragraph`\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, Object.assign({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-content`\n      }, $title, paragraphNode);\n    }\n    const cls = classNames(prefixCls, {\n      [`${prefixCls}-with-avatar`]: hasAvatar,\n      [`${prefixCls}-active`]: active,\n      [`${prefixCls}-rtl`]: direction === 'rtl',\n      [`${prefixCls}-round`]: round\n    }, contextClassName, className, rootClassName, hashId, cssVarCls);\n    return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: Object.assign(Object.assign({}, contextStyle), style)\n    }, avatarNode, contentNode));\n  }\n  return children !== null && children !== void 0 ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nif (process.env.NODE_ENV !== 'production') {\n  Skeleton.displayName = 'Skeleton';\n}\nexport default Skeleton;", "map": {"version": 3, "names": ["React", "classNames", "useComponentConfig", "SkeletonAvatar", "SkeletonButton", "Element", "SkeletonImage", "SkeletonInput", "SkeletonNode", "Paragraph", "useStyle", "Title", "getComponentProps", "prop", "getAvatarBasicProps", "hasTitle", "hasParagraph", "size", "shape", "getTitleBasicProps", "has<PERSON><PERSON><PERSON>", "width", "getParagraphBasicProps", "basicProps", "rows", "Skeleton", "props", "prefixCls", "customizePrefixCls", "loading", "className", "rootClassName", "style", "children", "avatar", "title", "paragraph", "active", "round", "getPrefixCls", "direction", "contextClassName", "contextStyle", "wrapCSSVar", "hashId", "cssVarCls", "avatarNode", "avatarProps", "Object", "assign", "createElement", "contentNode", "$title", "titleProps", "paragraphNode", "paragraphProps", "cls", "<PERSON><PERSON>", "Avatar", "Input", "Image", "Node", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/skeleton/Skeleton.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport SkeletonNode from './Node';\nimport Paragraph from './Paragraph';\nimport useStyle from './style';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && typeof prop === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  const basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nconst Skeleton = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    loading,\n    className,\n    rootClassName,\n    style,\n    children,\n    avatar = false,\n    title = true,\n    paragraph = true,\n    active,\n    round\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('skeleton');\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (loading || !('loading' in props)) {\n    const hasAvatar = !!avatar;\n    const hasTitle = !!title;\n    const hasParagraph = !!paragraph;\n    // Avatar\n    let avatarNode;\n    if (hasAvatar) {\n      const avatarProps = Object.assign(Object.assign({\n        prefixCls: `${prefixCls}-avatar`\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-header`\n      }, /*#__PURE__*/React.createElement(Element, Object.assign({}, avatarProps)));\n    }\n    let contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      let $title;\n      if (hasTitle) {\n        const titleProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-title`\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, Object.assign({}, titleProps));\n      }\n      // Paragraph\n      let paragraphNode;\n      if (hasParagraph) {\n        const paragraphProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-paragraph`\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, Object.assign({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-content`\n      }, $title, paragraphNode);\n    }\n    const cls = classNames(prefixCls, {\n      [`${prefixCls}-with-avatar`]: hasAvatar,\n      [`${prefixCls}-active`]: active,\n      [`${prefixCls}-rtl`]: direction === 'rtl',\n      [`${prefixCls}-round`]: round\n    }, contextClassName, className, rootClassName, hashId, cssVarCls);\n    return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: Object.assign(Object.assign({}, contextStyle), style)\n    }, avatarNode, contentNode));\n  }\n  return children !== null && children !== void 0 ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nif (process.env.NODE_ENV !== 'production') {\n  Skeleton.displayName = 'Skeleton';\n}\nexport default Skeleton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,cAAc,MAAM,UAAU;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,YAAY,MAAM,QAAQ;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACpC,OAAOA,IAAI;EACb;EACA,OAAO,CAAC,CAAC;AACX;AACA,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EACnD,IAAID,QAAQ,IAAI,CAACC,YAAY,EAAE;IAC7B;IACA,OAAO;MACLC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC;EACH;EACA,OAAO;IACLD,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;EACT,CAAC;AACH;AACA,SAASC,kBAAkBA,CAACC,SAAS,EAAEJ,YAAY,EAAE;EACnD,IAAI,CAACI,SAAS,IAAIJ,YAAY,EAAE;IAC9B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAID,SAAS,IAAIJ,YAAY,EAAE;IAC7B,OAAO;MACLK,KAAK,EAAE;IACT,CAAC;EACH;EACA,OAAO,CAAC,CAAC;AACX;AACA,SAASC,sBAAsBA,CAACF,SAAS,EAAEL,QAAQ,EAAE;EACnD,MAAMQ,UAAU,GAAG,CAAC,CAAC;EACrB;EACA,IAAI,CAACH,SAAS,IAAI,CAACL,QAAQ,EAAE;IAC3BQ,UAAU,CAACF,KAAK,GAAG,KAAK;EAC1B;EACA;EACA,IAAI,CAACD,SAAS,IAAIL,QAAQ,EAAE;IAC1BQ,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB,CAAC,MAAM;IACLD,UAAU,CAACC,IAAI,GAAG,CAAC;EACrB;EACA,OAAOD,UAAU;AACnB;AACA,MAAME,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,OAAO;IACPC,SAAS;IACTC,aAAa;IACbC,KAAK;IACLC,QAAQ;IACRC,MAAM,GAAG,KAAK;IACdC,KAAK,GAAG,IAAI;IACZC,SAAS,GAAG,IAAI;IAChBC,MAAM;IACNC;EACF,CAAC,GAAGZ,KAAK;EACT,MAAM;IACJa,YAAY;IACZC,SAAS;IACTV,SAAS,EAAEW,gBAAgB;IAC3BT,KAAK,EAAEU;EACT,CAAC,GAAGxC,kBAAkB,CAAC,UAAU,CAAC;EAClC,MAAMyB,SAAS,GAAGY,YAAY,CAAC,UAAU,EAAEX,kBAAkB,CAAC;EAC9D,MAAM,CAACe,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAACiB,SAAS,CAAC;EAC3D,IAAIE,OAAO,IAAI,EAAE,SAAS,IAAIH,KAAK,CAAC,EAAE;IACpC,MAAMN,SAAS,GAAG,CAAC,CAACc,MAAM;IAC1B,MAAMnB,QAAQ,GAAG,CAAC,CAACoB,KAAK;IACxB,MAAMnB,YAAY,GAAG,CAAC,CAACoB,SAAS;IAChC;IACA,IAAIU,UAAU;IACd,IAAI1B,SAAS,EAAE;MACb,MAAM2B,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAC9CtB,SAAS,EAAE,GAAGA,SAAS;MACzB,CAAC,EAAEb,mBAAmB,CAACC,QAAQ,EAAEC,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACsB,MAAM,CAAC,CAAC;MAC3E;MACAY,UAAU,GAAG,aAAa9C,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;QACnDpB,SAAS,EAAE,GAAGH,SAAS;MACzB,CAAC,EAAE,aAAa3B,KAAK,CAACkD,aAAa,CAAC7C,OAAO,EAAE2C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,WAAW,CAAC,CAAC,CAAC;IAC/E;IACA,IAAII,WAAW;IACf,IAAIpC,QAAQ,IAAIC,YAAY,EAAE;MAC5B;MACA,IAAIoC,MAAM;MACV,IAAIrC,QAAQ,EAAE;QACZ,MAAMsC,UAAU,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;UAC7CtB,SAAS,EAAE,GAAGA,SAAS;QACzB,CAAC,EAAER,kBAAkB,CAACC,SAAS,EAAEJ,YAAY,CAAC,CAAC,EAAEJ,iBAAiB,CAACuB,KAAK,CAAC,CAAC;QAC1EiB,MAAM,GAAG,aAAapD,KAAK,CAACkD,aAAa,CAACvC,KAAK,EAAEqC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC;MACjF;MACA;MACA,IAAIC,aAAa;MACjB,IAAItC,YAAY,EAAE;QAChB,MAAMuC,cAAc,GAAGP,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;UACjDtB,SAAS,EAAE,GAAGA,SAAS;QACzB,CAAC,EAAEL,sBAAsB,CAACF,SAAS,EAAEL,QAAQ,CAAC,CAAC,EAAEH,iBAAiB,CAACwB,SAAS,CAAC,CAAC;QAC9EkB,aAAa,GAAG,aAAatD,KAAK,CAACkD,aAAa,CAACzC,SAAS,EAAEuC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,cAAc,CAAC,CAAC;MAChG;MACAJ,WAAW,GAAG,aAAanD,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;QACpDpB,SAAS,EAAE,GAAGH,SAAS;MACzB,CAAC,EAAEyB,MAAM,EAAEE,aAAa,CAAC;IAC3B;IACA,MAAME,GAAG,GAAGvD,UAAU,CAAC0B,SAAS,EAAE;MAChC,CAAC,GAAGA,SAAS,cAAc,GAAGP,SAAS;MACvC,CAAC,GAAGO,SAAS,SAAS,GAAGU,MAAM;MAC/B,CAAC,GAAGV,SAAS,MAAM,GAAGa,SAAS,KAAK,KAAK;MACzC,CAAC,GAAGb,SAAS,QAAQ,GAAGW;IAC1B,CAAC,EAAEG,gBAAgB,EAAEX,SAAS,EAAEC,aAAa,EAAEa,MAAM,EAAEC,SAAS,CAAC;IACjE,OAAOF,UAAU,CAAC,aAAa3C,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;MACxDpB,SAAS,EAAE0B,GAAG;MACdxB,KAAK,EAAEgB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,YAAY,CAAC,EAAEV,KAAK;IAC7D,CAAC,EAAEc,UAAU,EAAEK,WAAW,CAAC,CAAC;EAC9B;EACA,OAAOlB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,IAAI;AACnE,CAAC;AACDR,QAAQ,CAACgC,MAAM,GAAGrD,cAAc;AAChCqB,QAAQ,CAACiC,MAAM,GAAGvD,cAAc;AAChCsB,QAAQ,CAACkC,KAAK,GAAGpD,aAAa;AAC9BkB,QAAQ,CAACmC,KAAK,GAAGtD,aAAa;AAC9BmB,QAAQ,CAACoC,IAAI,GAAGrD,YAAY;AAC5B,IAAIsD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCvC,QAAQ,CAACwC,WAAW,GAAG,UAAU;AACnC;AACA,eAAexC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}