{"ast": null, "code": "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;", "map": {"version": 3, "names": ["InternalAlert", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/alert/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,SAAS;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,KAAK,GAAGF,aAAa;AAC3BE,KAAK,CAACD,aAAa,GAAGA,aAAa;AACnC,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}