{"ast": null, "code": "\"use client\";\n\nimport Timeline from './Timeline';\nexport default Timeline;", "map": {"version": 3, "names": ["Timeline"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/timeline/index.js"], "sourcesContent": ["\"use client\";\n\nimport Timeline from './Timeline';\nexport default Timeline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}