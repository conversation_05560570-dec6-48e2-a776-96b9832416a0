{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genQRCodeStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    colorSplit\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: token.paddingSM,\n      backgroundColor: token.colorWhite,\n      borderRadius: token.borderRadiusLG,\n      border: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      position: 'relative',\n      overflow: 'hidden',\n      [`& > ${componentCls}-mask`]: {\n        position: 'absolute',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        zIndex: 10,\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        width: '100%',\n        height: '100%',\n        color: token.colorText,\n        lineHeight: token.lineHeight,\n        background: token.QRCodeMaskBackgroundColor,\n        textAlign: 'center',\n        [`& > ${componentCls}-expired, & > ${componentCls}-scanned`]: {\n          color: token.QRCodeTextColor\n        }\n      },\n      '> canvas': {\n        alignSelf: 'stretch',\n        flex: 'auto',\n        minWidth: 0\n      },\n      '&-icon': {\n        marginBlockEnd: token.marginXS,\n        fontSize: token.controlHeight\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      padding: 0,\n      borderRadius: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  QRCodeMaskBackgroundColor: new FastColor(token.colorBgContainer).setA(0.96).toRgbString()\n});\nexport default genStyleHooks('QRCode', token => {\n  const mergedToken = mergeToken(token, {\n    QRCodeTextColor: token.colorText\n  });\n  return genQRCodeStyle(mergedToken);\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "FastColor", "resetComponent", "genStyleHooks", "mergeToken", "genQRCodeStyle", "token", "componentCls", "lineWidth", "lineType", "colorSplit", "Object", "assign", "display", "justifyContent", "alignItems", "padding", "paddingSM", "backgroundColor", "colorWhite", "borderRadius", "borderRadiusLG", "border", "position", "overflow", "insetBlockStart", "insetInlineStart", "zIndex", "flexDirection", "width", "height", "color", "colorText", "lineHeight", "background", "QRCodeMaskBackgroundColor", "textAlign", "QRCodeTextColor", "alignSelf", "flex", "min<PERSON><PERSON><PERSON>", "marginBlockEnd", "marginXS", "fontSize", "controlHeight", "borderColor", "prepareComponentToken", "colorBgContainer", "setA", "toRgbString", "mergedToken"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/qr-code/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genQRCodeStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    colorSplit\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: token.paddingSM,\n      backgroundColor: token.colorWhite,\n      borderRadius: token.borderRadiusLG,\n      border: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      position: 'relative',\n      overflow: 'hidden',\n      [`& > ${componentCls}-mask`]: {\n        position: 'absolute',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        zIndex: 10,\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        width: '100%',\n        height: '100%',\n        color: token.colorText,\n        lineHeight: token.lineHeight,\n        background: token.QRCodeMaskBackgroundColor,\n        textAlign: 'center',\n        [`& > ${componentCls}-expired, & > ${componentCls}-scanned`]: {\n          color: token.QRCodeTextColor\n        }\n      },\n      '> canvas': {\n        alignSelf: 'stretch',\n        flex: 'auto',\n        minWidth: 0\n      },\n      '&-icon': {\n        marginBlockEnd: token.marginXS,\n        fontSize: token.controlHeight\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      padding: 0,\n      borderRadius: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  QRCodeMaskBackgroundColor: new FastColor(token.colorBgContainer).setA(0.96).toRgbString()\n});\nexport default genStyleHooks('QRCode', token => {\n  const mergedToken = mergeToken(token, {\n    QRCodeTextColor: token.colorText\n  });\n  return genQRCodeStyle(mergedToken);\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtEO,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAEV,KAAK,CAACW,SAAS;MACxBC,eAAe,EAAEZ,KAAK,CAACa,UAAU;MACjCC,YAAY,EAAEd,KAAK,CAACe,cAAc;MAClCC,MAAM,EAAE,GAAGtB,IAAI,CAACQ,SAAS,CAAC,IAAIC,QAAQ,IAAIC,UAAU,EAAE;MACtDa,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,CAAC,OAAOjB,YAAY,OAAO,GAAG;QAC5BgB,QAAQ,EAAE,UAAU;QACpBE,eAAe,EAAE,CAAC;QAClBC,gBAAgB,EAAE,CAAC;QACnBC,MAAM,EAAE,EAAE;QACVd,OAAO,EAAE,MAAM;QACfe,aAAa,EAAE,QAAQ;QACvBd,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBc,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAEzB,KAAK,CAAC0B,SAAS;QACtBC,UAAU,EAAE3B,KAAK,CAAC2B,UAAU;QAC5BC,UAAU,EAAE5B,KAAK,CAAC6B,yBAAyB;QAC3CC,SAAS,EAAE,QAAQ;QACnB,CAAC,OAAO7B,YAAY,iBAAiBA,YAAY,UAAU,GAAG;UAC5DwB,KAAK,EAAEzB,KAAK,CAAC+B;QACf;MACF,CAAC;MACD,UAAU,EAAE;QACVC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE;MACZ,CAAC;MACD,QAAQ,EAAE;QACRC,cAAc,EAAEnC,KAAK,CAACoC,QAAQ;QAC9BC,QAAQ,EAAErC,KAAK,CAACsC;MAClB;IACF,CAAC,CAAC;IACF,CAAC,GAAGrC,YAAY,aAAa,GAAG;MAC9BsC,WAAW,EAAE,aAAa;MAC1B7B,OAAO,EAAE,CAAC;MACVI,YAAY,EAAE;IAChB;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAM0B,qBAAqB,GAAGxC,KAAK,KAAK;EAC7C6B,yBAAyB,EAAE,IAAIlC,SAAS,CAACK,KAAK,CAACyC,gBAAgB,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC;AAC1F,CAAC,CAAC;AACF,eAAe9C,aAAa,CAAC,QAAQ,EAAEG,KAAK,IAAI;EAC9C,MAAM4C,WAAW,GAAG9C,UAAU,CAACE,KAAK,EAAE;IACpC+B,eAAe,EAAE/B,KAAK,CAAC0B;EACzB,CAAC,CAAC;EACF,OAAO3B,cAAc,CAAC6C,WAAW,CAAC;AACpC,CAAC,EAAEJ,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}