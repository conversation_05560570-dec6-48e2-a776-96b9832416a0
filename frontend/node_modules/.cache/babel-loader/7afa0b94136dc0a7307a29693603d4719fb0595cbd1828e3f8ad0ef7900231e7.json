{"ast": null, "code": "// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, {\n  textColor,\n  lightBorderColor,\n  lightColor,\n  darkColor\n}) => ({\n  [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n    color: textColor,\n    background: lightColor,\n    borderColor: lightBorderColor,\n    // Inverse color\n    '&-inverse': {\n      color: token.colorTextLightSolid,\n      background: darkColor,\n      borderColor: darkColor\n    },\n    [`&${token.componentCls}-borderless`]: {\n      borderColor: 'transparent'\n    }\n  }\n}));\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);", "map": {"version": 3, "names": ["prepareComponentToken", "prepareToken", "genPresetColor", "genSubStyleComponent", "genPresetStyle", "token", "colorKey", "textColor", "lightBorderColor", "lightColor", "darkColor", "componentCls", "color", "background", "borderColor", "colorTextLightSolid", "tagToken"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/tag/style/presetCmp.js"], "sourcesContent": ["// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, {\n  textColor,\n  lightBorderColor,\n  lightColor,\n  darkColor\n}) => ({\n  [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n    color: textColor,\n    background: lightColor,\n    borderColor: lightBorderColor,\n    // Inverse color\n    '&-inverse': {\n      color: token.colorTextLightSolid,\n      background: darkColor,\n      borderColor: darkColor\n    },\n    [`&${token.componentCls}-borderless`]: {\n      borderColor: 'transparent'\n    }\n  }\n}));\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);"], "mappings": "AAAA;AACA,SAASA,qBAAqB,EAAEC,YAAY,QAAQ,GAAG;AACvD,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC3E;AACA,MAAMC,cAAc,GAAGC,KAAK,IAAIH,cAAc,CAACG,KAAK,EAAE,CAACC,QAAQ,EAAE;EAC/DC,SAAS;EACTC,gBAAgB;EAChBC,UAAU;EACVC;AACF,CAAC,MAAM;EACL,CAAC,GAAGL,KAAK,CAACM,YAAY,GAAGN,KAAK,CAACM,YAAY,IAAIL,QAAQ,EAAE,GAAG;IAC1DM,KAAK,EAAEL,SAAS;IAChBM,UAAU,EAAEJ,UAAU;IACtBK,WAAW,EAAEN,gBAAgB;IAC7B;IACA,WAAW,EAAE;MACXI,KAAK,EAAEP,KAAK,CAACU,mBAAmB;MAChCF,UAAU,EAAEH,SAAS;MACrBI,WAAW,EAAEJ;IACf,CAAC;IACD,CAAC,IAAIL,KAAK,CAACM,YAAY,aAAa,GAAG;MACrCG,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC,CAAC;AACH;AACA,eAAeX,oBAAoB,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAEE,KAAK,IAAI;EAC9D,MAAMW,QAAQ,GAAGf,YAAY,CAACI,KAAK,CAAC;EACpC,OAAOD,cAAc,CAACY,QAAQ,CAAC;AACjC,CAAC,EAAEhB,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}