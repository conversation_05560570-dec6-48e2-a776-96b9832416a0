{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { isPresetColor } from '../_util/colors';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style/ribbon';\nconst Ribbon = props => {\n  const {\n    className,\n    prefixCls: customizePrefixCls,\n    style,\n    color,\n    children,\n    text,\n    placement = 'end',\n    rootClassName\n  } = props;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n  const wrapperCls = `${prefixCls}-wrapper`;\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, wrapperCls);\n  const colorInPreset = isPresetColor(color, false);\n  const ribbonCls = classNames(prefixCls, `${prefixCls}-placement-${placement}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-color-${color}`]: colorInPreset\n  }, className);\n  const colorStyle = {};\n  const cornerColorStyle = {};\n  if (color && !colorInPreset) {\n    colorStyle.background = color;\n    cornerColorStyle.color = color;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapperCls, rootClassName, hashId, cssVarCls)\n  }, children, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(ribbonCls, hashId),\n    style: Object.assign(Object.assign({}, colorStyle), style)\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-text`\n  }, text), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-corner`,\n    style: cornerColorStyle\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ribbon.displayName = 'Ribbon';\n}\nexport default Ribbon;", "map": {"version": 3, "names": ["React", "classNames", "isPresetColor", "ConfigContext", "useStyle", "Ribbon", "props", "className", "prefixCls", "customizePrefixCls", "style", "color", "children", "text", "placement", "rootClassName", "getPrefixCls", "direction", "useContext", "wrapperCls", "wrapCSSVar", "hashId", "cssVarCls", "colorInPreset", "ribbonCls", "colorStyle", "cornerColorStyle", "background", "createElement", "Object", "assign", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/badge/Ribbon.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { isPresetColor } from '../_util/colors';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style/ribbon';\nconst Ribbon = props => {\n  const {\n    className,\n    prefixCls: customizePrefixCls,\n    style,\n    color,\n    children,\n    text,\n    placement = 'end',\n    rootClassName\n  } = props;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n  const wrapperCls = `${prefixCls}-wrapper`;\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, wrapperCls);\n  const colorInPreset = isPresetColor(color, false);\n  const ribbonCls = classNames(prefixCls, `${prefixCls}-placement-${placement}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-color-${color}`]: colorInPreset\n  }, className);\n  const colorStyle = {};\n  const cornerColorStyle = {};\n  if (color && !colorInPreset) {\n    colorStyle.background = color;\n    cornerColorStyle.color = color;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapperCls, rootClassName, hashId, cssVarCls)\n  }, children, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(ribbonCls, hashId),\n    style: Object.assign(Object.assign({}, colorStyle), style)\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-text`\n  }, text), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-corner`,\n    style: cornerColorStyle\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ribbon.displayName = 'Ribbon';\n}\nexport default Ribbon;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,MAAM;IACJC,SAAS;IACTC,SAAS,EAAEC,kBAAkB;IAC7BC,KAAK;IACLC,KAAK;IACLC,QAAQ;IACRC,IAAI;IACJC,SAAS,GAAG,KAAK;IACjBC;EACF,CAAC,GAAGT,KAAK;EACT,MAAM;IACJU,YAAY;IACZC;EACF,CAAC,GAAGjB,KAAK,CAACkB,UAAU,CAACf,aAAa,CAAC;EACnC,MAAMK,SAAS,GAAGQ,YAAY,CAAC,QAAQ,EAAEP,kBAAkB,CAAC;EAC5D,MAAMU,UAAU,GAAG,GAAGX,SAAS,UAAU;EACzC,MAAM,CAACY,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAACI,SAAS,EAAEW,UAAU,CAAC;EACvE,MAAMI,aAAa,GAAGrB,aAAa,CAACS,KAAK,EAAE,KAAK,CAAC;EACjD,MAAMa,SAAS,GAAGvB,UAAU,CAACO,SAAS,EAAE,GAAGA,SAAS,cAAcM,SAAS,EAAE,EAAE;IAC7E,CAAC,GAAGN,SAAS,MAAM,GAAGS,SAAS,KAAK,KAAK;IACzC,CAAC,GAAGT,SAAS,UAAUG,KAAK,EAAE,GAAGY;EACnC,CAAC,EAAEhB,SAAS,CAAC;EACb,MAAMkB,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3B,IAAIf,KAAK,IAAI,CAACY,aAAa,EAAE;IAC3BE,UAAU,CAACE,UAAU,GAAGhB,KAAK;IAC7Be,gBAAgB,CAACf,KAAK,GAAGA,KAAK;EAChC;EACA,OAAOS,UAAU,CAAC,aAAapB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACxDrB,SAAS,EAAEN,UAAU,CAACkB,UAAU,EAAEJ,aAAa,EAAEM,MAAM,EAAEC,SAAS;EACpE,CAAC,EAAEV,QAAQ,EAAE,aAAaZ,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACnDrB,SAAS,EAAEN,UAAU,CAACuB,SAAS,EAAEH,MAAM,CAAC;IACxCX,KAAK,EAAEmB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,UAAU,CAAC,EAAEf,KAAK;EAC3D,CAAC,EAAE,aAAaV,KAAK,CAAC4B,aAAa,CAAC,MAAM,EAAE;IAC1CrB,SAAS,EAAE,GAAGC,SAAS;EACzB,CAAC,EAAEK,IAAI,CAAC,EAAE,aAAab,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAChDrB,SAAS,EAAE,GAAGC,SAAS,SAAS;IAChCE,KAAK,EAAEgB;EACT,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC5B,MAAM,CAAC6B,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAe7B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}