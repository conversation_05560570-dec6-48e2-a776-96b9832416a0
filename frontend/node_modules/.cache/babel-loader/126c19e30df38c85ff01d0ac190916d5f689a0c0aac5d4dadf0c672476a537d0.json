{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCounter } from './utils';\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nconst StatisticTimer = props => {\n  const {\n      value,\n      format = 'HH:mm:ss',\n      onChange,\n      onFinish,\n      type\n    } = props,\n    rest = __rest(props, [\"value\", \"format\", \"onChange\", \"onFinish\", \"type\"]);\n  const down = type === 'countdown';\n  // We reuse state here to do same as `forceUpdate`\n  const [showTime, setShowTime] = React.useState(null);\n  // ======================== Update ========================\n  const update = useEvent(() => {\n    const now = Date.now();\n    const timestamp = getTime(value);\n    setShowTime({});\n    const timeDiff = !down ? now - timestamp : timestamp - now;\n    onChange === null || onChange === void 0 ? void 0 : onChange(timeDiff);\n    // Only countdown will trigger `onFinish`\n    if (down && timestamp < now) {\n      onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n      return false;\n    }\n    return true;\n  });\n  // Effect trigger\n  React.useEffect(() => {\n    let rafId;\n    const clear = () => raf.cancel(rafId);\n    const rafUpdate = () => {\n      rafId = raf(() => {\n        if (update()) {\n          rafUpdate();\n        }\n      });\n    };\n    rafUpdate();\n    return clear;\n  }, [value, down]);\n  React.useEffect(() => {\n    setShowTime({});\n  }, []);\n  // ======================== Format ========================\n  const formatter = (formatValue, config) => showTime ? formatCounter(formatValue, Object.assign(Object.assign({}, config), {\n    format\n  }), down) : '-';\n  const valueRender = node => cloneElement(node, {\n    title: undefined\n  });\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(Statistic, Object.assign({}, rest, {\n    value: value,\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default StatisticTimer;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useEvent", "raf", "cloneElement", "Statistic", "formatCounter", "getTime", "value", "Date", "StatisticTimer", "props", "format", "onChange", "onFinish", "type", "rest", "down", "showTime", "setShowTime", "useState", "update", "now", "timestamp", "timeDiff", "useEffect", "rafId", "clear", "cancel", "rafUpdate", "formatter", "formatValue", "config", "assign", "valueRender", "node", "title", "undefined", "createElement"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/statistic/Timer.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCounter } from './utils';\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nconst StatisticTimer = props => {\n  const {\n      value,\n      format = 'HH:mm:ss',\n      onChange,\n      onFinish,\n      type\n    } = props,\n    rest = __rest(props, [\"value\", \"format\", \"onChange\", \"onFinish\", \"type\"]);\n  const down = type === 'countdown';\n  // We reuse state here to do same as `forceUpdate`\n  const [showTime, setShowTime] = React.useState(null);\n  // ======================== Update ========================\n  const update = useEvent(() => {\n    const now = Date.now();\n    const timestamp = getTime(value);\n    setShowTime({});\n    const timeDiff = !down ? now - timestamp : timestamp - now;\n    onChange === null || onChange === void 0 ? void 0 : onChange(timeDiff);\n    // Only countdown will trigger `onFinish`\n    if (down && timestamp < now) {\n      onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n      return false;\n    }\n    return true;\n  });\n  // Effect trigger\n  React.useEffect(() => {\n    let rafId;\n    const clear = () => raf.cancel(rafId);\n    const rafUpdate = () => {\n      rafId = raf(() => {\n        if (update()) {\n          rafUpdate();\n        }\n      });\n    };\n    rafUpdate();\n    return clear;\n  }, [value, down]);\n  React.useEffect(() => {\n    setShowTime({});\n  }, []);\n  // ======================== Format ========================\n  const formatter = (formatValue, config) => showTime ? formatCounter(formatValue, Object.assign(Object.assign({}, config), {\n    format\n  }), down) : '-';\n  const valueRender = node => cloneElement(node, {\n    title: undefined\n  });\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(Statistic, Object.assign({}, rest, {\n    value: value,\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default StatisticTimer;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,OAAO,IAAIC,IAAI,CAACD,KAAK,CAAC,CAACD,OAAO,CAAC,CAAC;AAClC;AACA,MAAMG,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;MACFH,KAAK;MACLI,MAAM,GAAG,UAAU;MACnBC,QAAQ;MACRC,QAAQ;MACRC;IACF,CAAC,GAAGJ,KAAK;IACTK,IAAI,GAAG7B,MAAM,CAACwB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;EAC3E,MAAMM,IAAI,GAAGF,IAAI,KAAK,WAAW;EACjC;EACA,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC;EACpD;EACA,MAAMC,MAAM,GAAGnB,QAAQ,CAAC,MAAM;IAC5B,MAAMoB,GAAG,GAAGb,IAAI,CAACa,GAAG,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAGhB,OAAO,CAACC,KAAK,CAAC;IAChCW,WAAW,CAAC,CAAC,CAAC,CAAC;IACf,MAAMK,QAAQ,GAAG,CAACP,IAAI,GAAGK,GAAG,GAAGC,SAAS,GAAGA,SAAS,GAAGD,GAAG;IAC1DT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,QAAQ,CAAC;IACtE;IACA,IAAIP,IAAI,IAAIM,SAAS,GAAGD,GAAG,EAAE;MAC3BR,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;MAC9D,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACAb,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,IAAIC,KAAK;IACT,MAAMC,KAAK,GAAGA,CAAA,KAAMxB,GAAG,CAACyB,MAAM,CAACF,KAAK,CAAC;IACrC,MAAMG,SAAS,GAAGA,CAAA,KAAM;MACtBH,KAAK,GAAGvB,GAAG,CAAC,MAAM;QAChB,IAAIkB,MAAM,CAAC,CAAC,EAAE;UACZQ,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,SAAS,CAAC,CAAC;IACX,OAAOF,KAAK;EACd,CAAC,EAAE,CAACnB,KAAK,EAAES,IAAI,CAAC,CAAC;EACjBhB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpBN,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAMW,SAAS,GAAGA,CAACC,WAAW,EAAEC,MAAM,KAAKd,QAAQ,GAAGZ,aAAa,CAACyB,WAAW,EAAEvC,MAAM,CAACyC,MAAM,CAACzC,MAAM,CAACyC,MAAM,CAAC,CAAC,CAAC,EAAED,MAAM,CAAC,EAAE;IACxHpB;EACF,CAAC,CAAC,EAAEK,IAAI,CAAC,GAAG,GAAG;EACf,MAAMiB,WAAW,GAAGC,IAAI,IAAI/B,YAAY,CAAC+B,IAAI,EAAE;IAC7CC,KAAK,EAAEC;EACT,CAAC,CAAC;EACF;EACA,OAAO,aAAapC,KAAK,CAACqC,aAAa,CAACjC,SAAS,EAAEb,MAAM,CAACyC,MAAM,CAAC,CAAC,CAAC,EAAEjB,IAAI,EAAE;IACzER,KAAK,EAAEA,KAAK;IACZ0B,WAAW,EAAEA,WAAW;IACxBJ,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}