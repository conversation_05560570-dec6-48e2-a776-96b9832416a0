{"ast": null, "code": "import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useRafLock() {\n  const [state, setState] = React.useState(false);\n  const rafRef = React.useRef(null);\n  const cleanup = () => {\n    raf.cancel(rafRef.current);\n  };\n  const setDelayState = nextState => {\n    cleanup();\n    if (nextState) {\n      setState(nextState);\n    } else {\n      rafRef.current = raf(() => {\n        setState(nextState);\n      });\n    }\n  };\n  React.useEffect(() => cleanup, []);\n  return [state, setDelayState];\n}", "map": {"version": 3, "names": ["React", "raf", "useRafLock", "state", "setState", "useState", "rafRef", "useRef", "cleanup", "cancel", "current", "setDelayState", "nextState", "useEffect"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/slider/useRafLock.js"], "sourcesContent": ["import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useRafLock() {\n  const [state, setState] = React.useState(false);\n  const rafRef = React.useRef(null);\n  const cleanup = () => {\n    raf.cancel(rafRef.current);\n  };\n  const setDelayState = nextState => {\n    cleanup();\n    if (nextState) {\n      setState(nextState);\n    } else {\n      rafRef.current = raf(() => {\n        setState(nextState);\n      });\n    }\n  };\n  React.useEffect(() => cleanup, []);\n  return [state, setDelayState];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGJ,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMC,MAAM,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpBP,GAAG,CAACQ,MAAM,CAACH,MAAM,CAACI,OAAO,CAAC;EAC5B,CAAC;EACD,MAAMC,aAAa,GAAGC,SAAS,IAAI;IACjCJ,OAAO,CAAC,CAAC;IACT,IAAII,SAAS,EAAE;MACbR,QAAQ,CAACQ,SAAS,CAAC;IACrB,CAAC,MAAM;MACLN,MAAM,CAACI,OAAO,GAAGT,GAAG,CAAC,MAAM;QACzBG,QAAQ,CAACQ,SAAS,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EACDZ,KAAK,CAACa,SAAS,CAAC,MAAML,OAAO,EAAE,EAAE,CAAC;EAClC,OAAO,CAACL,KAAK,EAAEQ,aAAa,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}