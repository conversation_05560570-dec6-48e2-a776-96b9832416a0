{"ast": null, "code": "import { genStyleHooks } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    antCls,\n    zIndexPopup,\n    colorText,\n    colorWarning,\n    marginXXS,\n    marginXS,\n    fontSize,\n    fontWeightStrong,\n    colorTextHeading\n  } = token;\n  return {\n    [componentCls]: {\n      zIndex: zIndexPopup,\n      [`&${antCls}-popover`]: {\n        fontSize\n      },\n      [`${componentCls}-message`]: {\n        marginBottom: marginXS,\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${componentCls}-message-icon ${iconCls}`]: {\n          color: colorWarning,\n          fontSize,\n          lineHeight: 1,\n          marginInlineEnd: marginXS\n        },\n        [`${componentCls}-title`]: {\n          fontWeight: fontWeightStrong,\n          color: colorTextHeading,\n          '&:only-child': {\n            fontWeight: 'normal'\n          }\n        },\n        [`${componentCls}-description`]: {\n          marginTop: marginXXS,\n          color: colorText\n        }\n      },\n      [`${componentCls}-buttons`]: {\n        textAlign: 'end',\n        whiteSpace: 'nowrap',\n        button: {\n          marginInlineStart: marginXS\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    zIndexPopupBase\n  } = token;\n  return {\n    zIndexPopup: zIndexPopupBase + 60\n  };\n};\nexport default genStyleHooks('Popconfirm', token => genBaseStyle(token), prepareComponentToken, {\n  resetStyle: false\n});", "map": {"version": 3, "names": ["genStyleHooks", "genBaseStyle", "token", "componentCls", "iconCls", "antCls", "zIndexPopup", "colorText", "colorWarning", "marginXXS", "marginXS", "fontSize", "fontWeightStrong", "colorTextHeading", "zIndex", "marginBottom", "display", "flexWrap", "alignItems", "color", "lineHeight", "marginInlineEnd", "fontWeight", "marginTop", "textAlign", "whiteSpace", "button", "marginInlineStart", "prepareComponentToken", "zIndexPopupBase", "resetStyle"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/popconfirm/style/index.js"], "sourcesContent": ["import { genStyleHooks } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    antCls,\n    zIndexPopup,\n    colorText,\n    colorWarning,\n    marginXXS,\n    marginXS,\n    fontSize,\n    fontWeightStrong,\n    colorTextHeading\n  } = token;\n  return {\n    [componentCls]: {\n      zIndex: zIndexPopup,\n      [`&${antCls}-popover`]: {\n        fontSize\n      },\n      [`${componentCls}-message`]: {\n        marginBottom: marginXS,\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${componentCls}-message-icon ${iconCls}`]: {\n          color: colorWarning,\n          fontSize,\n          lineHeight: 1,\n          marginInlineEnd: marginXS\n        },\n        [`${componentCls}-title`]: {\n          fontWeight: fontWeightStrong,\n          color: colorTextHeading,\n          '&:only-child': {\n            fontWeight: 'normal'\n          }\n        },\n        [`${componentCls}-description`]: {\n          marginTop: marginXXS,\n          color: colorText\n        }\n      },\n      [`${componentCls}-buttons`]: {\n        textAlign: 'end',\n        whiteSpace: 'nowrap',\n        button: {\n          marginInlineStart: marginXS\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    zIndexPopupBase\n  } = token;\n  return {\n    zIndexPopup: zIndexPopupBase + 60\n  };\n};\nexport default genStyleHooks('Popconfirm', token => genBaseStyle(token), prepareComponentToken, {\n  resetStyle: false\n});"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,MAAM;IACNC,WAAW;IACXC,SAAS;IACTC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACRC,gBAAgB;IAChBC;EACF,CAAC,GAAGX,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdW,MAAM,EAAER,WAAW;MACnB,CAAC,IAAID,MAAM,UAAU,GAAG;QACtBM;MACF,CAAC;MACD,CAAC,GAAGR,YAAY,UAAU,GAAG;QAC3BY,YAAY,EAAEL,QAAQ;QACtBM,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,OAAO;QACnB,CAAC,KAAKf,YAAY,iBAAiBC,OAAO,EAAE,GAAG;UAC7Ce,KAAK,EAAEX,YAAY;UACnBG,QAAQ;UACRS,UAAU,EAAE,CAAC;UACbC,eAAe,EAAEX;QACnB,CAAC;QACD,CAAC,GAAGP,YAAY,QAAQ,GAAG;UACzBmB,UAAU,EAAEV,gBAAgB;UAC5BO,KAAK,EAAEN,gBAAgB;UACvB,cAAc,EAAE;YACdS,UAAU,EAAE;UACd;QACF,CAAC;QACD,CAAC,GAAGnB,YAAY,cAAc,GAAG;UAC/BoB,SAAS,EAAEd,SAAS;UACpBU,KAAK,EAAEZ;QACT;MACF,CAAC;MACD,CAAC,GAAGJ,YAAY,UAAU,GAAG;QAC3BqB,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE;UACNC,iBAAiB,EAAEjB;QACrB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMkB,qBAAqB,GAAG1B,KAAK,IAAI;EAC5C,MAAM;IACJ2B;EACF,CAAC,GAAG3B,KAAK;EACT,OAAO;IACLI,WAAW,EAAEuB,eAAe,GAAG;EACjC,CAAC;AACH,CAAC;AACD,eAAe7B,aAAa,CAAC,YAAY,EAAEE,KAAK,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE0B,qBAAqB,EAAE;EAC9FE,UAAU,EAAE;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}