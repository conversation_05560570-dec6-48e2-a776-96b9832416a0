{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, {\n  colon,\n  prefixCls,\n  bordered\n}, {\n  component,\n  type,\n  showLabel,\n  showContent,\n  labelStyle: rootLabelStyle,\n  contentStyle: rootContentStyle,\n  styles: rootStyles\n}) {\n  return items.map(({\n    label,\n    children,\n    prefixCls: itemPrefixCls = prefixCls,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    span = 1,\n    key,\n    styles\n  }, index) => {\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        styles: {\n          label: Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n          content: Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n        },\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null,\n        type: type\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), style), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label,\n      type: \"label\"\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), style), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children,\n      type: \"content\"\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;", "map": {"version": 3, "names": ["React", "Cell", "DescriptionsContext", "renderCells", "items", "colon", "prefixCls", "bordered", "component", "type", "showLabel", "showContent", "labelStyle", "rootLabelStyle", "contentStyle", "rootContentStyle", "styles", "rootStyles", "map", "label", "children", "itemPrefixCls", "className", "style", "span", "key", "index", "createElement", "Object", "assign", "content", "Row", "props", "descContext", "useContext", "vertical", "row", "Fragment"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/descriptions/Row.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, {\n  colon,\n  prefixCls,\n  bordered\n}, {\n  component,\n  type,\n  showLabel,\n  showContent,\n  labelStyle: rootLabelStyle,\n  contentStyle: rootContentStyle,\n  styles: rootStyles\n}) {\n  return items.map(({\n    label,\n    children,\n    prefixCls: itemPrefixCls = prefixCls,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    span = 1,\n    key,\n    styles\n  }, index) => {\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        styles: {\n          label: Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n          content: Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n        },\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null,\n        type: type\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), style), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label,\n      type: \"label\"\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), style), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children,\n      type: \"content\"\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1BC,KAAK;EACLC,SAAS;EACTC;AACF,CAAC,EAAE;EACDC,SAAS;EACTC,IAAI;EACJC,SAAS;EACTC,WAAW;EACXC,UAAU,EAAEC,cAAc;EAC1BC,YAAY,EAAEC,gBAAgB;EAC9BC,MAAM,EAAEC;AACV,CAAC,EAAE;EACD,OAAOb,KAAK,CAACc,GAAG,CAAC,CAAC;IAChBC,KAAK;IACLC,QAAQ;IACRd,SAAS,EAAEe,aAAa,GAAGf,SAAS;IACpCgB,SAAS;IACTC,KAAK;IACLX,UAAU;IACVE,YAAY;IACZU,IAAI,GAAG,CAAC;IACRC,GAAG;IACHT;EACF,CAAC,EAAEU,KAAK,KAAK;IACX,IAAI,OAAOlB,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAO,aAAaR,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAE;QAC5CwB,GAAG,EAAE,GAAGhB,IAAI,IAAIgB,GAAG,IAAIC,KAAK,EAAE;QAC9BJ,SAAS,EAAEA,SAAS;QACpBC,KAAK,EAAEA,KAAK;QACZP,MAAM,EAAE;UACNG,KAAK,EAAES,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,cAAc,CAAC,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACE,KAAK,CAAC,EAAEP,UAAU,CAAC,EAAEI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,KAAK,CAAC;UACzOW,OAAO,EAAEF,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEd,gBAAgB,CAAC,EAAEE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACa,OAAO,CAAC,EAAEhB,YAAY,CAAC,EAAEE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACc,OAAO;QACpP,CAAC;QACDN,IAAI,EAAEA,IAAI;QACVnB,KAAK,EAAEA,KAAK;QACZG,SAAS,EAAEA,SAAS;QACpBa,aAAa,EAAEA,aAAa;QAC5Bd,QAAQ,EAAEA,QAAQ;QAClBY,KAAK,EAAET,SAAS,GAAGS,KAAK,GAAG,IAAI;QAC/BW,OAAO,EAAEnB,WAAW,GAAGS,QAAQ,GAAG,IAAI;QACtCX,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;IACA,OAAO,CAAC,aAAaT,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAE;MAC7CwB,GAAG,EAAE,SAASA,GAAG,IAAIC,KAAK,EAAE;MAC5BJ,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,cAAc,CAAC,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACE,KAAK,CAAC,EAAEI,KAAK,CAAC,EAAEX,UAAU,CAAC,EAAEI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,KAAK,CAAC;MAC/PK,IAAI,EAAE,CAAC;MACPnB,KAAK,EAAEA,KAAK;MACZG,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBa,aAAa,EAAEA,aAAa;MAC5Bd,QAAQ,EAAEA,QAAQ;MAClBY,KAAK,EAAEA,KAAK;MACZV,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAaT,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAE;MACzCwB,GAAG,EAAE,WAAWA,GAAG,IAAIC,KAAK,EAAE;MAC9BJ,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEd,gBAAgB,CAAC,EAAEE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACa,OAAO,CAAC,EAAEP,KAAK,CAAC,EAAET,YAAY,CAAC,EAAEE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACc,OAAO,CAAC;MACvQN,IAAI,EAAEA,IAAI,GAAG,CAAC,GAAG,CAAC;MAClBhB,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBa,aAAa,EAAEA,aAAa;MAC5Bd,QAAQ,EAAEA,QAAQ;MAClBuB,OAAO,EAAEV,QAAQ;MACjBX,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,MAAMsB,GAAG,GAAGC,KAAK,IAAI;EACnB,MAAMC,WAAW,GAAGjC,KAAK,CAACkC,UAAU,CAAChC,mBAAmB,CAAC;EACzD,MAAM;IACJI,SAAS;IACT6B,QAAQ;IACRC,GAAG;IACHV,KAAK;IACLnB;EACF,CAAC,GAAGyB,KAAK;EACT,IAAIG,QAAQ,EAAE;IACZ,OAAO,aAAanC,KAAK,CAAC2B,aAAa,CAAC3B,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAAE,aAAarC,KAAK,CAAC2B,aAAa,CAAC,IAAI,EAAE;MACnGF,GAAG,EAAE,SAASC,KAAK,EAAE;MACrBJ,SAAS,EAAE,GAAGhB,SAAS;IACzB,CAAC,EAAEH,WAAW,CAACiC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;MACvCrB,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;IACb,CAAC,EAAEuB,WAAW,CAAC,CAAC,CAAC,EAAE,aAAajC,KAAK,CAAC2B,aAAa,CAAC,IAAI,EAAE;MACxDF,GAAG,EAAE,WAAWC,KAAK,EAAE;MACvBJ,SAAS,EAAE,GAAGhB,SAAS;IACzB,CAAC,EAAEH,WAAW,CAACiC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;MACvCrB,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,SAAS;MACfE,WAAW,EAAE;IACf,CAAC,EAAEsB,WAAW,CAAC,CAAC,CAAC,CAAC;EACpB;EACA,OAAO,aAAajC,KAAK,CAAC2B,aAAa,CAAC,IAAI,EAAE;IAC5CF,GAAG,EAAEC,KAAK;IACVJ,SAAS,EAAE,GAAGhB,SAAS;EACzB,CAAC,EAAEH,WAAW,CAACiC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;IACvCrB,SAAS,EAAED,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI;IACzCE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE;EACf,CAAC,EAAEsB,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}