{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genModalMaskStyle } from '../../modal/style';\nimport { textEllipsis } from '../../style';\nimport { initFadeMotion, initZoomMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nexport const genBoxStyle = position => ({\n  position: position || 'absolute',\n  inset: 0\n});\nexport const genImageMaskStyle = token => {\n  const {\n    iconCls,\n    motionDurationSlow,\n    paddingXXS,\n    marginXXS,\n    prefixCls,\n    colorTextLightSolid\n  } = token;\n  return {\n    position: 'absolute',\n    inset: 0,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    color: colorTextLightSolid,\n    background: new FastColor('#000').setA(0.5).toRgbString(),\n    cursor: 'pointer',\n    opacity: 0,\n    transition: `opacity ${motionDurationSlow}`,\n    [`.${prefixCls}-mask-info`]: Object.assign(Object.assign({}, textEllipsis), {\n      padding: `0 ${unit(paddingXXS)}`,\n      [iconCls]: {\n        marginInlineEnd: marginXXS,\n        svg: {\n          verticalAlign: 'baseline'\n        }\n      }\n    })\n  };\n};\nexport const genPreviewOperationsStyle = token => {\n  const {\n    previewCls,\n    modalMaskBg,\n    paddingSM,\n    marginXL,\n    margin,\n    paddingLG,\n    previewOperationColorDisabled,\n    previewOperationHoverColor,\n    motionDurationSlow,\n    iconCls,\n    colorTextLightSolid\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-footer`]: {\n      position: 'fixed',\n      bottom: marginXL,\n      left: {\n        _skip_check_: true,\n        value: '50%'\n      },\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      color: token.previewOperationColor,\n      transform: 'translateX(-50%)'\n    },\n    [`${previewCls}-progress`]: {\n      marginBottom: margin\n    },\n    [`${previewCls}-close`]: {\n      position: 'fixed',\n      top: marginXL,\n      right: {\n        _skip_check_: true,\n        value: marginXL\n      },\n      display: 'flex',\n      color: colorTextLightSolid,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: '50%',\n      padding: paddingSM,\n      outline: 0,\n      border: 0,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      '&:hover': {\n        backgroundColor: operationBgHover.toRgbString()\n      },\n      [`& > ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-operations`]: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: `0 ${unit(paddingLG)}`,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: 100,\n      '&-operation': {\n        marginInlineStart: paddingSM,\n        padding: paddingSM,\n        cursor: 'pointer',\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:not(${previewCls}-operations-operation-disabled):hover > ${iconCls}`]: {\n          color: previewOperationHoverColor\n        },\n        '&-disabled': {\n          color: previewOperationColorDisabled,\n          cursor: 'not-allowed'\n        },\n        '&:first-of-type': {\n          marginInlineStart: 0\n        },\n        [`& > ${iconCls}`]: {\n          fontSize: token.previewOperationSize\n        }\n      }\n    }\n  };\n};\nexport const genPreviewSwitchStyle = token => {\n  const {\n    modalMaskBg,\n    iconCls,\n    previewOperationColorDisabled,\n    previewCls,\n    zIndexPopup,\n    motionDurationSlow\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-switch-left, ${previewCls}-switch-right`]: {\n      position: 'fixed',\n      insetBlockStart: '50%',\n      zIndex: token.calc(zIndexPopup).add(1).equal(),\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      width: token.imagePreviewSwitchSize,\n      height: token.imagePreviewSwitchSize,\n      marginTop: token.calc(token.imagePreviewSwitchSize).mul(-1).div(2).equal(),\n      color: token.previewOperationColor,\n      background: operationBg.toRgbString(),\n      borderRadius: '50%',\n      transform: `translateY(-50%)`,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      userSelect: 'none',\n      '&:hover': {\n        background: operationBgHover.toRgbString()\n      },\n      '&-disabled': {\n        '&, &:hover': {\n          color: previewOperationColorDisabled,\n          background: 'transparent',\n          cursor: 'not-allowed',\n          [`> ${iconCls}`]: {\n            cursor: 'not-allowed'\n          }\n        }\n      },\n      [`> ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-switch-left`]: {\n      insetInlineStart: token.marginSM\n    },\n    [`${previewCls}-switch-right`]: {\n      insetInlineEnd: token.marginSM\n    }\n  };\n};\nexport const genImagePreviewStyle = token => {\n  const {\n    motionEaseOut,\n    previewCls,\n    motionDurationSlow,\n    componentCls\n  } = token;\n  return [{\n    [`${componentCls}-preview-root`]: {\n      [previewCls]: {\n        height: '100%',\n        textAlign: 'center',\n        pointerEvents: 'none'\n      },\n      [`${previewCls}-body`]: Object.assign(Object.assign({}, genBoxStyle()), {\n        overflow: 'hidden'\n      }),\n      [`${previewCls}-img`]: {\n        maxWidth: '100%',\n        maxHeight: '70%',\n        verticalAlign: 'middle',\n        transform: 'scale3d(1, 1, 1)',\n        cursor: 'grab',\n        transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n        userSelect: 'none',\n        '&-wrapper': Object.assign(Object.assign({}, genBoxStyle()), {\n          transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n          // https://github.com/ant-design/ant-design/issues/39913\n          // TailwindCSS will reset img default style.\n          // Let's set back.\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          '& > *': {\n            pointerEvents: 'auto'\n          },\n          '&::before': {\n            display: 'inline-block',\n            width: 1,\n            height: '50%',\n            marginInlineEnd: -1,\n            content: '\"\"'\n          }\n        })\n      },\n      [`${previewCls}-moving`]: {\n        [`${previewCls}-preview-img`]: {\n          cursor: 'grabbing',\n          '&-wrapper': {\n            transitionDuration: '0s'\n          }\n        }\n      }\n    }\n  },\n  // Override\n  {\n    [`${componentCls}-preview-root`]: {\n      [`${previewCls}-wrap`]: {\n        zIndex: token.zIndexPopup\n      }\n    }\n  },\n  // Preview operations & switch\n  {\n    [`${componentCls}-preview-operations-wrapper`]: {\n      position: 'fixed',\n      zIndex: token.calc(token.zIndexPopup).add(1).equal()\n    },\n    '&': [genPreviewOperationsStyle(token), genPreviewSwitchStyle(token)]\n  }];\n};\nconst genImageStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    // ============================== image ==============================\n    [componentCls]: {\n      position: 'relative',\n      display: 'inline-block',\n      [`${componentCls}-img`]: {\n        width: '100%',\n        height: 'auto',\n        verticalAlign: 'middle'\n      },\n      [`${componentCls}-img-placeholder`]: {\n        backgroundColor: token.colorBgContainerDisabled,\n        backgroundImage: \"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')\",\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center center',\n        backgroundSize: '30%'\n      },\n      [`${componentCls}-mask`]: Object.assign({}, genImageMaskStyle(token)),\n      [`${componentCls}-mask:hover`]: {\n        opacity: 1\n      },\n      [`${componentCls}-placeholder`]: Object.assign({}, genBoxStyle())\n    }\n  };\n};\nconst genPreviewMotion = token => {\n  const {\n    previewCls\n  } = token;\n  return {\n    [`${previewCls}-root`]: initZoomMotion(token, 'zoom'),\n    '&': initFadeMotion(token, true)\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + 80,\n  previewOperationColor: new FastColor(token.colorTextLightSolid).setA(0.65).toRgbString(),\n  previewOperationHoverColor: new FastColor(token.colorTextLightSolid).setA(0.85).toRgbString(),\n  previewOperationColorDisabled: new FastColor(token.colorTextLightSolid).setA(0.25).toRgbString(),\n  previewOperationSize: token.fontSizeIcon * 1.5 // FIXME: fontSizeIconLG\n});\nexport default genStyleHooks('Image', token => {\n  const previewCls = `${token.componentCls}-preview`;\n  const imageToken = mergeToken(token, {\n    previewCls,\n    modalMaskBg: new FastColor('#000').setA(0.45).toRgbString(),\n    // FIXME: Shared Token\n    imagePreviewSwitchSize: token.controlHeightLG\n  });\n  return [genImageStyle(imageToken), genImagePreviewStyle(imageToken), genModalMaskStyle(mergeToken(imageToken, {\n    componentCls: previewCls\n  })), genPreviewMotion(imageToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "FastColor", "genModalMaskStyle", "textEllipsis", "initFadeMotion", "initZoomMotion", "genStyleHooks", "mergeToken", "genBoxStyle", "position", "inset", "genImageMaskStyle", "token", "iconCls", "motionDurationSlow", "paddingXXS", "marginXXS", "prefixCls", "colorTextLightSolid", "display", "alignItems", "justifyContent", "color", "background", "setA", "toRgbString", "cursor", "opacity", "transition", "Object", "assign", "padding", "marginInlineEnd", "svg", "verticalAlign", "genPreviewOperationsStyle", "previewCls", "modalMaskBg", "paddingSM", "marginXL", "margin", "paddingLG", "previewOperationColorDisabled", "previewOperationHoverColor", "operationBg", "operationBgHover", "clone", "bottom", "left", "_skip_check_", "value", "flexDirection", "previewOperationColor", "transform", "marginBottom", "top", "right", "backgroundColor", "borderRadius", "outline", "border", "fontSize", "previewOperationSize", "marginInlineStart", "userSelect", "genPreviewSwitchStyle", "zIndexPopup", "insetBlockStart", "zIndex", "calc", "add", "equal", "width", "imagePreviewSwitchSize", "height", "marginTop", "mul", "div", "insetInlineStart", "marginSM", "insetInlineEnd", "genImagePreviewStyle", "motionEaseOut", "componentCls", "textAlign", "pointerEvents", "overflow", "max<PERSON><PERSON><PERSON>", "maxHeight", "content", "transitionDuration", "genImageStyle", "colorBgContainerDisabled", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize", "genPreviewMotion", "prepareComponentToken", "zIndexPopupBase", "fontSizeIcon", "imageToken", "controlHeightLG"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/image/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genModalMaskStyle } from '../../modal/style';\nimport { textEllipsis } from '../../style';\nimport { initFadeMotion, initZoomMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nexport const genBoxStyle = position => ({\n  position: position || 'absolute',\n  inset: 0\n});\nexport const genImageMaskStyle = token => {\n  const {\n    iconCls,\n    motionDurationSlow,\n    paddingXXS,\n    marginXXS,\n    prefixCls,\n    colorTextLightSolid\n  } = token;\n  return {\n    position: 'absolute',\n    inset: 0,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    color: colorTextLightSolid,\n    background: new FastColor('#000').setA(0.5).toRgbString(),\n    cursor: 'pointer',\n    opacity: 0,\n    transition: `opacity ${motionDurationSlow}`,\n    [`.${prefixCls}-mask-info`]: Object.assign(Object.assign({}, textEllipsis), {\n      padding: `0 ${unit(paddingXXS)}`,\n      [iconCls]: {\n        marginInlineEnd: marginXXS,\n        svg: {\n          verticalAlign: 'baseline'\n        }\n      }\n    })\n  };\n};\nexport const genPreviewOperationsStyle = token => {\n  const {\n    previewCls,\n    modalMaskBg,\n    paddingSM,\n    marginXL,\n    margin,\n    paddingLG,\n    previewOperationColorDisabled,\n    previewOperationHoverColor,\n    motionDurationSlow,\n    iconCls,\n    colorTextLightSolid\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-footer`]: {\n      position: 'fixed',\n      bottom: marginXL,\n      left: {\n        _skip_check_: true,\n        value: '50%'\n      },\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      color: token.previewOperationColor,\n      transform: 'translateX(-50%)'\n    },\n    [`${previewCls}-progress`]: {\n      marginBottom: margin\n    },\n    [`${previewCls}-close`]: {\n      position: 'fixed',\n      top: marginXL,\n      right: {\n        _skip_check_: true,\n        value: marginXL\n      },\n      display: 'flex',\n      color: colorTextLightSolid,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: '50%',\n      padding: paddingSM,\n      outline: 0,\n      border: 0,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      '&:hover': {\n        backgroundColor: operationBgHover.toRgbString()\n      },\n      [`& > ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-operations`]: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: `0 ${unit(paddingLG)}`,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: 100,\n      '&-operation': {\n        marginInlineStart: paddingSM,\n        padding: paddingSM,\n        cursor: 'pointer',\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:not(${previewCls}-operations-operation-disabled):hover > ${iconCls}`]: {\n          color: previewOperationHoverColor\n        },\n        '&-disabled': {\n          color: previewOperationColorDisabled,\n          cursor: 'not-allowed'\n        },\n        '&:first-of-type': {\n          marginInlineStart: 0\n        },\n        [`& > ${iconCls}`]: {\n          fontSize: token.previewOperationSize\n        }\n      }\n    }\n  };\n};\nexport const genPreviewSwitchStyle = token => {\n  const {\n    modalMaskBg,\n    iconCls,\n    previewOperationColorDisabled,\n    previewCls,\n    zIndexPopup,\n    motionDurationSlow\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-switch-left, ${previewCls}-switch-right`]: {\n      position: 'fixed',\n      insetBlockStart: '50%',\n      zIndex: token.calc(zIndexPopup).add(1).equal(),\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      width: token.imagePreviewSwitchSize,\n      height: token.imagePreviewSwitchSize,\n      marginTop: token.calc(token.imagePreviewSwitchSize).mul(-1).div(2).equal(),\n      color: token.previewOperationColor,\n      background: operationBg.toRgbString(),\n      borderRadius: '50%',\n      transform: `translateY(-50%)`,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      userSelect: 'none',\n      '&:hover': {\n        background: operationBgHover.toRgbString()\n      },\n      '&-disabled': {\n        '&, &:hover': {\n          color: previewOperationColorDisabled,\n          background: 'transparent',\n          cursor: 'not-allowed',\n          [`> ${iconCls}`]: {\n            cursor: 'not-allowed'\n          }\n        }\n      },\n      [`> ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-switch-left`]: {\n      insetInlineStart: token.marginSM\n    },\n    [`${previewCls}-switch-right`]: {\n      insetInlineEnd: token.marginSM\n    }\n  };\n};\nexport const genImagePreviewStyle = token => {\n  const {\n    motionEaseOut,\n    previewCls,\n    motionDurationSlow,\n    componentCls\n  } = token;\n  return [{\n    [`${componentCls}-preview-root`]: {\n      [previewCls]: {\n        height: '100%',\n        textAlign: 'center',\n        pointerEvents: 'none'\n      },\n      [`${previewCls}-body`]: Object.assign(Object.assign({}, genBoxStyle()), {\n        overflow: 'hidden'\n      }),\n      [`${previewCls}-img`]: {\n        maxWidth: '100%',\n        maxHeight: '70%',\n        verticalAlign: 'middle',\n        transform: 'scale3d(1, 1, 1)',\n        cursor: 'grab',\n        transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n        userSelect: 'none',\n        '&-wrapper': Object.assign(Object.assign({}, genBoxStyle()), {\n          transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n          // https://github.com/ant-design/ant-design/issues/39913\n          // TailwindCSS will reset img default style.\n          // Let's set back.\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          '& > *': {\n            pointerEvents: 'auto'\n          },\n          '&::before': {\n            display: 'inline-block',\n            width: 1,\n            height: '50%',\n            marginInlineEnd: -1,\n            content: '\"\"'\n          }\n        })\n      },\n      [`${previewCls}-moving`]: {\n        [`${previewCls}-preview-img`]: {\n          cursor: 'grabbing',\n          '&-wrapper': {\n            transitionDuration: '0s'\n          }\n        }\n      }\n    }\n  },\n  // Override\n  {\n    [`${componentCls}-preview-root`]: {\n      [`${previewCls}-wrap`]: {\n        zIndex: token.zIndexPopup\n      }\n    }\n  },\n  // Preview operations & switch\n  {\n    [`${componentCls}-preview-operations-wrapper`]: {\n      position: 'fixed',\n      zIndex: token.calc(token.zIndexPopup).add(1).equal()\n    },\n    '&': [genPreviewOperationsStyle(token), genPreviewSwitchStyle(token)]\n  }];\n};\nconst genImageStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    // ============================== image ==============================\n    [componentCls]: {\n      position: 'relative',\n      display: 'inline-block',\n      [`${componentCls}-img`]: {\n        width: '100%',\n        height: 'auto',\n        verticalAlign: 'middle'\n      },\n      [`${componentCls}-img-placeholder`]: {\n        backgroundColor: token.colorBgContainerDisabled,\n        backgroundImage: \"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')\",\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center center',\n        backgroundSize: '30%'\n      },\n      [`${componentCls}-mask`]: Object.assign({}, genImageMaskStyle(token)),\n      [`${componentCls}-mask:hover`]: {\n        opacity: 1\n      },\n      [`${componentCls}-placeholder`]: Object.assign({}, genBoxStyle())\n    }\n  };\n};\nconst genPreviewMotion = token => {\n  const {\n    previewCls\n  } = token;\n  return {\n    [`${previewCls}-root`]: initZoomMotion(token, 'zoom'),\n    '&': initFadeMotion(token, true)\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + 80,\n  previewOperationColor: new FastColor(token.colorTextLightSolid).setA(0.65).toRgbString(),\n  previewOperationHoverColor: new FastColor(token.colorTextLightSolid).setA(0.85).toRgbString(),\n  previewOperationColorDisabled: new FastColor(token.colorTextLightSolid).setA(0.25).toRgbString(),\n  previewOperationSize: token.fontSizeIcon * 1.5 // FIXME: fontSizeIconLG\n});\nexport default genStyleHooks('Image', token => {\n  const previewCls = `${token.componentCls}-preview`;\n  const imageToken = mergeToken(token, {\n    previewCls,\n    modalMaskBg: new FastColor('#000').setA(0.45).toRgbString(),\n    // FIXME: Shared Token\n    imagePreviewSwitchSize: token.controlHeightLG\n  });\n  return [genImageStyle(imageToken), genImagePreviewStyle(imageToken), genModalMaskStyle(mergeToken(imageToken, {\n    componentCls: previewCls\n  })), genPreviewMotion(imageToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,cAAc,EAAEC,cAAc,QAAQ,oBAAoB;AACnE,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAO,MAAMC,WAAW,GAAGC,QAAQ,KAAK;EACtCA,QAAQ,EAAEA,QAAQ,IAAI,UAAU;EAChCC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,OAAO,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACxC,MAAM;IACJC,OAAO;IACPC,kBAAkB;IAClBC,UAAU;IACVC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGN,KAAK;EACT,OAAO;IACLH,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,CAAC;IACRS,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAEJ,mBAAmB;IAC1BK,UAAU,EAAE,IAAItB,SAAS,CAAC,MAAM,CAAC,CAACuB,IAAI,CAAC,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;IACzDC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,WAAWd,kBAAkB,EAAE;IAC3C,CAAC,IAAIG,SAAS,YAAY,GAAGY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3B,YAAY,CAAC,EAAE;MAC1E4B,OAAO,EAAE,KAAK/B,IAAI,CAACe,UAAU,CAAC,EAAE;MAChC,CAACF,OAAO,GAAG;QACTmB,eAAe,EAAEhB,SAAS;QAC1BiB,GAAG,EAAE;UACHC,aAAa,EAAE;QACjB;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,yBAAyB,GAAGvB,KAAK,IAAI;EAChD,MAAM;IACJwB,UAAU;IACVC,WAAW;IACXC,SAAS;IACTC,QAAQ;IACRC,MAAM;IACNC,SAAS;IACTC,6BAA6B;IAC7BC,0BAA0B;IAC1B7B,kBAAkB;IAClBD,OAAO;IACPK;EACF,CAAC,GAAGN,KAAK;EACT,MAAMgC,WAAW,GAAG,IAAI3C,SAAS,CAACoC,WAAW,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;EACxD,MAAMqB,gBAAgB,GAAGD,WAAW,CAACE,KAAK,CAAC,CAAC,CAACtB,IAAI,CAAC,GAAG,CAAC;EACtD,OAAO;IACL,CAAC,GAAGY,UAAU,SAAS,GAAG;MACxB3B,QAAQ,EAAE,OAAO;MACjBsC,MAAM,EAAER,QAAQ;MAChBS,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE;MACT,CAAC;MACD/B,OAAO,EAAE,MAAM;MACfgC,aAAa,EAAE,QAAQ;MACvB/B,UAAU,EAAE,QAAQ;MACpBE,KAAK,EAAEV,KAAK,CAACwC,qBAAqB;MAClCC,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAGjB,UAAU,WAAW,GAAG;MAC1BkB,YAAY,EAAEd;IAChB,CAAC;IACD,CAAC,GAAGJ,UAAU,QAAQ,GAAG;MACvB3B,QAAQ,EAAE,OAAO;MACjB8C,GAAG,EAAEhB,QAAQ;MACbiB,KAAK,EAAE;QACLP,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAEX;MACT,CAAC;MACDpB,OAAO,EAAE,MAAM;MACfG,KAAK,EAAEJ,mBAAmB;MAC1BuC,eAAe,EAAEb,WAAW,CAACnB,WAAW,CAAC,CAAC;MAC1CiC,YAAY,EAAE,KAAK;MACnB3B,OAAO,EAAEO,SAAS;MAClBqB,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTlC,MAAM,EAAE,SAAS;MACjBE,UAAU,EAAE,OAAOd,kBAAkB,EAAE;MACvC,SAAS,EAAE;QACT2C,eAAe,EAAEZ,gBAAgB,CAACpB,WAAW,CAAC;MAChD,CAAC;MACD,CAAC,OAAOZ,OAAO,EAAE,GAAG;QAClBgD,QAAQ,EAAEjD,KAAK,CAACkD;MAClB;IACF,CAAC;IACD,CAAC,GAAG1B,UAAU,aAAa,GAAG;MAC5BjB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBW,OAAO,EAAE,KAAK/B,IAAI,CAACyC,SAAS,CAAC,EAAE;MAC/BgB,eAAe,EAAEb,WAAW,CAACnB,WAAW,CAAC,CAAC;MAC1CiC,YAAY,EAAE,GAAG;MACjB,aAAa,EAAE;QACbK,iBAAiB,EAAEzB,SAAS;QAC5BP,OAAO,EAAEO,SAAS;QAClBZ,MAAM,EAAE,SAAS;QACjBE,UAAU,EAAE,OAAOd,kBAAkB,EAAE;QACvCkD,UAAU,EAAE,MAAM;QAClB,CAAC,SAAS5B,UAAU,2CAA2CvB,OAAO,EAAE,GAAG;UACzES,KAAK,EAAEqB;QACT,CAAC;QACD,YAAY,EAAE;UACZrB,KAAK,EAAEoB,6BAA6B;UACpChB,MAAM,EAAE;QACV,CAAC;QACD,iBAAiB,EAAE;UACjBqC,iBAAiB,EAAE;QACrB,CAAC;QACD,CAAC,OAAOlD,OAAO,EAAE,GAAG;UAClBgD,QAAQ,EAAEjD,KAAK,CAACkD;QAClB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMG,qBAAqB,GAAGrD,KAAK,IAAI;EAC5C,MAAM;IACJyB,WAAW;IACXxB,OAAO;IACP6B,6BAA6B;IAC7BN,UAAU;IACV8B,WAAW;IACXpD;EACF,CAAC,GAAGF,KAAK;EACT,MAAMgC,WAAW,GAAG,IAAI3C,SAAS,CAACoC,WAAW,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;EACxD,MAAMqB,gBAAgB,GAAGD,WAAW,CAACE,KAAK,CAAC,CAAC,CAACtB,IAAI,CAAC,GAAG,CAAC;EACtD,OAAO;IACL,CAAC,GAAGY,UAAU,iBAAiBA,UAAU,eAAe,GAAG;MACzD3B,QAAQ,EAAE,OAAO;MACjB0D,eAAe,EAAE,KAAK;MACtBC,MAAM,EAAExD,KAAK,CAACyD,IAAI,CAACH,WAAW,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MAC9CpD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBmD,KAAK,EAAE5D,KAAK,CAAC6D,sBAAsB;MACnCC,MAAM,EAAE9D,KAAK,CAAC6D,sBAAsB;MACpCE,SAAS,EAAE/D,KAAK,CAACyD,IAAI,CAACzD,KAAK,CAAC6D,sBAAsB,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC;MAC1EjD,KAAK,EAAEV,KAAK,CAACwC,qBAAqB;MAClC7B,UAAU,EAAEqB,WAAW,CAACnB,WAAW,CAAC,CAAC;MACrCiC,YAAY,EAAE,KAAK;MACnBL,SAAS,EAAE,kBAAkB;MAC7B3B,MAAM,EAAE,SAAS;MACjBE,UAAU,EAAE,OAAOd,kBAAkB,EAAE;MACvCkD,UAAU,EAAE,MAAM;MAClB,SAAS,EAAE;QACTzC,UAAU,EAAEsB,gBAAgB,CAACpB,WAAW,CAAC;MAC3C,CAAC;MACD,YAAY,EAAE;QACZ,YAAY,EAAE;UACZH,KAAK,EAAEoB,6BAA6B;UACpCnB,UAAU,EAAE,aAAa;UACzBG,MAAM,EAAE,aAAa;UACrB,CAAC,KAAKb,OAAO,EAAE,GAAG;YAChBa,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACD,CAAC,KAAKb,OAAO,EAAE,GAAG;QAChBgD,QAAQ,EAAEjD,KAAK,CAACkD;MAClB;IACF,CAAC;IACD,CAAC,GAAG1B,UAAU,cAAc,GAAG;MAC7B0C,gBAAgB,EAAElE,KAAK,CAACmE;IAC1B,CAAC;IACD,CAAC,GAAG3C,UAAU,eAAe,GAAG;MAC9B4C,cAAc,EAAEpE,KAAK,CAACmE;IACxB;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAME,oBAAoB,GAAGrE,KAAK,IAAI;EAC3C,MAAM;IACJsE,aAAa;IACb9C,UAAU;IACVtB,kBAAkB;IAClBqE;EACF,CAAC,GAAGvE,KAAK;EACT,OAAO,CAAC;IACN,CAAC,GAAGuE,YAAY,eAAe,GAAG;MAChC,CAAC/C,UAAU,GAAG;QACZsC,MAAM,EAAE,MAAM;QACdU,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAGjD,UAAU,OAAO,GAAGP,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,WAAW,CAAC,CAAC,CAAC,EAAE;QACtE8E,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,CAAC,GAAGlD,UAAU,MAAM,GAAG;QACrBmD,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,KAAK;QAChBtD,aAAa,EAAE,QAAQ;QACvBmB,SAAS,EAAE,kBAAkB;QAC7B3B,MAAM,EAAE,MAAM;QACdE,UAAU,EAAE,aAAad,kBAAkB,IAAIoE,aAAa,KAAK;QACjElB,UAAU,EAAE,MAAM;QAClB,WAAW,EAAEnC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,WAAW,CAAC,CAAC,CAAC,EAAE;UAC3DoB,UAAU,EAAE,aAAad,kBAAkB,IAAIoE,aAAa,KAAK;UACjE;UACA;UACA;UACA/D,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpB,OAAO,EAAE;YACPiE,aAAa,EAAE;UACjB,CAAC;UACD,WAAW,EAAE;YACXlE,OAAO,EAAE,cAAc;YACvBqD,KAAK,EAAE,CAAC;YACRE,MAAM,EAAE,KAAK;YACb1C,eAAe,EAAE,CAAC,CAAC;YACnByD,OAAO,EAAE;UACX;QACF,CAAC;MACH,CAAC;MACD,CAAC,GAAGrD,UAAU,SAAS,GAAG;QACxB,CAAC,GAAGA,UAAU,cAAc,GAAG;UAC7BV,MAAM,EAAE,UAAU;UAClB,WAAW,EAAE;YACXgE,kBAAkB,EAAE;UACtB;QACF;MACF;IACF;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGP,YAAY,eAAe,GAAG;MAChC,CAAC,GAAG/C,UAAU,OAAO,GAAG;QACtBgC,MAAM,EAAExD,KAAK,CAACsD;MAChB;IACF;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGiB,YAAY,6BAA6B,GAAG;MAC9C1E,QAAQ,EAAE,OAAO;MACjB2D,MAAM,EAAExD,KAAK,CAACyD,IAAI,CAACzD,KAAK,CAACsD,WAAW,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;IACrD,CAAC;IACD,GAAG,EAAE,CAACpC,yBAAyB,CAACvB,KAAK,CAAC,EAAEqD,qBAAqB,CAACrD,KAAK,CAAC;EACtE,CAAC,CAAC;AACJ,CAAC;AACD,MAAM+E,aAAa,GAAG/E,KAAK,IAAI;EAC7B,MAAM;IACJuE;EACF,CAAC,GAAGvE,KAAK;EACT,OAAO;IACL;IACA,CAACuE,YAAY,GAAG;MACd1E,QAAQ,EAAE,UAAU;MACpBU,OAAO,EAAE,cAAc;MACvB,CAAC,GAAGgE,YAAY,MAAM,GAAG;QACvBX,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,MAAM;QACdxC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAGiD,YAAY,kBAAkB,GAAG;QACnC1B,eAAe,EAAE7C,KAAK,CAACgF,wBAAwB;QAC/CC,eAAe,EAAE,+oBAA+oB;QAChqBC,gBAAgB,EAAE,WAAW;QAC7BC,kBAAkB,EAAE,eAAe;QACnCC,cAAc,EAAE;MAClB,CAAC;MACD,CAAC,GAAGb,YAAY,OAAO,GAAGtD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,iBAAiB,CAACC,KAAK,CAAC,CAAC;MACrE,CAAC,GAAGuE,YAAY,aAAa,GAAG;QAC9BxD,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGwD,YAAY,cAAc,GAAGtD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,WAAW,CAAC,CAAC;IAClE;EACF,CAAC;AACH,CAAC;AACD,MAAMyF,gBAAgB,GAAGrF,KAAK,IAAI;EAChC,MAAM;IACJwB;EACF,CAAC,GAAGxB,KAAK;EACT,OAAO;IACL,CAAC,GAAGwB,UAAU,OAAO,GAAG/B,cAAc,CAACO,KAAK,EAAE,MAAM,CAAC;IACrD,GAAG,EAAER,cAAc,CAACQ,KAAK,EAAE,IAAI;EACjC,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMsF,qBAAqB,GAAGtF,KAAK,KAAK;EAC7CsD,WAAW,EAAEtD,KAAK,CAACuF,eAAe,GAAG,EAAE;EACvC/C,qBAAqB,EAAE,IAAInD,SAAS,CAACW,KAAK,CAACM,mBAAmB,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;EACxFkB,0BAA0B,EAAE,IAAI1C,SAAS,CAACW,KAAK,CAACM,mBAAmB,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;EAC7FiB,6BAA6B,EAAE,IAAIzC,SAAS,CAACW,KAAK,CAACM,mBAAmB,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;EAChGqC,oBAAoB,EAAElD,KAAK,CAACwF,YAAY,GAAG,GAAG,CAAC;AACjD,CAAC,CAAC;AACF,eAAe9F,aAAa,CAAC,OAAO,EAAEM,KAAK,IAAI;EAC7C,MAAMwB,UAAU,GAAG,GAAGxB,KAAK,CAACuE,YAAY,UAAU;EAClD,MAAMkB,UAAU,GAAG9F,UAAU,CAACK,KAAK,EAAE;IACnCwB,UAAU;IACVC,WAAW,EAAE,IAAIpC,SAAS,CAAC,MAAM,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;IAC3D;IACAgD,sBAAsB,EAAE7D,KAAK,CAAC0F;EAChC,CAAC,CAAC;EACF,OAAO,CAACX,aAAa,CAACU,UAAU,CAAC,EAAEpB,oBAAoB,CAACoB,UAAU,CAAC,EAAEnG,iBAAiB,CAACK,UAAU,CAAC8F,UAAU,EAAE;IAC5GlB,YAAY,EAAE/C;EAChB,CAAC,CAAC,CAAC,EAAE6D,gBAAgB,CAACI,UAAU,CAAC,CAAC;AACpC,CAAC,EAAEH,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}