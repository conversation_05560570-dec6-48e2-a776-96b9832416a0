{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport initCollapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport CollapsePanel from './CollapsePanel';\nimport useStyle from './style';\nconst Collapse = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    getPrefixCls,\n    direction,\n    expandIcon: contextExpandIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('collapse');\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    bordered = true,\n    ghost,\n    size: customizeSize,\n    expandIconPosition = 'start',\n    children,\n    destroyInactivePanel,\n    destroyOnHidden,\n    expandIcon\n  } = props;\n  const mergedSize = useSize(ctx => {\n    var _a;\n    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : ctx) !== null && _a !== void 0 ? _a : 'middle';\n  });\n  const prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Collapse');\n    // Warning if use legacy type `expandIconPosition`\n    process.env.NODE_ENV !== \"production\" ? warning(expandIconPosition !== 'left' && expandIconPosition !== 'right', 'deprecated', '`expandIconPosition` with `left` or `right` is deprecated. Please use `start` or `end` instead.') : void 0;\n    warning.deprecated(!('destroyInactivePanel' in props), 'destroyInactivePanel', 'destroyOnHidden');\n  }\n  // Align with logic position\n  const mergedExpandIconPosition = React.useMemo(() => {\n    if (expandIconPosition === 'left') {\n      return 'start';\n    }\n    return expandIconPosition === 'right' ? 'end' : expandIconPosition;\n  }, [expandIconPosition]);\n  const mergedExpandIcon = expandIcon !== null && expandIcon !== void 0 ? expandIcon : contextExpandIcon;\n  const renderExpandIcon = React.useCallback((panelProps = {}) => {\n    const icon = typeof mergedExpandIcon === 'function' ? mergedExpandIcon(panelProps) : (/*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? direction === 'rtl' ? -90 : 90 : undefined,\n      \"aria-label\": panelProps.isActive ? 'expanded' : 'collapsed'\n    }));\n    return cloneElement(icon, () => {\n      var _a;\n      return {\n        className: classNames((_a = icon === null || icon === void 0 ? void 0 : icon.props) === null || _a === void 0 ? void 0 : _a.className, `${prefixCls}-arrow`)\n      };\n    });\n  }, [mergedExpandIcon, prefixCls]);\n  const collapseClassName = classNames(`${prefixCls}-icon-position-${mergedExpandIconPosition}`, {\n    [`${prefixCls}-borderless`]: !bordered,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-ghost`]: !!ghost,\n    [`${prefixCls}-${mergedSize}`]: mergedSize !== 'middle'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const openMotion = Object.assign(Object.assign({}, initCollapseMotion(rootPrefixCls)), {\n    motionAppear: false,\n    leavedClassName: `${prefixCls}-content-hidden`\n  });\n  const items = React.useMemo(() => {\n    if (children) {\n      return toArray(children).map((child, index) => {\n        var _a, _b;\n        const childProps = child.props;\n        if (childProps === null || childProps === void 0 ? void 0 : childProps.disabled) {\n          const key = (_a = child.key) !== null && _a !== void 0 ? _a : String(index);\n          const mergedChildProps = Object.assign(Object.assign({}, omit(child.props, ['disabled'])), {\n            key,\n            collapsible: (_b = childProps.collapsible) !== null && _b !== void 0 ? _b : 'disabled'\n          });\n          return cloneElement(child, mergedChildProps);\n        }\n        return child;\n      });\n    }\n    return null;\n  }, [children]);\n  return wrapCSSVar(/*#__PURE__*/\n  // @ts-ignore\n  React.createElement(RcCollapse, Object.assign({\n    ref: ref,\n    openMotion: openMotion\n  }, omit(props, ['rootClassName']), {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    // TODO: In the future, destroyInactivePanel in rc-collapse needs to be upgrade to destroyOnHidden\n    destroyInactivePanel: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyInactivePanel\n  }), items));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Collapse.displayName = 'Collapse';\n}\nexport default Object.assign(Collapse, {\n  Panel: CollapsePanel\n});", "map": {"version": 3, "names": ["React", "RightOutlined", "classNames", "RcCollapse", "toArray", "omit", "initCollapseMotion", "cloneElement", "devUseW<PERSON>ning", "useComponentConfig", "useSize", "CollapsePanel", "useStyle", "Collapse", "forwardRef", "props", "ref", "getPrefixCls", "direction", "expandIcon", "contextExpandIcon", "className", "contextClassName", "style", "contextStyle", "prefixCls", "customizePrefixCls", "rootClassName", "bordered", "ghost", "size", "customizeSize", "expandIconPosition", "children", "destroyInactivePanel", "destroyOnHidden", "mergedSize", "ctx", "_a", "rootPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "process", "env", "NODE_ENV", "warning", "deprecated", "mergedExpandIconPosition", "useMemo", "mergedExpandIcon", "renderExpandIcon", "useCallback", "panelProps", "icon", "createElement", "rotate", "isActive", "undefined", "collapseClassName", "openMotion", "Object", "assign", "motionAppear", "leavedClassName", "items", "map", "child", "index", "_b", "childProps", "disabled", "key", "String", "mergedChildProps", "collapsible", "displayName", "Panel"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/collapse/Collapse.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport initCollapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport CollapsePanel from './CollapsePanel';\nimport useStyle from './style';\nconst Collapse = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    getPrefixCls,\n    direction,\n    expandIcon: contextExpandIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('collapse');\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    bordered = true,\n    ghost,\n    size: customizeSize,\n    expandIconPosition = 'start',\n    children,\n    destroyInactivePanel,\n    destroyOnHidden,\n    expandIcon\n  } = props;\n  const mergedSize = useSize(ctx => {\n    var _a;\n    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : ctx) !== null && _a !== void 0 ? _a : 'middle';\n  });\n  const prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Collapse');\n    // Warning if use legacy type `expandIconPosition`\n    process.env.NODE_ENV !== \"production\" ? warning(expandIconPosition !== 'left' && expandIconPosition !== 'right', 'deprecated', '`expandIconPosition` with `left` or `right` is deprecated. Please use `start` or `end` instead.') : void 0;\n    warning.deprecated(!('destroyInactivePanel' in props), 'destroyInactivePanel', 'destroyOnHidden');\n  }\n  // Align with logic position\n  const mergedExpandIconPosition = React.useMemo(() => {\n    if (expandIconPosition === 'left') {\n      return 'start';\n    }\n    return expandIconPosition === 'right' ? 'end' : expandIconPosition;\n  }, [expandIconPosition]);\n  const mergedExpandIcon = expandIcon !== null && expandIcon !== void 0 ? expandIcon : contextExpandIcon;\n  const renderExpandIcon = React.useCallback((panelProps = {}) => {\n    const icon = typeof mergedExpandIcon === 'function' ? mergedExpandIcon(panelProps) : (/*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? direction === 'rtl' ? -90 : 90 : undefined,\n      \"aria-label\": panelProps.isActive ? 'expanded' : 'collapsed'\n    }));\n    return cloneElement(icon, () => {\n      var _a;\n      return {\n        className: classNames((_a = icon === null || icon === void 0 ? void 0 : icon.props) === null || _a === void 0 ? void 0 : _a.className, `${prefixCls}-arrow`)\n      };\n    });\n  }, [mergedExpandIcon, prefixCls]);\n  const collapseClassName = classNames(`${prefixCls}-icon-position-${mergedExpandIconPosition}`, {\n    [`${prefixCls}-borderless`]: !bordered,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-ghost`]: !!ghost,\n    [`${prefixCls}-${mergedSize}`]: mergedSize !== 'middle'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const openMotion = Object.assign(Object.assign({}, initCollapseMotion(rootPrefixCls)), {\n    motionAppear: false,\n    leavedClassName: `${prefixCls}-content-hidden`\n  });\n  const items = React.useMemo(() => {\n    if (children) {\n      return toArray(children).map((child, index) => {\n        var _a, _b;\n        const childProps = child.props;\n        if (childProps === null || childProps === void 0 ? void 0 : childProps.disabled) {\n          const key = (_a = child.key) !== null && _a !== void 0 ? _a : String(index);\n          const mergedChildProps = Object.assign(Object.assign({}, omit(child.props, ['disabled'])), {\n            key,\n            collapsible: (_b = childProps.collapsible) !== null && _b !== void 0 ? _b : 'disabled'\n          });\n          return cloneElement(child, mergedChildProps);\n        }\n        return child;\n      });\n    }\n    return null;\n  }, [children]);\n  return wrapCSSVar(\n  /*#__PURE__*/\n  // @ts-ignore\n  React.createElement(RcCollapse, Object.assign({\n    ref: ref,\n    openMotion: openMotion\n  }, omit(props, ['rootClassName']), {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    // TODO: In the future, destroyInactivePanel in rc-collapse needs to be upgrade to destroyOnHidden\n    destroyInactivePanel: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyInactivePanel\n  }), items));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Collapse.displayName = 'Collapse';\n}\nexport default Object.assign(Collapse, {\n  Panel: CollapsePanel\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,kBAAkB,MAAM,iBAAiB;AAChD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC7D,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,UAAU,EAAEC,iBAAiB;IAC7BC,SAAS,EAAEC,gBAAgB;IAC3BC,KAAK,EAAEC;EACT,CAAC,GAAGf,kBAAkB,CAAC,UAAU,CAAC;EAClC,MAAM;IACJgB,SAAS,EAAEC,kBAAkB;IAC7BL,SAAS;IACTM,aAAa;IACbJ,KAAK;IACLK,QAAQ,GAAG,IAAI;IACfC,KAAK;IACLC,IAAI,EAAEC,aAAa;IACnBC,kBAAkB,GAAG,OAAO;IAC5BC,QAAQ;IACRC,oBAAoB;IACpBC,eAAe;IACfhB;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMqB,UAAU,GAAG1B,OAAO,CAAC2B,GAAG,IAAI;IAChC,IAAIC,EAAE;IACN,OAAO,CAACA,EAAE,GAAGP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGM,GAAG,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,QAAQ;EAClI,CAAC,CAAC;EACF,MAAMb,SAAS,GAAGR,YAAY,CAAC,UAAU,EAAES,kBAAkB,CAAC;EAC9D,MAAMa,aAAa,GAAGtB,YAAY,CAAC,CAAC;EACpC,MAAM,CAACuB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAACa,SAAS,CAAC;EAC3D,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGtC,aAAa,CAAC,UAAU,CAAC;IACzC;IACAmC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAACd,kBAAkB,KAAK,MAAM,IAAIA,kBAAkB,KAAK,OAAO,EAAE,YAAY,EAAE,iGAAiG,CAAC,GAAG,KAAK,CAAC;IAC1Oc,OAAO,CAACC,UAAU,CAAC,EAAE,sBAAsB,IAAIhC,KAAK,CAAC,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;EACnG;EACA;EACA,MAAMiC,wBAAwB,GAAGhD,KAAK,CAACiD,OAAO,CAAC,MAAM;IACnD,IAAIjB,kBAAkB,KAAK,MAAM,EAAE;MACjC,OAAO,OAAO;IAChB;IACA,OAAOA,kBAAkB,KAAK,OAAO,GAAG,KAAK,GAAGA,kBAAkB;EACpE,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACxB,MAAMkB,gBAAgB,GAAG/B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGC,iBAAiB;EACtG,MAAM+B,gBAAgB,GAAGnD,KAAK,CAACoD,WAAW,CAAC,CAACC,UAAU,GAAG,CAAC,CAAC,KAAK;IAC9D,MAAMC,IAAI,GAAG,OAAOJ,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACG,UAAU,CAAC,IAAI,aAAarD,KAAK,CAACuD,aAAa,CAACtD,aAAa,EAAE;MACpIuD,MAAM,EAAEH,UAAU,CAACI,QAAQ,GAAGvC,SAAS,KAAK,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGwC,SAAS;MACxE,YAAY,EAAEL,UAAU,CAACI,QAAQ,GAAG,UAAU,GAAG;IACnD,CAAC,CAAC,CAAC;IACH,OAAOlD,YAAY,CAAC+C,IAAI,EAAE,MAAM;MAC9B,IAAIhB,EAAE;MACN,OAAO;QACLjB,SAAS,EAAEnB,UAAU,CAAC,CAACoC,EAAE,GAAGgB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACvC,KAAK,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjB,SAAS,EAAE,GAAGI,SAAS,QAAQ;MAC7J,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACyB,gBAAgB,EAAEzB,SAAS,CAAC,CAAC;EACjC,MAAMkC,iBAAiB,GAAGzD,UAAU,CAAC,GAAGuB,SAAS,kBAAkBuB,wBAAwB,EAAE,EAAE;IAC7F,CAAC,GAAGvB,SAAS,aAAa,GAAG,CAACG,QAAQ;IACtC,CAAC,GAAGH,SAAS,MAAM,GAAGP,SAAS,KAAK,KAAK;IACzC,CAAC,GAAGO,SAAS,QAAQ,GAAG,CAAC,CAACI,KAAK;IAC/B,CAAC,GAAGJ,SAAS,IAAIW,UAAU,EAAE,GAAGA,UAAU,KAAK;EACjD,CAAC,EAAEd,gBAAgB,EAAED,SAAS,EAAEM,aAAa,EAAEc,MAAM,EAAEC,SAAS,CAAC;EACjE,MAAMkB,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExD,kBAAkB,CAACiC,aAAa,CAAC,CAAC,EAAE;IACrFwB,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE,GAAGvC,SAAS;EAC/B,CAAC,CAAC;EACF,MAAMwC,KAAK,GAAGjE,KAAK,CAACiD,OAAO,CAAC,MAAM;IAChC,IAAIhB,QAAQ,EAAE;MACZ,OAAO7B,OAAO,CAAC6B,QAAQ,CAAC,CAACiC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC7C,IAAI9B,EAAE,EAAE+B,EAAE;QACV,MAAMC,UAAU,GAAGH,KAAK,CAACpD,KAAK;QAC9B,IAAIuD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,QAAQ,EAAE;UAC/E,MAAMC,GAAG,GAAG,CAAClC,EAAE,GAAG6B,KAAK,CAACK,GAAG,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGmC,MAAM,CAACL,KAAK,CAAC;UAC3E,MAAMM,gBAAgB,GAAGb,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzD,IAAI,CAAC8D,KAAK,CAACpD,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACzFyD,GAAG;YACHG,WAAW,EAAE,CAACN,EAAE,GAAGC,UAAU,CAACK,WAAW,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;UAC9E,CAAC,CAAC;UACF,OAAO9D,YAAY,CAAC4D,KAAK,EAAEO,gBAAgB,CAAC;QAC9C;QACA,OAAOP,KAAK;MACd,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAAClC,QAAQ,CAAC,CAAC;EACd,OAAOO,UAAU,CACjB;EACA;EACAxC,KAAK,CAACuD,aAAa,CAACpD,UAAU,EAAE0D,MAAM,CAACC,MAAM,CAAC;IAC5C9C,GAAG,EAAEA,GAAG;IACR4C,UAAU,EAAEA;EACd,CAAC,EAAEvD,IAAI,CAACU,KAAK,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE;IACjCI,UAAU,EAAEgC,gBAAgB;IAC5B1B,SAAS,EAAEA,SAAS;IACpBJ,SAAS,EAAEsC,iBAAiB;IAC5BpC,KAAK,EAAEsC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtC,YAAY,CAAC,EAAED,KAAK,CAAC;IAC5D;IACAW,oBAAoB,EAAEC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGD;EACnG,CAAC,CAAC,EAAE+B,KAAK,CAAC,CAAC;AACb,CAAC,CAAC;AACF,IAAItB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChC,QAAQ,CAAC+D,WAAW,GAAG,UAAU;AACnC;AACA,eAAef,MAAM,CAACC,MAAM,CAACjD,QAAQ,EAAE;EACrCgE,KAAK,EAAElE;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}