{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport Tooltip from '../tooltip';\nconst SliderTooltip = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    open,\n    draggingDelete,\n    value\n  } = props;\n  const innerRef = useRef(null);\n  const mergedOpen = open && !draggingDelete;\n  const rafRef = useRef(null);\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n  function keepAlign() {\n    rafRef.current = raf(() => {\n      var _a;\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forceAlign();\n      rafRef.current = null;\n    });\n  }\n  React.useEffect(() => {\n    if (mergedOpen) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n    return cancelKeepAlign;\n  }, [mergedOpen, props.title, value]);\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    ref: composeRef(innerRef, ref)\n  }, props, {\n    open: mergedOpen\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  SliderTooltip.displayName = 'SliderTooltip';\n}\nexport default SliderTooltip;", "map": {"version": 3, "names": ["React", "useRef", "raf", "composeRef", "<PERSON><PERSON><PERSON>", "SliderTooltip", "forwardRef", "props", "ref", "open", "draggingDelete", "value", "innerRef", "mergedOpen", "rafRef", "cancelKeepAlign", "cancel", "current", "keepAlign", "_a", "forceAlign", "useEffect", "title", "createElement", "Object", "assign", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/slider/SliderTooltip.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport Tooltip from '../tooltip';\nconst SliderTooltip = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    open,\n    draggingDelete,\n    value\n  } = props;\n  const innerRef = useRef(null);\n  const mergedOpen = open && !draggingDelete;\n  const rafRef = useRef(null);\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n  function keepAlign() {\n    rafRef.current = raf(() => {\n      var _a;\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forceAlign();\n      rafRef.current = null;\n    });\n  }\n  React.useEffect(() => {\n    if (mergedOpen) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n    return cancelKeepAlign;\n  }, [mergedOpen, props.title, value]);\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    ref: composeRef(innerRef, ref)\n  }, props, {\n    open: mergedOpen\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  SliderTooltip.displayName = 'SliderTooltip';\n}\nexport default SliderTooltip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,OAAO,MAAM,YAAY;AAChC,MAAMC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAClE,MAAM;IACJC,IAAI;IACJC,cAAc;IACdC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,QAAQ,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMY,UAAU,GAAGJ,IAAI,IAAI,CAACC,cAAc;EAC1C,MAAMI,MAAM,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC3B,SAASc,eAAeA,CAAA,EAAG;IACzBb,GAAG,CAACc,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;IAC1BH,MAAM,CAACG,OAAO,GAAG,IAAI;EACvB;EACA,SAASC,SAASA,CAAA,EAAG;IACnBJ,MAAM,CAACG,OAAO,GAAGf,GAAG,CAAC,MAAM;MACzB,IAAIiB,EAAE;MACN,CAACA,EAAE,GAAGP,QAAQ,CAACK,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,UAAU,CAAC,CAAC;MAC5EN,MAAM,CAACG,OAAO,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EACAjB,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIR,UAAU,EAAE;MACdK,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLH,eAAe,CAAC,CAAC;IACnB;IACA,OAAOA,eAAe;EACxB,CAAC,EAAE,CAACF,UAAU,EAAEN,KAAK,CAACe,KAAK,EAAEX,KAAK,CAAC,CAAC;EACpC,OAAO,aAAaX,KAAK,CAACuB,aAAa,CAACnB,OAAO,EAAEoB,MAAM,CAACC,MAAM,CAAC;IAC7DjB,GAAG,EAAEL,UAAU,CAACS,QAAQ,EAAEJ,GAAG;EAC/B,CAAC,EAAED,KAAK,EAAE;IACRE,IAAI,EAAEI;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCvB,aAAa,CAACwB,WAAW,GAAG,eAAe;AAC7C;AACA,eAAexB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}