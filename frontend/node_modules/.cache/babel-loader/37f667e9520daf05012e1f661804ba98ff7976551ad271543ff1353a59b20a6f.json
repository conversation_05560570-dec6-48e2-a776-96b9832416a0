{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport Affix from '../affix';\nimport { ConfigContext, useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport AnchorLink from './AnchorLink';\nimport AnchorContext from './context';\nimport useStyle from './style';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  const rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      return rect.top - element.ownerDocument.documentElement.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nconst sharpMatcherRegex = /#([\\S ]+)$/;\nconst Anchor = props => {\n  var _a;\n  const {\n    rootClassName,\n    prefixCls: customPrefixCls,\n    className,\n    style,\n    offsetTop,\n    affix = true,\n    showInkInFixed = false,\n    children,\n    items,\n    direction: anchorDirection = 'vertical',\n    bounds,\n    targetOffset,\n    onClick,\n    onChange,\n    getContainer,\n    getCurrentAnchor,\n    replace\n  } = props;\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor');\n    warning.deprecated(!children, 'Anchor children', 'items');\n    process.env.NODE_ENV !== \"production\" ? warning(!(anchorDirection === 'horizontal' && (items === null || items === void 0 ? void 0 : items.some(n => 'children' in n))), 'usage', '`Anchor items#children` is not supported when `Anchor` direction is horizontal.') : void 0;\n  }\n  const [links, setLinks] = React.useState([]);\n  const [activeLink, setActiveLink] = React.useState(null);\n  const activeLinkRef = React.useRef(activeLink);\n  const wrapperRef = React.useRef(null);\n  const spanLinkNode = React.useRef(null);\n  const animating = React.useRef(false);\n  const {\n    direction,\n    getPrefixCls,\n    className: anchorClassName,\n    style: anchorStyle\n  } = useComponentConfig('anchor');\n  const {\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customPrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  const dependencyListItem = JSON.stringify(links);\n  const registerLink = useEvent(link => {\n    if (!links.includes(link)) {\n      setLinks(prev => [].concat(_toConsumableArray(prev), [link]));\n    }\n  });\n  const unregisterLink = useEvent(link => {\n    if (links.includes(link)) {\n      setLinks(prev => prev.filter(i => i !== link));\n    }\n  });\n  const updateInk = () => {\n    var _a;\n    const linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(`.${prefixCls}-link-title-active`);\n    if (linkNode && spanLinkNode.current) {\n      const {\n        style: inkStyle\n      } = spanLinkNode.current;\n      const horizontalAnchor = anchorDirection === 'horizontal';\n      inkStyle.top = horizontalAnchor ? '' : `${linkNode.offsetTop + linkNode.clientHeight / 2}px`;\n      inkStyle.height = horizontalAnchor ? '' : `${linkNode.clientHeight}px`;\n      inkStyle.left = horizontalAnchor ? `${linkNode.offsetLeft}px` : '';\n      inkStyle.width = horizontalAnchor ? `${linkNode.clientWidth}px` : '';\n      if (horizontalAnchor) {\n        scrollIntoView(linkNode, {\n          scrollMode: 'if-needed',\n          block: 'nearest'\n        });\n      }\n    }\n  };\n  const getInternalCurrentAnchor = (_links, _offsetTop = 0, _bounds = 5) => {\n    const linkSections = [];\n    const container = getCurrentContainer();\n    _links.forEach(link => {\n      const sharpLinkMatch = sharpMatcherRegex.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      const target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        const top = getOffsetTop(target, container);\n        if (top <= _offsetTop + _bounds) {\n          linkSections.push({\n            link,\n            top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      const maxSection = linkSections.reduce((prev, curr) => curr.top > prev.top ? curr : prev);\n      return maxSection.link;\n    }\n    return '';\n  };\n  const setCurrentActiveLink = useEvent(link => {\n    // FIXME: Seems a bug since this compare is not equals\n    // `activeLinkRef` is parsed value which will always trigger `onChange` event.\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    const newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  });\n  const handleScroll = React.useCallback(() => {\n    if (animating.current) {\n      return;\n    }\n    const currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  const handleScrollTo = React.useCallback(link => {\n    setCurrentActiveLink(link);\n    const sharpLinkMatch = sharpMatcherRegex.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    const targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    const container = getCurrentContainer();\n    const scrollTop = getScroll(container);\n    const eleOffsetTop = getOffsetTop(targetElement, container);\n    let y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  const wrapperClass = classNames(hashId, cssVarCls, rootCls, rootClassName, `${prefixCls}-wrapper`, {\n    [`${prefixCls}-wrapper-horizontal`]: anchorDirection === 'horizontal',\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, anchorClassName);\n  const anchorClass = classNames(prefixCls, {\n    [`${prefixCls}-fixed`]: !affix && !showInkInFixed\n  });\n  const inkClass = classNames(`${prefixCls}-ink`, {\n    [`${prefixCls}-ink-visible`]: activeLink\n  });\n  const wrapperStyle = Object.assign(Object.assign({\n    maxHeight: offsetTop ? `calc(100vh - ${offsetTop}px)` : '100vh'\n  }, anchorStyle), style);\n  const createNestedLink = options => Array.isArray(options) ? options.map(item => (/*#__PURE__*/React.createElement(AnchorLink, Object.assign({\n    replace: replace\n  }, item, {\n    key: item.key\n  }), anchorDirection === 'vertical' && createNestedLink(item.children)))) : null;\n  const anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  }), 'items' in props ? createNestedLink(items) : children));\n  React.useEffect(() => {\n    const scrollContainer = getCurrentContainer();\n    handleScroll();\n    scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.addEventListener('scroll', handleScroll);\n    return () => {\n      scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.removeEventListener('scroll', handleScroll);\n    };\n  }, [dependencyListItem]);\n  React.useEffect(() => {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(() => {\n    updateInk();\n  }, [anchorDirection, getCurrentAnchor, dependencyListItem, activeLink]);\n  const memoizedContextValue = React.useMemo(() => ({\n    registerLink,\n    unregisterLink,\n    scrollTo: handleScrollTo,\n    activeLink,\n    onClick,\n    direction: anchorDirection\n  }), [activeLink, onClick, handleScrollTo, anchorDirection]);\n  const affixProps = affix && typeof affix === 'object' ? affix : undefined;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? (/*#__PURE__*/React.createElement(Affix, Object.assign({\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, affixProps), anchorContent)) : anchorContent));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Anchor.displayName = 'Anchor';\n}\nexport default Anchor;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "useEvent", "scrollIntoView", "getScroll", "scrollTo", "devUseW<PERSON>ning", "Affix", "ConfigContext", "useComponentConfig", "useCSSVarCls", "AnchorLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useStyle", "getDefaultContainer", "window", "getOffsetTop", "element", "container", "getClientRects", "length", "rect", "getBoundingClientRect", "width", "height", "top", "ownerDocument", "documentElement", "clientTop", "sharpMatcherRegex", "<PERSON><PERSON>", "props", "_a", "rootClassName", "prefixCls", "customPrefixCls", "className", "style", "offsetTop", "affix", "showInkInFixed", "children", "items", "direction", "anchorDirection", "bounds", "targetOffset", "onClick", "onChange", "getContainer", "getCurrentAnchor", "replace", "process", "env", "NODE_ENV", "warning", "deprecated", "some", "n", "links", "setLinks", "useState", "activeLink", "setActiveLink", "activeLinkRef", "useRef", "wrapperRef", "spanLinkNode", "animating", "getPrefixCls", "anchorClassName", "anchorStyle", "getTargetContainer", "useContext", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "getCurrentContainer", "dependencyListItem", "JSON", "stringify", "registerLink", "link", "includes", "prev", "concat", "unregisterLink", "filter", "i", "updateInk", "linkNode", "current", "querySelector", "inkStyle", "horizontalAnchor", "clientHeight", "left", "offsetLeft", "clientWidth", "scrollMode", "block", "getInternalCurrentAnchor", "_links", "_offsetTop", "_bounds", "linkSections", "for<PERSON>ach", "sharpLinkMatch", "exec", "toString", "target", "document", "getElementById", "push", "maxSection", "reduce", "curr", "setCurrentActiveLink", "newLink", "handleScroll", "useCallback", "currentActiveLink", "undefined", "handleScrollTo", "targetElement", "scrollTop", "eleOffsetTop", "y", "callback", "wrapperClass", "anchorClass", "inkClass", "wrapperStyle", "Object", "assign", "maxHeight", "createNestedLink", "options", "Array", "isArray", "map", "item", "createElement", "key", "anchorContent", "ref", "useEffect", "scrollContainer", "addEventListener", "removeEventListener", "memoizedContextValue", "useMemo", "affixProps", "Provider", "value", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/anchor/Anchor.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport Affix from '../affix';\nimport { ConfigContext, useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport AnchorLink from './AnchorLink';\nimport AnchorContext from './context';\nimport useStyle from './style';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  const rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      return rect.top - element.ownerDocument.documentElement.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nconst sharpMatcherRegex = /#([\\S ]+)$/;\nconst Anchor = props => {\n  var _a;\n  const {\n    rootClassName,\n    prefixCls: customPrefixCls,\n    className,\n    style,\n    offsetTop,\n    affix = true,\n    showInkInFixed = false,\n    children,\n    items,\n    direction: anchorDirection = 'vertical',\n    bounds,\n    targetOffset,\n    onClick,\n    onChange,\n    getContainer,\n    getCurrentAnchor,\n    replace\n  } = props;\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor');\n    warning.deprecated(!children, 'Anchor children', 'items');\n    process.env.NODE_ENV !== \"production\" ? warning(!(anchorDirection === 'horizontal' && (items === null || items === void 0 ? void 0 : items.some(n => 'children' in n))), 'usage', '`Anchor items#children` is not supported when `Anchor` direction is horizontal.') : void 0;\n  }\n  const [links, setLinks] = React.useState([]);\n  const [activeLink, setActiveLink] = React.useState(null);\n  const activeLinkRef = React.useRef(activeLink);\n  const wrapperRef = React.useRef(null);\n  const spanLinkNode = React.useRef(null);\n  const animating = React.useRef(false);\n  const {\n    direction,\n    getPrefixCls,\n    className: anchorClassName,\n    style: anchorStyle\n  } = useComponentConfig('anchor');\n  const {\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customPrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  const dependencyListItem = JSON.stringify(links);\n  const registerLink = useEvent(link => {\n    if (!links.includes(link)) {\n      setLinks(prev => [].concat(_toConsumableArray(prev), [link]));\n    }\n  });\n  const unregisterLink = useEvent(link => {\n    if (links.includes(link)) {\n      setLinks(prev => prev.filter(i => i !== link));\n    }\n  });\n  const updateInk = () => {\n    var _a;\n    const linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(`.${prefixCls}-link-title-active`);\n    if (linkNode && spanLinkNode.current) {\n      const {\n        style: inkStyle\n      } = spanLinkNode.current;\n      const horizontalAnchor = anchorDirection === 'horizontal';\n      inkStyle.top = horizontalAnchor ? '' : `${linkNode.offsetTop + linkNode.clientHeight / 2}px`;\n      inkStyle.height = horizontalAnchor ? '' : `${linkNode.clientHeight}px`;\n      inkStyle.left = horizontalAnchor ? `${linkNode.offsetLeft}px` : '';\n      inkStyle.width = horizontalAnchor ? `${linkNode.clientWidth}px` : '';\n      if (horizontalAnchor) {\n        scrollIntoView(linkNode, {\n          scrollMode: 'if-needed',\n          block: 'nearest'\n        });\n      }\n    }\n  };\n  const getInternalCurrentAnchor = (_links, _offsetTop = 0, _bounds = 5) => {\n    const linkSections = [];\n    const container = getCurrentContainer();\n    _links.forEach(link => {\n      const sharpLinkMatch = sharpMatcherRegex.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      const target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        const top = getOffsetTop(target, container);\n        if (top <= _offsetTop + _bounds) {\n          linkSections.push({\n            link,\n            top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      const maxSection = linkSections.reduce((prev, curr) => curr.top > prev.top ? curr : prev);\n      return maxSection.link;\n    }\n    return '';\n  };\n  const setCurrentActiveLink = useEvent(link => {\n    // FIXME: Seems a bug since this compare is not equals\n    // `activeLinkRef` is parsed value which will always trigger `onChange` event.\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    const newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  });\n  const handleScroll = React.useCallback(() => {\n    if (animating.current) {\n      return;\n    }\n    const currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  const handleScrollTo = React.useCallback(link => {\n    setCurrentActiveLink(link);\n    const sharpLinkMatch = sharpMatcherRegex.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    const targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    const container = getCurrentContainer();\n    const scrollTop = getScroll(container);\n    const eleOffsetTop = getOffsetTop(targetElement, container);\n    let y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  const wrapperClass = classNames(hashId, cssVarCls, rootCls, rootClassName, `${prefixCls}-wrapper`, {\n    [`${prefixCls}-wrapper-horizontal`]: anchorDirection === 'horizontal',\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, anchorClassName);\n  const anchorClass = classNames(prefixCls, {\n    [`${prefixCls}-fixed`]: !affix && !showInkInFixed\n  });\n  const inkClass = classNames(`${prefixCls}-ink`, {\n    [`${prefixCls}-ink-visible`]: activeLink\n  });\n  const wrapperStyle = Object.assign(Object.assign({\n    maxHeight: offsetTop ? `calc(100vh - ${offsetTop}px)` : '100vh'\n  }, anchorStyle), style);\n  const createNestedLink = options => Array.isArray(options) ? options.map(item => (/*#__PURE__*/React.createElement(AnchorLink, Object.assign({\n    replace: replace\n  }, item, {\n    key: item.key\n  }), anchorDirection === 'vertical' && createNestedLink(item.children)))) : null;\n  const anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  }), 'items' in props ? createNestedLink(items) : children));\n  React.useEffect(() => {\n    const scrollContainer = getCurrentContainer();\n    handleScroll();\n    scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.addEventListener('scroll', handleScroll);\n    return () => {\n      scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.removeEventListener('scroll', handleScroll);\n    };\n  }, [dependencyListItem]);\n  React.useEffect(() => {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(() => {\n    updateInk();\n  }, [anchorDirection, getCurrentAnchor, dependencyListItem, activeLink]);\n  const memoizedContextValue = React.useMemo(() => ({\n    registerLink,\n    unregisterLink,\n    scrollTo: handleScrollTo,\n    activeLink,\n    onClick,\n    direction: anchorDirection\n  }), [activeLink, onClick, handleScrollTo, anchorDirection]);\n  const affixProps = affix && typeof affix === 'object' ? affix : undefined;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? (/*#__PURE__*/React.createElement(Affix, Object.assign({\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, affixProps), anchorContent)) : anchorContent));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Anchor.displayName = 'Anchor';\n}\nexport default Anchor;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,4BAA4B;AAC9E,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOC,MAAM;AACf;AACA,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACxC,IAAI,CAACD,OAAO,CAACE,cAAc,CAAC,CAAC,CAACC,MAAM,EAAE;IACpC,OAAO,CAAC;EACV;EACA,MAAMC,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;EAC5C,IAAID,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;IAC7B,IAAIN,SAAS,KAAKH,MAAM,EAAE;MACxB,OAAOM,IAAI,CAACI,GAAG,GAAGR,OAAO,CAACS,aAAa,CAACC,eAAe,CAACC,SAAS;IACnE;IACA,OAAOP,IAAI,CAACI,GAAG,GAAGP,SAAS,CAACI,qBAAqB,CAAC,CAAC,CAACG,GAAG;EACzD;EACA,OAAOJ,IAAI,CAACI,GAAG;AACjB;AACA,MAAMI,iBAAiB,GAAG,YAAY;AACtC,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,IAAIC,EAAE;EACN,MAAM;IACJC,aAAa;IACbC,SAAS,EAAEC,eAAe;IAC1BC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK,GAAG,IAAI;IACZC,cAAc,GAAG,KAAK;IACtBC,QAAQ;IACRC,KAAK;IACLC,SAAS,EAAEC,eAAe,GAAG,UAAU;IACvCC,MAAM;IACNC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC,gBAAgB;IAChBC;EACF,CAAC,GAAGpB,KAAK;EACT;EACA,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGjD,aAAa,CAAC,QAAQ,CAAC;IACvCiD,OAAO,CAACC,UAAU,CAAC,CAACf,QAAQ,EAAE,iBAAiB,EAAE,OAAO,CAAC;IACzDW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAEX,eAAe,KAAK,YAAY,KAAKF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACe,IAAI,CAACC,CAAC,IAAI,UAAU,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,iFAAiF,CAAC,GAAG,KAAK,CAAC;EAC/Q;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,KAAK,CAAC6D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/D,KAAK,CAAC6D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMG,aAAa,GAAGhE,KAAK,CAACiE,MAAM,CAACH,UAAU,CAAC;EAC9C,MAAMI,UAAU,GAAGlE,KAAK,CAACiE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,YAAY,GAAGnE,KAAK,CAACiE,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMG,SAAS,GAAGpE,KAAK,CAACiE,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM;IACJtB,SAAS;IACT0B,YAAY;IACZjC,SAAS,EAAEkC,eAAe;IAC1BjC,KAAK,EAAEkC;EACT,CAAC,GAAG9D,kBAAkB,CAAC,QAAQ,CAAC;EAChC,MAAM;IACJ+D;EACF,CAAC,GAAGxE,KAAK,CAACyE,UAAU,CAACjE,aAAa,CAAC;EACnC,MAAM0B,SAAS,GAAGmC,YAAY,CAAC,QAAQ,EAAElC,eAAe,CAAC;EACzD,MAAMuC,OAAO,GAAGhE,YAAY,CAACwB,SAAS,CAAC;EACvC,MAAM,CAACyC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAACqB,SAAS,EAAEwC,OAAO,CAAC;EACpE,MAAMI,mBAAmB,GAAG,CAAC9C,EAAE,GAAGiB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGuB,kBAAkB,MAAM,IAAI,IAAIxC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGlB,mBAAmB;EAC5K,MAAMiE,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CAACtB,KAAK,CAAC;EAChD,MAAMuB,YAAY,GAAGhF,QAAQ,CAACiF,IAAI,IAAI;IACpC,IAAI,CAACxB,KAAK,CAACyB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACzBvB,QAAQ,CAACyB,IAAI,IAAI,EAAE,CAACC,MAAM,CAACvF,kBAAkB,CAACsF,IAAI,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC,CAAC;IAC/D;EACF,CAAC,CAAC;EACF,MAAMI,cAAc,GAAGrF,QAAQ,CAACiF,IAAI,IAAI;IACtC,IAAIxB,KAAK,CAACyB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACxBvB,QAAQ,CAACyB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKN,IAAI,CAAC,CAAC;IAChD;EACF,CAAC,CAAC;EACF,MAAMO,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI1D,EAAE;IACN,MAAM2D,QAAQ,GAAG,CAAC3D,EAAE,GAAGkC,UAAU,CAAC0B,OAAO,MAAM,IAAI,IAAI5D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6D,aAAa,CAAC,IAAI3D,SAAS,oBAAoB,CAAC;IACnI,IAAIyD,QAAQ,IAAIxB,YAAY,CAACyB,OAAO,EAAE;MACpC,MAAM;QACJvD,KAAK,EAAEyD;MACT,CAAC,GAAG3B,YAAY,CAACyB,OAAO;MACxB,MAAMG,gBAAgB,GAAGnD,eAAe,KAAK,YAAY;MACzDkD,QAAQ,CAACrE,GAAG,GAAGsE,gBAAgB,GAAG,EAAE,GAAG,GAAGJ,QAAQ,CAACrD,SAAS,GAAGqD,QAAQ,CAACK,YAAY,GAAG,CAAC,IAAI;MAC5FF,QAAQ,CAACtE,MAAM,GAAGuE,gBAAgB,GAAG,EAAE,GAAG,GAAGJ,QAAQ,CAACK,YAAY,IAAI;MACtEF,QAAQ,CAACG,IAAI,GAAGF,gBAAgB,GAAG,GAAGJ,QAAQ,CAACO,UAAU,IAAI,GAAG,EAAE;MAClEJ,QAAQ,CAACvE,KAAK,GAAGwE,gBAAgB,GAAG,GAAGJ,QAAQ,CAACQ,WAAW,IAAI,GAAG,EAAE;MACpE,IAAIJ,gBAAgB,EAAE;QACpB5F,cAAc,CAACwF,QAAQ,EAAE;UACvBS,UAAU,EAAE,WAAW;UACvBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD,MAAMC,wBAAwB,GAAGA,CAACC,MAAM,EAAEC,UAAU,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,KAAK;IACxE,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMxF,SAAS,GAAG4D,mBAAmB,CAAC,CAAC;IACvCyB,MAAM,CAACI,OAAO,CAACxB,IAAI,IAAI;MACrB,MAAMyB,cAAc,GAAG/E,iBAAiB,CAACgF,IAAI,CAAC1B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC2B,QAAQ,CAAC,CAAC,CAAC;MAC1G,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACA,MAAMG,MAAM,GAAGC,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIG,MAAM,EAAE;QACV,MAAMtF,GAAG,GAAGT,YAAY,CAAC+F,MAAM,EAAE7F,SAAS,CAAC;QAC3C,IAAIO,GAAG,IAAI+E,UAAU,GAAGC,OAAO,EAAE;UAC/BC,YAAY,CAACQ,IAAI,CAAC;YAChB/B,IAAI;YACJ1D;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IACF,IAAIiF,YAAY,CAACtF,MAAM,EAAE;MACvB,MAAM+F,UAAU,GAAGT,YAAY,CAACU,MAAM,CAAC,CAAC/B,IAAI,EAAEgC,IAAI,KAAKA,IAAI,CAAC5F,GAAG,GAAG4D,IAAI,CAAC5D,GAAG,GAAG4F,IAAI,GAAGhC,IAAI,CAAC;MACzF,OAAO8B,UAAU,CAAChC,IAAI;IACxB;IACA,OAAO,EAAE;EACX,CAAC;EACD,MAAMmC,oBAAoB,GAAGpH,QAAQ,CAACiF,IAAI,IAAI;IAC5C;IACA;IACA,IAAInB,aAAa,CAAC4B,OAAO,KAAKT,IAAI,EAAE;MAClC;IACF;IACA;IACA,MAAMoC,OAAO,GAAG,OAAOrE,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACiC,IAAI,CAAC,GAAGA,IAAI;IACtFpB,aAAa,CAACwD,OAAO,CAAC;IACtBvD,aAAa,CAAC4B,OAAO,GAAG2B,OAAO;IAC/B;IACA;IACAvE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACmC,IAAI,CAAC;EACpE,CAAC,CAAC;EACF,MAAMqC,YAAY,GAAGxH,KAAK,CAACyH,WAAW,CAAC,MAAM;IAC3C,IAAIrD,SAAS,CAACwB,OAAO,EAAE;MACrB;IACF;IACA,MAAM8B,iBAAiB,GAAGpB,wBAAwB,CAAC3C,KAAK,EAAEb,YAAY,KAAK6E,SAAS,GAAG7E,YAAY,GAAGR,SAAS,IAAI,CAAC,EAAEO,MAAM,CAAC;IAC7HyE,oBAAoB,CAACI,iBAAiB,CAAC;EACzC,CAAC,EAAE,CAAC3C,kBAAkB,EAAEjC,YAAY,EAAER,SAAS,CAAC,CAAC;EACjD,MAAMsF,cAAc,GAAG5H,KAAK,CAACyH,WAAW,CAACtC,IAAI,IAAI;IAC/CmC,oBAAoB,CAACnC,IAAI,CAAC;IAC1B,MAAMyB,cAAc,GAAG/E,iBAAiB,CAACgF,IAAI,CAAC1B,IAAI,CAAC;IACnD,IAAI,CAACyB,cAAc,EAAE;MACnB;IACF;IACA,MAAMiB,aAAa,GAAGb,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACiB,aAAa,EAAE;MAClB;IACF;IACA,MAAM3G,SAAS,GAAG4D,mBAAmB,CAAC,CAAC;IACvC,MAAMgD,SAAS,GAAG1H,SAAS,CAACc,SAAS,CAAC;IACtC,MAAM6G,YAAY,GAAG/G,YAAY,CAAC6G,aAAa,EAAE3G,SAAS,CAAC;IAC3D,IAAI8G,CAAC,GAAGF,SAAS,GAAGC,YAAY;IAChCC,CAAC,IAAIlF,YAAY,KAAK6E,SAAS,GAAG7E,YAAY,GAAGR,SAAS,IAAI,CAAC;IAC/D8B,SAAS,CAACwB,OAAO,GAAG,IAAI;IACxBvF,QAAQ,CAAC2H,CAAC,EAAE;MACV/E,YAAY,EAAE6B,mBAAmB;MACjCmD,QAAQA,CAAA,EAAG;QACT7D,SAAS,CAACwB,OAAO,GAAG,KAAK;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9C,YAAY,EAAER,SAAS,CAAC,CAAC;EAC7B,MAAM4F,YAAY,GAAGjI,UAAU,CAAC2E,MAAM,EAAEC,SAAS,EAAEH,OAAO,EAAEzC,aAAa,EAAE,GAAGC,SAAS,UAAU,EAAE;IACjG,CAAC,GAAGA,SAAS,qBAAqB,GAAGU,eAAe,KAAK,YAAY;IACrE,CAAC,GAAGV,SAAS,MAAM,GAAGS,SAAS,KAAK;EACtC,CAAC,EAAEP,SAAS,EAAEkC,eAAe,CAAC;EAC9B,MAAM6D,WAAW,GAAGlI,UAAU,CAACiC,SAAS,EAAE;IACxC,CAAC,GAAGA,SAAS,QAAQ,GAAG,CAACK,KAAK,IAAI,CAACC;EACrC,CAAC,CAAC;EACF,MAAM4F,QAAQ,GAAGnI,UAAU,CAAC,GAAGiC,SAAS,MAAM,EAAE;IAC9C,CAAC,GAAGA,SAAS,cAAc,GAAG4B;EAChC,CAAC,CAAC;EACF,MAAMuE,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAC/CC,SAAS,EAAElG,SAAS,GAAG,gBAAgBA,SAAS,KAAK,GAAG;EAC1D,CAAC,EAAEiC,WAAW,CAAC,EAAElC,KAAK,CAAC;EACvB,MAAMoG,gBAAgB,GAAGC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,CAACG,GAAG,CAACC,IAAI,KAAK,aAAa9I,KAAK,CAAC+I,aAAa,CAACpI,UAAU,EAAE2H,MAAM,CAACC,MAAM,CAAC;IAC3IpF,OAAO,EAAEA;EACX,CAAC,EAAE2F,IAAI,EAAE;IACPE,GAAG,EAAEF,IAAI,CAACE;EACZ,CAAC,CAAC,EAAEpG,eAAe,KAAK,UAAU,IAAI6F,gBAAgB,CAACK,IAAI,CAACrG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EAC/E,MAAMwG,aAAa,GAAG,aAAajJ,KAAK,CAAC+I,aAAa,CAAC,KAAK,EAAE;IAC5DG,GAAG,EAAEhF,UAAU;IACf9B,SAAS,EAAE8F,YAAY;IACvB7F,KAAK,EAAEgG;EACT,CAAC,EAAE,aAAarI,KAAK,CAAC+I,aAAa,CAAC,KAAK,EAAE;IACzC3G,SAAS,EAAE+F;EACb,CAAC,EAAE,aAAanI,KAAK,CAAC+I,aAAa,CAAC,MAAM,EAAE;IAC1C3G,SAAS,EAAEgG,QAAQ;IACnBc,GAAG,EAAE/E;EACP,CAAC,CAAC,EAAE,OAAO,IAAIpC,KAAK,GAAG0G,gBAAgB,CAAC/F,KAAK,CAAC,GAAGD,QAAQ,CAAC,CAAC;EAC3DzC,KAAK,CAACmJ,SAAS,CAAC,MAAM;IACpB,MAAMC,eAAe,GAAGtE,mBAAmB,CAAC,CAAC;IAC7C0C,YAAY,CAAC,CAAC;IACd4B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACC,gBAAgB,CAAC,QAAQ,EAAE7B,YAAY,CAAC;IAC1H,OAAO,MAAM;MACX4B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,mBAAmB,CAAC,QAAQ,EAAE9B,YAAY,CAAC;IAC/H,CAAC;EACH,CAAC,EAAE,CAACzC,kBAAkB,CAAC,CAAC;EACxB/E,KAAK,CAACmJ,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOjG,gBAAgB,KAAK,UAAU,EAAE;MAC1CoE,oBAAoB,CAACpE,gBAAgB,CAACc,aAAa,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAAC1C,gBAAgB,CAAC,CAAC;EACtBlD,KAAK,CAACmJ,SAAS,CAAC,MAAM;IACpBzD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC9C,eAAe,EAAEM,gBAAgB,EAAE6B,kBAAkB,EAAEjB,UAAU,CAAC,CAAC;EACvE,MAAMyF,oBAAoB,GAAGvJ,KAAK,CAACwJ,OAAO,CAAC,OAAO;IAChDtE,YAAY;IACZK,cAAc;IACdlF,QAAQ,EAAEuH,cAAc;IACxB9D,UAAU;IACVf,OAAO;IACPJ,SAAS,EAAEC;EACb,CAAC,CAAC,EAAE,CAACkB,UAAU,EAAEf,OAAO,EAAE6E,cAAc,EAAEhF,eAAe,CAAC,CAAC;EAC3D,MAAM6G,UAAU,GAAGlH,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGoF,SAAS;EACzE,OAAOhD,UAAU,CAAC,aAAa3E,KAAK,CAAC+I,aAAa,CAACnI,aAAa,CAAC8I,QAAQ,EAAE;IACzEC,KAAK,EAAEJ;EACT,CAAC,EAAEhH,KAAK,IAAI,aAAavC,KAAK,CAAC+I,aAAa,CAACxI,KAAK,EAAE+H,MAAM,CAACC,MAAM,CAAC;IAChEjG,SAAS,EAAEA,SAAS;IACpByE,MAAM,EAAEjC;EACV,CAAC,EAAE2E,UAAU,CAAC,EAAER,aAAa,CAAC,IAAIA,aAAa,CAAC,CAAC;AACnD,CAAC;AACD,IAAI7F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCxB,MAAM,CAAC8H,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAe9H,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}