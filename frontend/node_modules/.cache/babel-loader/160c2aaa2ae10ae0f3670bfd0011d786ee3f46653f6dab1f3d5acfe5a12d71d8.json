{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport Button from '../button';\nimport Spin from '../spin';\nconst defaultSpin = /*#__PURE__*/React.createElement(Spin, null);\nexport default function QRcodeStatus({\n  prefixCls,\n  locale,\n  onRefresh,\n  statusRender,\n  status\n}) {\n  const defaultExpiredNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", {\n    className: `${prefixCls}-expired`\n  }, locale === null || locale === void 0 ? void 0 : locale.expired), onRefresh && (/*#__PURE__*/React.createElement(Button, {\n    type: \"link\",\n    icon: /*#__PURE__*/React.createElement(ReloadOutlined, null),\n    onClick: onRefresh\n  }, locale === null || locale === void 0 ? void 0 : locale.refresh)));\n  const defaultScannedNode = /*#__PURE__*/React.createElement(\"p\", {\n    className: `${prefixCls}-scanned`\n  }, locale === null || locale === void 0 ? void 0 : locale.scanned);\n  const defaultNodes = {\n    expired: defaultExpiredNode,\n    loading: defaultSpin,\n    scanned: defaultScannedNode\n  };\n  const defaultStatusRender = info => defaultNodes[info.status];\n  const mergedStatusRender = statusRender !== null && statusRender !== void 0 ? statusRender : defaultStatusRender;\n  return mergedStatusRender({\n    status,\n    locale,\n    onRefresh\n  });\n}", "map": {"version": 3, "names": ["React", "ReloadOutlined", "<PERSON><PERSON>", "Spin", "defaultSpin", "createElement", "QRcodeStatus", "prefixCls", "locale", "onRefresh", "statusRender", "status", "defaultExpiredNode", "Fragment", "className", "expired", "type", "icon", "onClick", "refresh", "defaultScannedNode", "scanned", "defaultNodes", "loading", "defaultStatusRender", "info", "mergedStatusRender"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/qr-code/QrcodeStatus.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport Button from '../button';\nimport Spin from '../spin';\nconst defaultSpin = /*#__PURE__*/React.createElement(Spin, null);\nexport default function QRcodeStatus({\n  prefixCls,\n  locale,\n  onRefresh,\n  statusRender,\n  status\n}) {\n  const defaultExpiredNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", {\n    className: `${prefixCls}-expired`\n  }, locale === null || locale === void 0 ? void 0 : locale.expired), onRefresh && (/*#__PURE__*/React.createElement(Button, {\n    type: \"link\",\n    icon: /*#__PURE__*/React.createElement(ReloadOutlined, null),\n    onClick: onRefresh\n  }, locale === null || locale === void 0 ? void 0 : locale.refresh)));\n  const defaultScannedNode = /*#__PURE__*/React.createElement(\"p\", {\n    className: `${prefixCls}-scanned`\n  }, locale === null || locale === void 0 ? void 0 : locale.scanned);\n  const defaultNodes = {\n    expired: defaultExpiredNode,\n    loading: defaultSpin,\n    scanned: defaultScannedNode\n  };\n  const defaultStatusRender = info => defaultNodes[info.status];\n  const mergedStatusRender = statusRender !== null && statusRender !== void 0 ? statusRender : defaultStatusRender;\n  return mergedStatusRender({\n    status,\n    locale,\n    onRefresh\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAACF,IAAI,EAAE,IAAI,CAAC;AAChE,eAAe,SAASG,YAAYA,CAAC;EACnCC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,MAAMC,kBAAkB,GAAG,aAAaZ,KAAK,CAACK,aAAa,CAACL,KAAK,CAACa,QAAQ,EAAE,IAAI,EAAE,aAAab,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE;IACtHS,SAAS,EAAE,GAAGP,SAAS;EACzB,CAAC,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,OAAO,CAAC,EAAEN,SAAS,KAAK,aAAaT,KAAK,CAACK,aAAa,CAACH,MAAM,EAAE;IACzHc,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,aAAajB,KAAK,CAACK,aAAa,CAACJ,cAAc,EAAE,IAAI,CAAC;IAC5DiB,OAAO,EAAET;EACX,CAAC,EAAED,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACW,OAAO,CAAC,CAAC,CAAC;EACpE,MAAMC,kBAAkB,GAAG,aAAapB,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE;IAC/DS,SAAS,EAAE,GAAGP,SAAS;EACzB,CAAC,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACa,OAAO,CAAC;EAClE,MAAMC,YAAY,GAAG;IACnBP,OAAO,EAAEH,kBAAkB;IAC3BW,OAAO,EAAEnB,WAAW;IACpBiB,OAAO,EAAED;EACX,CAAC;EACD,MAAMI,mBAAmB,GAAGC,IAAI,IAAIH,YAAY,CAACG,IAAI,CAACd,MAAM,CAAC;EAC7D,MAAMe,kBAAkB,GAAGhB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGc,mBAAmB;EAChH,OAAOE,kBAAkB,CAAC;IACxBf,MAAM;IACNH,MAAM;IACNC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}