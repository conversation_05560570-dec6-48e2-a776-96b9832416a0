{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React from 'react';\nimport { cloneElement, isFragment } from '../_util/reactNode';\nimport { PresetColors } from '../theme/interface';\nconst rxTwoCNChar = /^[\\u4E00-\\u9FA5]{2}$/;\nexport const isTwoCNChar = rxTwoCNChar.test.bind(rxTwoCNChar);\nexport function convertLegacyProps(type) {\n  if (type === 'danger') {\n    return {\n      danger: true\n    };\n  }\n  return {\n    type\n  };\n}\nexport function isString(str) {\n  return typeof str === 'string';\n}\nexport function isUnBorderedButtonVariant(type) {\n  return type === 'text' || type === 'link';\n}\nfunction splitCNCharsBySpace(child, needInserted) {\n  if (child === null || child === undefined) {\n    return;\n  }\n  const SPACE = needInserted ? ' ' : '';\n  if (typeof child !== 'string' && typeof child !== 'number' && isString(child.type) && isTwoCNChar(child.props.children)) {\n    return cloneElement(child, {\n      children: child.props.children.split('').join(SPACE)\n    });\n  }\n  if (isString(child)) {\n    return isTwoCNChar(child) ? /*#__PURE__*/React.createElement(\"span\", null, child.split('').join(SPACE)) : /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  if (isFragment(child)) {\n    return /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  return child;\n}\nexport function spaceChildren(children, needInserted) {\n  let isPrevChildPure = false;\n  const childList = [];\n  React.Children.forEach(children, child => {\n    const type = typeof child;\n    const isCurrentChildPure = type === 'string' || type === 'number';\n    if (isPrevChildPure && isCurrentChildPure) {\n      const lastIndex = childList.length - 1;\n      const lastChild = childList[lastIndex];\n      childList[lastIndex] = `${lastChild}${child}`;\n    } else {\n      childList.push(child);\n    }\n    isPrevChildPure = isCurrentChildPure;\n  });\n  return React.Children.map(childList, child => splitCNCharsBySpace(child, needInserted));\n}\nconst _ButtonTypes = ['default', 'primary', 'dashed', 'link', 'text'];\nconst _ButtonShapes = ['default', 'circle', 'round'];\nconst _ButtonHTMLTypes = ['submit', 'button', 'reset'];\nexport const _ButtonVariantTypes = ['outlined', 'dashed', 'solid', 'filled', 'text', 'link'];\nexport const _ButtonColorTypes = ['default', 'primary', 'danger'].concat(_toConsumableArray(PresetColors));", "map": {"version": 3, "names": ["_toConsumableArray", "React", "cloneElement", "isFragment", "PresetColors", "rxTwoCNChar", "isTwoCNChar", "test", "bind", "convertLegacyProps", "type", "danger", "isString", "str", "isUnBorderedButtonVariant", "splitCNCharsBySpace", "child", "needInserted", "undefined", "SPACE", "props", "children", "split", "join", "createElement", "spaceChildren", "isPrevChildPure", "childList", "Children", "for<PERSON>ach", "isCurrentChildPure", "lastIndex", "length", "<PERSON><PERSON><PERSON><PERSON>", "push", "map", "_ButtonTypes", "_ButtonShapes", "_ButtonHTMLTypes", "_ButtonVariantTypes", "_ButtonColorTypes", "concat"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/button/buttonHelpers.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React from 'react';\nimport { cloneElement, isFragment } from '../_util/reactNode';\nimport { PresetColors } from '../theme/interface';\nconst rxTwoCNChar = /^[\\u4E00-\\u9FA5]{2}$/;\nexport const isTwoCNChar = rxTwoCNChar.test.bind(rxTwoCNChar);\nexport function convertLegacyProps(type) {\n  if (type === 'danger') {\n    return {\n      danger: true\n    };\n  }\n  return {\n    type\n  };\n}\nexport function isString(str) {\n  return typeof str === 'string';\n}\nexport function isUnBorderedButtonVariant(type) {\n  return type === 'text' || type === 'link';\n}\nfunction splitCNCharsBySpace(child, needInserted) {\n  if (child === null || child === undefined) {\n    return;\n  }\n  const SPACE = needInserted ? ' ' : '';\n  if (typeof child !== 'string' && typeof child !== 'number' && isString(child.type) && isTwoCNChar(child.props.children)) {\n    return cloneElement(child, {\n      children: child.props.children.split('').join(SPACE)\n    });\n  }\n  if (isString(child)) {\n    return isTwoCNChar(child) ? /*#__PURE__*/React.createElement(\"span\", null, child.split('').join(SPACE)) : /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  if (isFragment(child)) {\n    return /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  return child;\n}\nexport function spaceChildren(children, needInserted) {\n  let isPrevChildPure = false;\n  const childList = [];\n  React.Children.forEach(children, child => {\n    const type = typeof child;\n    const isCurrentChildPure = type === 'string' || type === 'number';\n    if (isPrevChildPure && isCurrentChildPure) {\n      const lastIndex = childList.length - 1;\n      const lastChild = childList[lastIndex];\n      childList[lastIndex] = `${lastChild}${child}`;\n    } else {\n      childList.push(child);\n    }\n    isPrevChildPure = isCurrentChildPure;\n  });\n  return React.Children.map(childList, child => splitCNCharsBySpace(child, needInserted));\n}\nconst _ButtonTypes = ['default', 'primary', 'dashed', 'link', 'text'];\nconst _ButtonShapes = ['default', 'circle', 'round'];\nconst _ButtonHTMLTypes = ['submit', 'button', 'reset'];\nexport const _ButtonVariantTypes = ['outlined', 'dashed', 'solid', 'filled', 'text', 'link'];\nexport const _ButtonColorTypes = ['default', 'primary', 'danger'].concat(_toConsumableArray(PresetColors));"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,UAAU,QAAQ,oBAAoB;AAC7D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,MAAMC,WAAW,GAAG,sBAAsB;AAC1C,OAAO,MAAMC,WAAW,GAAGD,WAAW,CAACE,IAAI,CAACC,IAAI,CAACH,WAAW,CAAC;AAC7D,OAAO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAO;MACLC,MAAM,EAAE;IACV,CAAC;EACH;EACA,OAAO;IACLD;EACF,CAAC;AACH;AACA,OAAO,SAASE,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AACA,OAAO,SAASC,yBAAyBA,CAACJ,IAAI,EAAE;EAC9C,OAAOA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM;AAC3C;AACA,SAASK,mBAAmBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAChD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;IACzC;EACF;EACA,MAAMC,KAAK,GAAGF,YAAY,GAAG,GAAG,GAAG,EAAE;EACrC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIJ,QAAQ,CAACI,KAAK,CAACN,IAAI,CAAC,IAAIJ,WAAW,CAACU,KAAK,CAACI,KAAK,CAACC,QAAQ,CAAC,EAAE;IACvH,OAAOnB,YAAY,CAACc,KAAK,EAAE;MACzBK,QAAQ,EAAEL,KAAK,CAACI,KAAK,CAACC,QAAQ,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAACJ,KAAK;IACrD,CAAC,CAAC;EACJ;EACA,IAAIP,QAAQ,CAACI,KAAK,CAAC,EAAE;IACnB,OAAOV,WAAW,CAACU,KAAK,CAAC,GAAG,aAAaf,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAER,KAAK,CAACM,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC,GAAG,aAAalB,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAER,KAAK,CAAC;EACjK;EACA,IAAIb,UAAU,CAACa,KAAK,CAAC,EAAE;IACrB,OAAO,aAAaf,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAER,KAAK,CAAC;EAC9D;EACA,OAAOA,KAAK;AACd;AACA,OAAO,SAASS,aAAaA,CAACJ,QAAQ,EAAEJ,YAAY,EAAE;EACpD,IAAIS,eAAe,GAAG,KAAK;EAC3B,MAAMC,SAAS,GAAG,EAAE;EACpB1B,KAAK,CAAC2B,QAAQ,CAACC,OAAO,CAACR,QAAQ,EAAEL,KAAK,IAAI;IACxC,MAAMN,IAAI,GAAG,OAAOM,KAAK;IACzB,MAAMc,kBAAkB,GAAGpB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;IACjE,IAAIgB,eAAe,IAAII,kBAAkB,EAAE;MACzC,MAAMC,SAAS,GAAGJ,SAAS,CAACK,MAAM,GAAG,CAAC;MACtC,MAAMC,SAAS,GAAGN,SAAS,CAACI,SAAS,CAAC;MACtCJ,SAAS,CAACI,SAAS,CAAC,GAAG,GAAGE,SAAS,GAAGjB,KAAK,EAAE;IAC/C,CAAC,MAAM;MACLW,SAAS,CAACO,IAAI,CAAClB,KAAK,CAAC;IACvB;IACAU,eAAe,GAAGI,kBAAkB;EACtC,CAAC,CAAC;EACF,OAAO7B,KAAK,CAAC2B,QAAQ,CAACO,GAAG,CAACR,SAAS,EAAEX,KAAK,IAAID,mBAAmB,CAACC,KAAK,EAAEC,YAAY,CAAC,CAAC;AACzF;AACA,MAAMmB,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;AACrE,MAAMC,aAAa,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;AACpD,MAAMC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;AACtD,OAAO,MAAMC,mBAAmB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;AAC5F,OAAO,MAAMC,iBAAiB,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAACC,MAAM,CAACzC,kBAAkB,CAACI,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}