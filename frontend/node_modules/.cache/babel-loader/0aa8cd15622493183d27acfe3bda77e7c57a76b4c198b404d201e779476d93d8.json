{"ast": null, "code": "export default function omit(obj, fields) {\n  var clone = Object.assign({}, obj);\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n  return clone;\n}", "map": {"version": 3, "names": ["omit", "obj", "fields", "clone", "Object", "assign", "Array", "isArray", "for<PERSON>ach", "key"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-util/es/omit.js"], "sourcesContent": ["export default function omit(obj, fields) {\n  var clone = Object.assign({}, obj);\n  if (Array.isArray(fields)) {\n    fields.forEach(function (key) {\n      delete clone[key];\n    });\n  }\n  return clone;\n}"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACxC,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAAC;EAClC,IAAIK,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;IACzBA,MAAM,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC5B,OAAON,KAAK,CAACM,GAAG,CAAC;IACnB,CAAC,CAAC;EACJ;EACA,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}