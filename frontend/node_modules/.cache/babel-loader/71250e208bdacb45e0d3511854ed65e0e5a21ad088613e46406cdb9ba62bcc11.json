{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = exports.default = _zh_CN.default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "_zh_CN", "_default"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/lib/calendar/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = exports.default = _zh_CN.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIK,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAC9E,IAAIO,QAAQ,GAAGH,OAAO,CAACH,OAAO,GAAGK,MAAM,CAACL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}