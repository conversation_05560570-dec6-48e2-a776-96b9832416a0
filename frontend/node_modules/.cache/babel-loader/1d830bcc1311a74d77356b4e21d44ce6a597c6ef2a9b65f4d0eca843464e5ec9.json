{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y, options = {}) {\n  const {\n    getContainer = () => window,\n    callback,\n    duration = 450\n  } = options;\n  const container = getContainer();\n  const scrollTop = getScroll(container);\n  const startTime = Date.now();\n  const frameFunc = () => {\n    const timestamp = Date.now();\n    const time = timestamp - startTime;\n    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}", "map": {"version": 3, "names": ["raf", "easeInOutCubic", "getScroll", "isWindow", "scrollTo", "y", "options", "getContainer", "window", "callback", "duration", "container", "scrollTop", "startTime", "Date", "now", "frameFunc", "timestamp", "time", "nextScrollTop", "pageXOffset", "Document", "constructor", "name", "documentElement"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/scrollTo.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y, options = {}) {\n  const {\n    getContainer = () => window,\n    callback,\n    duration = 450\n  } = options;\n  const container = getContainer();\n  const scrollTop = getScroll(container);\n  const startTime = Date.now();\n  const frameFunc = () => {\n    const timestamp = Date.now();\n    const time = timestamp - startTime;\n    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,SAAS,IAAIC,QAAQ,QAAQ,aAAa;AACjD,eAAe,SAASC,QAAQA,CAACC,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAChD,MAAM;IACJC,YAAY,GAAGA,CAAA,KAAMC,MAAM;IAC3BC,QAAQ;IACRC,QAAQ,GAAG;EACb,CAAC,GAAGJ,OAAO;EACX,MAAMK,SAAS,GAAGJ,YAAY,CAAC,CAAC;EAChC,MAAMK,SAAS,GAAGV,SAAS,CAACS,SAAS,CAAC;EACtC,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC5B,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMG,IAAI,GAAGD,SAAS,GAAGJ,SAAS;IAClC,MAAMM,aAAa,GAAGlB,cAAc,CAACiB,IAAI,GAAGR,QAAQ,GAAGA,QAAQ,GAAGQ,IAAI,EAAEN,SAAS,EAAEP,CAAC,EAAEK,QAAQ,CAAC;IAC/F,IAAIP,QAAQ,CAACQ,SAAS,CAAC,EAAE;MACvBA,SAAS,CAACP,QAAQ,CAACI,MAAM,CAACY,WAAW,EAAED,aAAa,CAAC;IACvD,CAAC,MAAM,IAAIR,SAAS,YAAYU,QAAQ,IAAIV,SAAS,CAACW,WAAW,CAACC,IAAI,KAAK,cAAc,EAAE;MACzFZ,SAAS,CAACa,eAAe,CAACZ,SAAS,GAAGO,aAAa;IACrD,CAAC,MAAM;MACLR,SAAS,CAACC,SAAS,GAAGO,aAAa;IACrC;IACA,IAAID,IAAI,GAAGR,QAAQ,EAAE;MACnBV,GAAG,CAACgB,SAAS,CAAC;IAChB,CAAC,MAAM,IAAI,OAAOP,QAAQ,KAAK,UAAU,EAAE;MACzCA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDT,GAAG,CAACgB,SAAS,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}