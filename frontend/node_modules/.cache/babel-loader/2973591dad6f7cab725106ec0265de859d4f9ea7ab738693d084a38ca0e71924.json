{"ast": null, "code": "import * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV === 'test') {\n    const warning = devUseWarning('Menu');\n    warning.deprecated(!children, 'Step', 'items');\n  }\n  if (items) {\n    return items;\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        props\n      } = node;\n      const item = Object.assign({}, props);\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "map": {"version": 3, "names": ["React", "toArray", "devUseW<PERSON>ning", "filter", "items", "item", "useLegacyItems", "children", "process", "env", "NODE_ENV", "warning", "deprecated", "childrenItems", "map", "node", "isValidElement", "props", "Object", "assign"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/steps/useLegacyItems.js"], "sourcesContent": ["import * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV === 'test') {\n    const warning = devUseWarning('Menu');\n    warning.deprecated(!children, 'Step', 'items');\n  }\n  if (items) {\n    return items;\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        props\n      } = node;\n      const item = Object.assign({}, props);\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,CAACE,IAAI,IAAIA,IAAI,CAAC;AACnC;AACA,eAAe,SAASC,cAAcA,CAACF,KAAK,EAAEG,QAAQ,EAAE;EACtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,MAAMC,OAAO,GAAGT,aAAa,CAAC,MAAM,CAAC;IACrCS,OAAO,CAACC,UAAU,CAAC,CAACL,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;EAChD;EACA,IAAIH,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EACA,MAAMS,aAAa,GAAGZ,OAAO,CAACM,QAAQ,CAAC,CAACO,GAAG,CAACC,IAAI,IAAI;IAClD,IAAI,aAAaf,KAAK,CAACgB,cAAc,CAACD,IAAI,CAAC,EAAE;MAC3C,MAAM;QACJE;MACF,CAAC,GAAGF,IAAI;MACR,MAAMV,IAAI,GAAGa,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,CAAC;MACrC,OAAOZ,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF,OAAOF,MAAM,CAACU,aAAa,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}