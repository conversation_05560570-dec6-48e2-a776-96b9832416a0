{"ast": null, "code": "\"use client\";\n\nimport React, { useMemo, useRef, useState } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { groupKeysMap } from '../_util/transKeys';\nimport Checkbox from '../checkbox';\nimport Dropdown from '../dropdown';\nimport DefaultListBody, { OmitProps } from './ListBody';\nimport Search from './search';\nconst defaultRender = () => null;\nfunction isRenderResultPlainObject(result) {\n  return !!(result && ! /*#__PURE__*/React.isValidElement(result) && Object.prototype.toString.call(result) === '[object Object]');\n}\nfunction getEnabledItemKeys(items) {\n  return items.filter(data => !data.disabled).map(data => data.key);\n}\nconst isValidIcon = icon => icon !== undefined;\nconst getShowSearchOption = showSearch => {\n  if (showSearch && typeof showSearch === 'object') {\n    return Object.assign(Object.assign({}, showSearch), {\n      defaultValue: showSearch.defaultValue || ''\n    });\n  }\n  return {\n    defaultValue: '',\n    placeholder: ''\n  };\n};\nconst TransferList = props => {\n  const {\n    prefixCls,\n    dataSource = [],\n    titleText = '',\n    checkedKeys,\n    disabled,\n    showSearch = false,\n    style,\n    searchPlaceholder,\n    notFoundContent,\n    selectAll,\n    deselectAll,\n    selectCurrent,\n    selectInvert,\n    removeAll,\n    removeCurrent,\n    showSelectAll = true,\n    showRemove,\n    pagination,\n    direction,\n    itemsUnit,\n    itemUnit,\n    selectAllLabel,\n    selectionsIcon,\n    footer,\n    renderList,\n    onItemSelectAll,\n    onItemRemove,\n    handleFilter,\n    handleClear,\n    filterOption,\n    render = defaultRender\n  } = props;\n  const searchOptions = getShowSearchOption(showSearch);\n  const [filterValue, setFilterValue] = useState(searchOptions.defaultValue);\n  const listBodyRef = useRef({});\n  const internalHandleFilter = e => {\n    setFilterValue(e.target.value);\n    handleFilter(e);\n  };\n  const internalHandleClear = () => {\n    setFilterValue('');\n    handleClear();\n  };\n  const matchFilter = (text, item) => {\n    if (filterOption) {\n      return filterOption(filterValue, item, direction);\n    }\n    return text.includes(filterValue);\n  };\n  const renderListBody = listProps => {\n    let bodyContent = renderList ? renderList(Object.assign(Object.assign({}, listProps), {\n      onItemSelect: (key, check) => listProps.onItemSelect(key, check)\n    })) : null;\n    const customize = !!bodyContent;\n    if (!customize) {\n      // @ts-ignore\n      bodyContent = /*#__PURE__*/React.createElement(DefaultListBody, Object.assign({\n        ref: listBodyRef\n      }, listProps));\n    }\n    return {\n      customize,\n      bodyContent\n    };\n  };\n  const renderItem = item => {\n    const renderResult = render(item);\n    const isRenderResultPlain = isRenderResultPlainObject(renderResult);\n    return {\n      item,\n      renderedEl: isRenderResultPlain ? renderResult.label : renderResult,\n      renderedText: isRenderResultPlain ? renderResult.value : renderResult\n    };\n  };\n  const notFoundContentEle = useMemo(() => Array.isArray(notFoundContent) ? notFoundContent[direction === 'left' ? 0 : 1] : notFoundContent, [notFoundContent, direction]);\n  const [filteredItems, filteredRenderItems] = useMemo(() => {\n    const filterItems = [];\n    const filterRenderItems = [];\n    dataSource.forEach(item => {\n      const renderedItem = renderItem(item);\n      if (filterValue && !matchFilter(renderedItem.renderedText, item)) {\n        return;\n      }\n      filterItems.push(item);\n      filterRenderItems.push(renderedItem);\n    });\n    return [filterItems, filterRenderItems];\n  }, [dataSource, filterValue]);\n  const checkedActiveItems = useMemo(() => {\n    return filteredItems.filter(item => checkedKeys.includes(item.key) && !item.disabled);\n  }, [checkedKeys, filteredItems]);\n  const checkStatus = useMemo(() => {\n    if (checkedActiveItems.length === 0) {\n      return 'none';\n    }\n    const checkedKeysMap = groupKeysMap(checkedKeys);\n    if (filteredItems.every(item => checkedKeysMap.has(item.key) || !!item.disabled)) {\n      return 'all';\n    }\n    return 'part';\n  }, [checkedKeys, checkedActiveItems]);\n  const listBody = useMemo(() => {\n    const search = showSearch ? (/*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-body-search-wrapper`\n    }, /*#__PURE__*/React.createElement(Search, {\n      prefixCls: `${prefixCls}-search`,\n      onChange: internalHandleFilter,\n      handleClear: internalHandleClear,\n      placeholder: searchOptions.placeholder || searchPlaceholder,\n      value: filterValue,\n      disabled: disabled\n    }))) : null;\n    const {\n      customize,\n      bodyContent\n    } = renderListBody(Object.assign(Object.assign({}, omit(props, OmitProps)), {\n      filteredItems,\n      filteredRenderItems,\n      selectedKeys: checkedKeys\n    }));\n    let bodyNode;\n    // We should wrap customize list body in a classNamed div to use flex layout.\n    if (customize) {\n      bodyNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-body-customize-wrapper`\n      }, bodyContent);\n    } else {\n      bodyNode = filteredItems.length ? bodyContent : (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-body-not-found`\n      }, notFoundContentEle));\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(`${prefixCls}-body`, {\n        [`${prefixCls}-body-with-search`]: showSearch\n      })\n    }, search, bodyNode);\n  }, [showSearch, prefixCls, searchPlaceholder, filterValue, disabled, checkedKeys, filteredItems, filteredRenderItems, notFoundContentEle]);\n  const checkBox = /*#__PURE__*/React.createElement(Checkbox, {\n    disabled: dataSource.filter(d => !d.disabled).length === 0 || disabled,\n    checked: checkStatus === 'all',\n    indeterminate: checkStatus === 'part',\n    className: `${prefixCls}-checkbox`,\n    onChange: () => {\n      // Only select enabled items\n      onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(filteredItems.filter(item => !item.disabled).map(({\n        key\n      }) => key), checkStatus !== 'all');\n    }\n  });\n  const getSelectAllLabel = (selectedCount, totalCount) => {\n    if (selectAllLabel) {\n      return typeof selectAllLabel === 'function' ? selectAllLabel({\n        selectedCount,\n        totalCount\n      }) : selectAllLabel;\n    }\n    const unit = totalCount > 1 ? itemsUnit : itemUnit;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, (selectedCount > 0 ? `${selectedCount}/` : '') + totalCount, \" \", unit);\n  };\n  // Custom Layout\n  const footerDom = footer && (footer.length < 2 ? footer(props) : footer(props, {\n    direction\n  }));\n  const listCls = classNames(prefixCls, {\n    [`${prefixCls}-with-pagination`]: !!pagination,\n    [`${prefixCls}-with-footer`]: !!footerDom\n  });\n  // ====================== Get filtered, checked item list ======================\n  const listFooter = footerDom ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-footer`\n  }, footerDom) : null;\n  const checkAllCheckbox = !showRemove && !pagination && checkBox;\n  let items;\n  if (showRemove) {\n    items = [/* Remove Current Page */\n    pagination ? {\n      key: 'removeCurrent',\n      label: removeCurrent,\n      onClick() {\n        var _a;\n        const pageKeys = getEnabledItemKeys((((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || []).map(entity => entity.item));\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(pageKeys);\n      }\n    } : null, /* Remove All */\n    {\n      key: 'removeAll',\n      label: removeAll,\n      onClick() {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(getEnabledItemKeys(filteredItems));\n      }\n    }].filter(Boolean);\n  } else {\n    items = [{\n      key: 'selectAll',\n      label: checkStatus === 'all' ? deselectAll : selectAll,\n      onClick() {\n        const keys = getEnabledItemKeys(filteredItems);\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(keys, keys.length !== checkedKeys.length);\n      }\n    }, pagination ? {\n      key: 'selectCurrent',\n      label: selectCurrent,\n      onClick() {\n        var _a;\n        const pageItems = ((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || [];\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(getEnabledItemKeys(pageItems.map(entity => entity.item)), true);\n      }\n    } : null, {\n      key: 'selectInvert',\n      label: selectInvert,\n      onClick() {\n        var _a;\n        const availablePageItemKeys = getEnabledItemKeys((((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || []).map(entity => entity.item));\n        const checkedKeySet = new Set(checkedKeys);\n        const newCheckedKeysSet = new Set(checkedKeySet);\n        availablePageItemKeys.forEach(key => {\n          if (checkedKeySet.has(key)) {\n            newCheckedKeysSet.delete(key);\n          } else {\n            newCheckedKeysSet.add(key);\n          }\n        });\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(Array.from(newCheckedKeysSet), 'replace');\n      }\n    }];\n  }\n  const dropdown = /*#__PURE__*/React.createElement(Dropdown, {\n    className: `${prefixCls}-header-dropdown`,\n    menu: {\n      items\n    },\n    disabled: disabled\n  }, isValidIcon(selectionsIcon) ? selectionsIcon : /*#__PURE__*/React.createElement(DownOutlined, null));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: listCls,\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`\n  }, showSelectAll ? (/*#__PURE__*/React.createElement(React.Fragment, null, checkAllCheckbox, dropdown)) : null, /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-header-selected`\n  }, getSelectAllLabel(checkedActiveItems.length, filteredItems.length)), /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-header-title`\n  }, titleText)), listBody, listFooter);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TransferList.displayName = 'TransferList';\n}\nexport default TransferList;", "map": {"version": 3, "names": ["React", "useMemo", "useRef", "useState", "DownOutlined", "classNames", "omit", "groupKeysMap", "Checkbox", "Dropdown", "DefaultListBody", "OmitProps", "Search", "defaultRender", "isRenderResultPlainObject", "result", "isValidElement", "Object", "prototype", "toString", "call", "getEnabledItemKeys", "items", "filter", "data", "disabled", "map", "key", "isValidIcon", "icon", "undefined", "getShowSearchOption", "showSearch", "assign", "defaultValue", "placeholder", "TransferList", "props", "prefixCls", "dataSource", "titleText", "checked<PERSON>eys", "style", "searchPlaceholder", "notFoundContent", "selectAll", "deselectAll", "selectCurrent", "selectInvert", "removeAll", "removeCurrent", "showSelectAll", "showRemove", "pagination", "direction", "itemsUnit", "itemUnit", "selectAllLabel", "selectionsIcon", "footer", "renderList", "onItemSelectAll", "onItemRemove", "handleFilter", "handleClear", "filterOption", "render", "searchOptions", "filterValue", "setFilterValue", "listBodyRef", "internalHandleFilter", "e", "target", "value", "internalHandleClear", "matchFilter", "text", "item", "includes", "renderListBody", "listProps", "bodyContent", "onItemSelect", "check", "customize", "createElement", "ref", "renderItem", "renderResult", "isRenderResultPlain", "renderedEl", "label", "renderedText", "notFoundContentEle", "Array", "isArray", "filteredItems", "filteredRenderItems", "filterItems", "filterRenderItems", "for<PERSON>ach", "renderedItem", "push", "checkedActiveItems", "checkStatus", "length", "checkedKeysMap", "every", "has", "listBody", "search", "className", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "bodyNode", "checkBox", "d", "checked", "indeterminate", "getSelectAllLabel", "selectedCount", "totalCount", "unit", "Fragment", "footerDom", "listCls", "listFooter", "checkAllCheckbox", "onClick", "_a", "pageKeys", "current", "entity", "Boolean", "keys", "pageItems", "availablePageItemKeys", "checkedKeySet", "Set", "newCheckedKeysSet", "delete", "add", "from", "dropdown", "menu", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/transfer/list.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useMemo, useRef, useState } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { groupKeysMap } from '../_util/transKeys';\nimport Checkbox from '../checkbox';\nimport Dropdown from '../dropdown';\nimport DefaultListBody, { OmitProps } from './ListBody';\nimport Search from './search';\nconst defaultRender = () => null;\nfunction isRenderResultPlainObject(result) {\n  return !!(result && ! /*#__PURE__*/React.isValidElement(result) && Object.prototype.toString.call(result) === '[object Object]');\n}\nfunction getEnabledItemKeys(items) {\n  return items.filter(data => !data.disabled).map(data => data.key);\n}\nconst isValidIcon = icon => icon !== undefined;\nconst getShowSearchOption = showSearch => {\n  if (showSearch && typeof showSearch === 'object') {\n    return Object.assign(Object.assign({}, showSearch), {\n      defaultValue: showSearch.defaultValue || ''\n    });\n  }\n  return {\n    defaultValue: '',\n    placeholder: ''\n  };\n};\nconst TransferList = props => {\n  const {\n    prefixCls,\n    dataSource = [],\n    titleText = '',\n    checkedKeys,\n    disabled,\n    showSearch = false,\n    style,\n    searchPlaceholder,\n    notFoundContent,\n    selectAll,\n    deselectAll,\n    selectCurrent,\n    selectInvert,\n    removeAll,\n    removeCurrent,\n    showSelectAll = true,\n    showRemove,\n    pagination,\n    direction,\n    itemsUnit,\n    itemUnit,\n    selectAllLabel,\n    selectionsIcon,\n    footer,\n    renderList,\n    onItemSelectAll,\n    onItemRemove,\n    handleFilter,\n    handleClear,\n    filterOption,\n    render = defaultRender\n  } = props;\n  const searchOptions = getShowSearchOption(showSearch);\n  const [filterValue, setFilterValue] = useState(searchOptions.defaultValue);\n  const listBodyRef = useRef({});\n  const internalHandleFilter = e => {\n    setFilterValue(e.target.value);\n    handleFilter(e);\n  };\n  const internalHandleClear = () => {\n    setFilterValue('');\n    handleClear();\n  };\n  const matchFilter = (text, item) => {\n    if (filterOption) {\n      return filterOption(filterValue, item, direction);\n    }\n    return text.includes(filterValue);\n  };\n  const renderListBody = listProps => {\n    let bodyContent = renderList ? renderList(Object.assign(Object.assign({}, listProps), {\n      onItemSelect: (key, check) => listProps.onItemSelect(key, check)\n    })) : null;\n    const customize = !!bodyContent;\n    if (!customize) {\n      // @ts-ignore\n      bodyContent = /*#__PURE__*/React.createElement(DefaultListBody, Object.assign({\n        ref: listBodyRef\n      }, listProps));\n    }\n    return {\n      customize,\n      bodyContent\n    };\n  };\n  const renderItem = item => {\n    const renderResult = render(item);\n    const isRenderResultPlain = isRenderResultPlainObject(renderResult);\n    return {\n      item,\n      renderedEl: isRenderResultPlain ? renderResult.label : renderResult,\n      renderedText: isRenderResultPlain ? renderResult.value : renderResult\n    };\n  };\n  const notFoundContentEle = useMemo(() => Array.isArray(notFoundContent) ? notFoundContent[direction === 'left' ? 0 : 1] : notFoundContent, [notFoundContent, direction]);\n  const [filteredItems, filteredRenderItems] = useMemo(() => {\n    const filterItems = [];\n    const filterRenderItems = [];\n    dataSource.forEach(item => {\n      const renderedItem = renderItem(item);\n      if (filterValue && !matchFilter(renderedItem.renderedText, item)) {\n        return;\n      }\n      filterItems.push(item);\n      filterRenderItems.push(renderedItem);\n    });\n    return [filterItems, filterRenderItems];\n  }, [dataSource, filterValue]);\n  const checkedActiveItems = useMemo(() => {\n    return filteredItems.filter(item => checkedKeys.includes(item.key) && !item.disabled);\n  }, [checkedKeys, filteredItems]);\n  const checkStatus = useMemo(() => {\n    if (checkedActiveItems.length === 0) {\n      return 'none';\n    }\n    const checkedKeysMap = groupKeysMap(checkedKeys);\n    if (filteredItems.every(item => checkedKeysMap.has(item.key) || !!item.disabled)) {\n      return 'all';\n    }\n    return 'part';\n  }, [checkedKeys, checkedActiveItems]);\n  const listBody = useMemo(() => {\n    const search = showSearch ? (/*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-body-search-wrapper`\n    }, /*#__PURE__*/React.createElement(Search, {\n      prefixCls: `${prefixCls}-search`,\n      onChange: internalHandleFilter,\n      handleClear: internalHandleClear,\n      placeholder: searchOptions.placeholder || searchPlaceholder,\n      value: filterValue,\n      disabled: disabled\n    }))) : null;\n    const {\n      customize,\n      bodyContent\n    } = renderListBody(Object.assign(Object.assign({}, omit(props, OmitProps)), {\n      filteredItems,\n      filteredRenderItems,\n      selectedKeys: checkedKeys\n    }));\n    let bodyNode;\n    // We should wrap customize list body in a classNamed div to use flex layout.\n    if (customize) {\n      bodyNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-body-customize-wrapper`\n      }, bodyContent);\n    } else {\n      bodyNode = filteredItems.length ? bodyContent : (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-body-not-found`\n      }, notFoundContentEle));\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(`${prefixCls}-body`, {\n        [`${prefixCls}-body-with-search`]: showSearch\n      })\n    }, search, bodyNode);\n  }, [showSearch, prefixCls, searchPlaceholder, filterValue, disabled, checkedKeys, filteredItems, filteredRenderItems, notFoundContentEle]);\n  const checkBox = /*#__PURE__*/React.createElement(Checkbox, {\n    disabled: dataSource.filter(d => !d.disabled).length === 0 || disabled,\n    checked: checkStatus === 'all',\n    indeterminate: checkStatus === 'part',\n    className: `${prefixCls}-checkbox`,\n    onChange: () => {\n      // Only select enabled items\n      onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(filteredItems.filter(item => !item.disabled).map(({\n        key\n      }) => key), checkStatus !== 'all');\n    }\n  });\n  const getSelectAllLabel = (selectedCount, totalCount) => {\n    if (selectAllLabel) {\n      return typeof selectAllLabel === 'function' ? selectAllLabel({\n        selectedCount,\n        totalCount\n      }) : selectAllLabel;\n    }\n    const unit = totalCount > 1 ? itemsUnit : itemUnit;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, (selectedCount > 0 ? `${selectedCount}/` : '') + totalCount, \" \", unit);\n  };\n  // Custom Layout\n  const footerDom = footer && (footer.length < 2 ? footer(props) : footer(props, {\n    direction\n  }));\n  const listCls = classNames(prefixCls, {\n    [`${prefixCls}-with-pagination`]: !!pagination,\n    [`${prefixCls}-with-footer`]: !!footerDom\n  });\n  // ====================== Get filtered, checked item list ======================\n  const listFooter = footerDom ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-footer`\n  }, footerDom) : null;\n  const checkAllCheckbox = !showRemove && !pagination && checkBox;\n  let items;\n  if (showRemove) {\n    items = [/* Remove Current Page */\n    pagination ? {\n      key: 'removeCurrent',\n      label: removeCurrent,\n      onClick() {\n        var _a;\n        const pageKeys = getEnabledItemKeys((((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || []).map(entity => entity.item));\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(pageKeys);\n      }\n    } : null, /* Remove All */\n    {\n      key: 'removeAll',\n      label: removeAll,\n      onClick() {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(getEnabledItemKeys(filteredItems));\n      }\n    }].filter(Boolean);\n  } else {\n    items = [{\n      key: 'selectAll',\n      label: checkStatus === 'all' ? deselectAll : selectAll,\n      onClick() {\n        const keys = getEnabledItemKeys(filteredItems);\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(keys, keys.length !== checkedKeys.length);\n      }\n    }, pagination ? {\n      key: 'selectCurrent',\n      label: selectCurrent,\n      onClick() {\n        var _a;\n        const pageItems = ((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || [];\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(getEnabledItemKeys(pageItems.map(entity => entity.item)), true);\n      }\n    } : null, {\n      key: 'selectInvert',\n      label: selectInvert,\n      onClick() {\n        var _a;\n        const availablePageItemKeys = getEnabledItemKeys((((_a = listBodyRef.current) === null || _a === void 0 ? void 0 : _a.items) || []).map(entity => entity.item));\n        const checkedKeySet = new Set(checkedKeys);\n        const newCheckedKeysSet = new Set(checkedKeySet);\n        availablePageItemKeys.forEach(key => {\n          if (checkedKeySet.has(key)) {\n            newCheckedKeysSet.delete(key);\n          } else {\n            newCheckedKeysSet.add(key);\n          }\n        });\n        onItemSelectAll === null || onItemSelectAll === void 0 ? void 0 : onItemSelectAll(Array.from(newCheckedKeysSet), 'replace');\n      }\n    }];\n  }\n  const dropdown = /*#__PURE__*/React.createElement(Dropdown, {\n    className: `${prefixCls}-header-dropdown`,\n    menu: {\n      items\n    },\n    disabled: disabled\n  }, isValidIcon(selectionsIcon) ? selectionsIcon : /*#__PURE__*/React.createElement(DownOutlined, null));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: listCls,\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`\n  }, showSelectAll ? (/*#__PURE__*/React.createElement(React.Fragment, null, checkAllCheckbox, dropdown)) : null, /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-header-selected`\n  }, getSelectAllLabel(checkedActiveItems.length, filteredItems.length)), /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-header-title`\n  }, titleText)), listBody, listFooter);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TransferList.displayName = 'TransferList';\n}\nexport default TransferList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,eAAe,IAAIC,SAAS,QAAQ,YAAY;AACvD,OAAOC,MAAM,MAAM,UAAU;AAC7B,MAAMC,aAAa,GAAGA,CAAA,KAAM,IAAI;AAChC,SAASC,yBAAyBA,CAACC,MAAM,EAAE;EACzC,OAAO,CAAC,EAAEA,MAAM,IAAI,EAAE,aAAaf,KAAK,CAACgB,cAAc,CAACD,MAAM,CAAC,IAAIE,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,MAAM,CAAC,KAAK,iBAAiB,CAAC;AAClI;AACA,SAASM,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAOA,KAAK,CAACC,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC,CAACC,GAAG,CAACF,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAC;AACnE;AACA,MAAMC,WAAW,GAAGC,IAAI,IAAIA,IAAI,KAAKC,SAAS;AAC9C,MAAMC,mBAAmB,GAAGC,UAAU,IAAI;EACxC,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAChD,OAAOf,MAAM,CAACgB,MAAM,CAAChB,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAED,UAAU,CAAC,EAAE;MAClDE,YAAY,EAAEF,UAAU,CAACE,YAAY,IAAI;IAC3C,CAAC,CAAC;EACJ;EACA,OAAO;IACLA,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC;AACH,CAAC;AACD,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,SAAS;IACTC,UAAU,GAAG,EAAE;IACfC,SAAS,GAAG,EAAE;IACdC,WAAW;IACXhB,QAAQ;IACRO,UAAU,GAAG,KAAK;IAClBU,KAAK;IACLC,iBAAiB;IACjBC,eAAe;IACfC,SAAS;IACTC,WAAW;IACXC,aAAa;IACbC,YAAY;IACZC,SAAS;IACTC,aAAa;IACbC,aAAa,GAAG,IAAI;IACpBC,UAAU;IACVC,UAAU;IACVC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,cAAc;IACdC,cAAc;IACdC,MAAM;IACNC,UAAU;IACVC,eAAe;IACfC,YAAY;IACZC,YAAY;IACZC,WAAW;IACXC,YAAY;IACZC,MAAM,GAAGrD;EACX,CAAC,GAAGwB,KAAK;EACT,MAAM8B,aAAa,GAAGpC,mBAAmB,CAACC,UAAU,CAAC;EACrD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAACgE,aAAa,CAACjC,YAAY,CAAC;EAC1E,MAAMoC,WAAW,GAAGpE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAMqE,oBAAoB,GAAGC,CAAC,IAAI;IAChCH,cAAc,CAACG,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC9BX,YAAY,CAACS,CAAC,CAAC;EACjB,CAAC;EACD,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCN,cAAc,CAAC,EAAE,CAAC;IAClBL,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMY,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClC,IAAIb,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACG,WAAW,EAAEU,IAAI,EAAExB,SAAS,CAAC;IACnD;IACA,OAAOuB,IAAI,CAACE,QAAQ,CAACX,WAAW,CAAC;EACnC,CAAC;EACD,MAAMY,cAAc,GAAGC,SAAS,IAAI;IAClC,IAAIC,WAAW,GAAGtB,UAAU,GAAGA,UAAU,CAAC3C,MAAM,CAACgB,MAAM,CAAChB,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC,EAAE;MACpFE,YAAY,EAAEA,CAACxD,GAAG,EAAEyD,KAAK,KAAKH,SAAS,CAACE,YAAY,CAACxD,GAAG,EAAEyD,KAAK;IACjE,CAAC,CAAC,CAAC,GAAG,IAAI;IACV,MAAMC,SAAS,GAAG,CAAC,CAACH,WAAW;IAC/B,IAAI,CAACG,SAAS,EAAE;MACd;MACAH,WAAW,GAAG,aAAalF,KAAK,CAACsF,aAAa,CAAC5E,eAAe,EAAEO,MAAM,CAACgB,MAAM,CAAC;QAC5EsD,GAAG,EAAEjB;MACP,CAAC,EAAEW,SAAS,CAAC,CAAC;IAChB;IACA,OAAO;MACLI,SAAS;MACTH;IACF,CAAC;EACH,CAAC;EACD,MAAMM,UAAU,GAAGV,IAAI,IAAI;IACzB,MAAMW,YAAY,GAAGvB,MAAM,CAACY,IAAI,CAAC;IACjC,MAAMY,mBAAmB,GAAG5E,yBAAyB,CAAC2E,YAAY,CAAC;IACnE,OAAO;MACLX,IAAI;MACJa,UAAU,EAAED,mBAAmB,GAAGD,YAAY,CAACG,KAAK,GAAGH,YAAY;MACnEI,YAAY,EAAEH,mBAAmB,GAAGD,YAAY,CAACf,KAAK,GAAGe;IAC3D,CAAC;EACH,CAAC;EACD,MAAMK,kBAAkB,GAAG7F,OAAO,CAAC,MAAM8F,KAAK,CAACC,OAAO,CAACpD,eAAe,CAAC,GAAGA,eAAe,CAACU,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGV,eAAe,EAAE,CAACA,eAAe,EAAEU,SAAS,CAAC,CAAC;EACxK,MAAM,CAAC2C,aAAa,EAAEC,mBAAmB,CAAC,GAAGjG,OAAO,CAAC,MAAM;IACzD,MAAMkG,WAAW,GAAG,EAAE;IACtB,MAAMC,iBAAiB,GAAG,EAAE;IAC5B7D,UAAU,CAAC8D,OAAO,CAACvB,IAAI,IAAI;MACzB,MAAMwB,YAAY,GAAGd,UAAU,CAACV,IAAI,CAAC;MACrC,IAAIV,WAAW,IAAI,CAACQ,WAAW,CAAC0B,YAAY,CAACT,YAAY,EAAEf,IAAI,CAAC,EAAE;QAChE;MACF;MACAqB,WAAW,CAACI,IAAI,CAACzB,IAAI,CAAC;MACtBsB,iBAAiB,CAACG,IAAI,CAACD,YAAY,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,CAACH,WAAW,EAAEC,iBAAiB,CAAC;EACzC,CAAC,EAAE,CAAC7D,UAAU,EAAE6B,WAAW,CAAC,CAAC;EAC7B,MAAMoC,kBAAkB,GAAGvG,OAAO,CAAC,MAAM;IACvC,OAAOgG,aAAa,CAAC1E,MAAM,CAACuD,IAAI,IAAIrC,WAAW,CAACsC,QAAQ,CAACD,IAAI,CAACnD,GAAG,CAAC,IAAI,CAACmD,IAAI,CAACrD,QAAQ,CAAC;EACvF,CAAC,EAAE,CAACgB,WAAW,EAAEwD,aAAa,CAAC,CAAC;EAChC,MAAMQ,WAAW,GAAGxG,OAAO,CAAC,MAAM;IAChC,IAAIuG,kBAAkB,CAACE,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,MAAM;IACf;IACA,MAAMC,cAAc,GAAGpG,YAAY,CAACkC,WAAW,CAAC;IAChD,IAAIwD,aAAa,CAACW,KAAK,CAAC9B,IAAI,IAAI6B,cAAc,CAACE,GAAG,CAAC/B,IAAI,CAACnD,GAAG,CAAC,IAAI,CAAC,CAACmD,IAAI,CAACrD,QAAQ,CAAC,EAAE;MAChF,OAAO,KAAK;IACd;IACA,OAAO,MAAM;EACf,CAAC,EAAE,CAACgB,WAAW,EAAE+D,kBAAkB,CAAC,CAAC;EACrC,MAAMM,QAAQ,GAAG7G,OAAO,CAAC,MAAM;IAC7B,MAAM8G,MAAM,GAAG/E,UAAU,IAAI,aAAahC,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MACnE0B,SAAS,EAAE,GAAG1E,SAAS;IACzB,CAAC,EAAE,aAAatC,KAAK,CAACsF,aAAa,CAAC1E,MAAM,EAAE;MAC1C0B,SAAS,EAAE,GAAGA,SAAS,SAAS;MAChC2E,QAAQ,EAAE1C,oBAAoB;MAC9BP,WAAW,EAAEW,mBAAmB;MAChCxC,WAAW,EAAEgC,aAAa,CAAChC,WAAW,IAAIQ,iBAAiB;MAC3D+B,KAAK,EAAEN,WAAW;MAClB3C,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC,IAAI,IAAI;IACX,MAAM;MACJ4D,SAAS;MACTH;IACF,CAAC,GAAGF,cAAc,CAAC/D,MAAM,CAACgB,MAAM,CAAChB,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAE3B,IAAI,CAAC+B,KAAK,EAAE1B,SAAS,CAAC,CAAC,EAAE;MAC1EsF,aAAa;MACbC,mBAAmB;MACnBgB,YAAY,EAAEzE;IAChB,CAAC,CAAC,CAAC;IACH,IAAI0E,QAAQ;IACZ;IACA,IAAI9B,SAAS,EAAE;MACb8B,QAAQ,GAAG,aAAanH,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;QACjD0B,SAAS,EAAE,GAAG1E,SAAS;MACzB,CAAC,EAAE4C,WAAW,CAAC;IACjB,CAAC,MAAM;MACLiC,QAAQ,GAAGlB,aAAa,CAACS,MAAM,GAAGxB,WAAW,IAAI,aAAalF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;QACvF0B,SAAS,EAAE,GAAG1E,SAAS;MACzB,CAAC,EAAEwD,kBAAkB,CAAC,CAAC;IACzB;IACA,OAAO,aAAa9F,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAC7C0B,SAAS,EAAE3G,UAAU,CAAC,GAAGiC,SAAS,OAAO,EAAE;QACzC,CAAC,GAAGA,SAAS,mBAAmB,GAAGN;MACrC,CAAC;IACH,CAAC,EAAE+E,MAAM,EAAEI,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACnF,UAAU,EAAEM,SAAS,EAAEK,iBAAiB,EAAEyB,WAAW,EAAE3C,QAAQ,EAAEgB,WAAW,EAAEwD,aAAa,EAAEC,mBAAmB,EAAEJ,kBAAkB,CAAC,CAAC;EAC1I,MAAMsB,QAAQ,GAAG,aAAapH,KAAK,CAACsF,aAAa,CAAC9E,QAAQ,EAAE;IAC1DiB,QAAQ,EAAEc,UAAU,CAAChB,MAAM,CAAC8F,CAAC,IAAI,CAACA,CAAC,CAAC5F,QAAQ,CAAC,CAACiF,MAAM,KAAK,CAAC,IAAIjF,QAAQ;IACtE6F,OAAO,EAAEb,WAAW,KAAK,KAAK;IAC9Bc,aAAa,EAAEd,WAAW,KAAK,MAAM;IACrCO,SAAS,EAAE,GAAG1E,SAAS,WAAW;IAClC2E,QAAQ,EAAEA,CAAA,KAAM;MACd;MACApD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACoC,aAAa,CAAC1E,MAAM,CAACuD,IAAI,IAAI,CAACA,IAAI,CAACrD,QAAQ,CAAC,CAACC,GAAG,CAAC,CAAC;QAClIC;MACF,CAAC,KAAKA,GAAG,CAAC,EAAE8E,WAAW,KAAK,KAAK,CAAC;IACpC;EACF,CAAC,CAAC;EACF,MAAMe,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,UAAU,KAAK;IACvD,IAAIjE,cAAc,EAAE;MAClB,OAAO,OAAOA,cAAc,KAAK,UAAU,GAAGA,cAAc,CAAC;QAC3DgE,aAAa;QACbC;MACF,CAAC,CAAC,GAAGjE,cAAc;IACrB;IACA,MAAMkE,IAAI,GAAGD,UAAU,GAAG,CAAC,GAAGnE,SAAS,GAAGC,QAAQ;IAClD,OAAO,aAAaxD,KAAK,CAACsF,aAAa,CAACtF,KAAK,CAAC4H,QAAQ,EAAE,IAAI,EAAE,CAACH,aAAa,GAAG,CAAC,GAAG,GAAGA,aAAa,GAAG,GAAG,EAAE,IAAIC,UAAU,EAAE,GAAG,EAAEC,IAAI,CAAC;EACvI,CAAC;EACD;EACA,MAAME,SAAS,GAAGlE,MAAM,KAAKA,MAAM,CAAC+C,MAAM,GAAG,CAAC,GAAG/C,MAAM,CAACtB,KAAK,CAAC,GAAGsB,MAAM,CAACtB,KAAK,EAAE;IAC7EiB;EACF,CAAC,CAAC,CAAC;EACH,MAAMwE,OAAO,GAAGzH,UAAU,CAACiC,SAAS,EAAE;IACpC,CAAC,GAAGA,SAAS,kBAAkB,GAAG,CAAC,CAACe,UAAU;IAC9C,CAAC,GAAGf,SAAS,cAAc,GAAG,CAAC,CAACuF;EAClC,CAAC,CAAC;EACF;EACA,MAAME,UAAU,GAAGF,SAAS,GAAG,aAAa7H,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IACrE0B,SAAS,EAAE,GAAG1E,SAAS;EACzB,CAAC,EAAEuF,SAAS,CAAC,GAAG,IAAI;EACpB,MAAMG,gBAAgB,GAAG,CAAC5E,UAAU,IAAI,CAACC,UAAU,IAAI+D,QAAQ;EAC/D,IAAI9F,KAAK;EACT,IAAI8B,UAAU,EAAE;IACd9B,KAAK,GAAG,CAAC;IACT+B,UAAU,GAAG;MACX1B,GAAG,EAAE,eAAe;MACpBiE,KAAK,EAAE1C,aAAa;MACpB+E,OAAOA,CAAA,EAAG;QACR,IAAIC,EAAE;QACN,MAAMC,QAAQ,GAAG9G,kBAAkB,CAAC,CAAC,CAAC,CAAC6G,EAAE,GAAG5D,WAAW,CAAC8D,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5G,KAAK,KAAK,EAAE,EAAEI,GAAG,CAAC2G,MAAM,IAAIA,MAAM,CAACvD,IAAI,CAAC,CAAC;QAClJhB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACqE,QAAQ,CAAC;MACpF;IACF,CAAC,GAAG,IAAI,EAAE;IACV;MACExG,GAAG,EAAE,WAAW;MAChBiE,KAAK,EAAE3C,SAAS;MAChBgF,OAAOA,CAAA,EAAG;QACRnE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACzC,kBAAkB,CAAC4E,aAAa,CAAC,CAAC;MAC7G;IACF,CAAC,CAAC,CAAC1E,MAAM,CAAC+G,OAAO,CAAC;EACpB,CAAC,MAAM;IACLhH,KAAK,GAAG,CAAC;MACPK,GAAG,EAAE,WAAW;MAChBiE,KAAK,EAAEa,WAAW,KAAK,KAAK,GAAG3D,WAAW,GAAGD,SAAS;MACtDoF,OAAOA,CAAA,EAAG;QACR,MAAMM,IAAI,GAAGlH,kBAAkB,CAAC4E,aAAa,CAAC;QAC9CpC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC0E,IAAI,EAAEA,IAAI,CAAC7B,MAAM,KAAKjE,WAAW,CAACiE,MAAM,CAAC;MAC7H;IACF,CAAC,EAAErD,UAAU,GAAG;MACd1B,GAAG,EAAE,eAAe;MACpBiE,KAAK,EAAE7C,aAAa;MACpBkF,OAAOA,CAAA,EAAG;QACR,IAAIC,EAAE;QACN,MAAMM,SAAS,GAAG,CAAC,CAACN,EAAE,GAAG5D,WAAW,CAAC8D,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5G,KAAK,KAAK,EAAE;QAClGuC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACxC,kBAAkB,CAACmH,SAAS,CAAC9G,GAAG,CAAC2G,MAAM,IAAIA,MAAM,CAACvD,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;MACnJ;IACF,CAAC,GAAG,IAAI,EAAE;MACRnD,GAAG,EAAE,cAAc;MACnBiE,KAAK,EAAE5C,YAAY;MACnBiF,OAAOA,CAAA,EAAG;QACR,IAAIC,EAAE;QACN,MAAMO,qBAAqB,GAAGpH,kBAAkB,CAAC,CAAC,CAAC,CAAC6G,EAAE,GAAG5D,WAAW,CAAC8D,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5G,KAAK,KAAK,EAAE,EAAEI,GAAG,CAAC2G,MAAM,IAAIA,MAAM,CAACvD,IAAI,CAAC,CAAC;QAC/J,MAAM4D,aAAa,GAAG,IAAIC,GAAG,CAAClG,WAAW,CAAC;QAC1C,MAAMmG,iBAAiB,GAAG,IAAID,GAAG,CAACD,aAAa,CAAC;QAChDD,qBAAqB,CAACpC,OAAO,CAAC1E,GAAG,IAAI;UACnC,IAAI+G,aAAa,CAAC7B,GAAG,CAAClF,GAAG,CAAC,EAAE;YAC1BiH,iBAAiB,CAACC,MAAM,CAAClH,GAAG,CAAC;UAC/B,CAAC,MAAM;YACLiH,iBAAiB,CAACE,GAAG,CAACnH,GAAG,CAAC;UAC5B;QACF,CAAC,CAAC;QACFkC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACkC,KAAK,CAACgD,IAAI,CAACH,iBAAiB,CAAC,EAAE,SAAS,CAAC;MAC7H;IACF,CAAC,CAAC;EACJ;EACA,MAAMI,QAAQ,GAAG,aAAahJ,KAAK,CAACsF,aAAa,CAAC7E,QAAQ,EAAE;IAC1DuG,SAAS,EAAE,GAAG1E,SAAS,kBAAkB;IACzC2G,IAAI,EAAE;MACJ3H;IACF,CAAC;IACDG,QAAQ,EAAEA;EACZ,CAAC,EAAEG,WAAW,CAAC8B,cAAc,CAAC,GAAGA,cAAc,GAAG,aAAa1D,KAAK,CAACsF,aAAa,CAAClF,YAAY,EAAE,IAAI,CAAC,CAAC;EACvG,OAAO,aAAaJ,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IAC7C0B,SAAS,EAAEc,OAAO;IAClBpF,KAAK,EAAEA;EACT,CAAC,EAAE,aAAa1C,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IACzC0B,SAAS,EAAE,GAAG1E,SAAS;EACzB,CAAC,EAAEa,aAAa,IAAI,aAAanD,KAAK,CAACsF,aAAa,CAACtF,KAAK,CAAC4H,QAAQ,EAAE,IAAI,EAAEI,gBAAgB,EAAEgB,QAAQ,CAAC,IAAI,IAAI,EAAE,aAAahJ,KAAK,CAACsF,aAAa,CAAC,MAAM,EAAE;IACvJ0B,SAAS,EAAE,GAAG1E,SAAS;EACzB,CAAC,EAAEkF,iBAAiB,CAAChB,kBAAkB,CAACE,MAAM,EAAET,aAAa,CAACS,MAAM,CAAC,CAAC,EAAE,aAAa1G,KAAK,CAACsF,aAAa,CAAC,MAAM,EAAE;IAC/G0B,SAAS,EAAE,GAAG1E,SAAS;EACzB,CAAC,EAAEE,SAAS,CAAC,CAAC,EAAEsE,QAAQ,EAAEiB,UAAU,CAAC;AACvC,CAAC;AACD,IAAImB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChH,YAAY,CAACiH,WAAW,GAAG,cAAc;AAC3C;AACA,eAAejH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}