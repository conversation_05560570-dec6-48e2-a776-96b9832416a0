{"ast": null, "code": "import * as React from 'react';\nimport { getStyleStr } from './utils';\n/**\n * Base size of the canvas, 1 for parallel layout and 2 for alternate layout\n * Only alternate layout is currently supported\n */\nexport const BaseSize = 2;\nexport const FontGap = 3;\n// Prevent external hidden elements from adding accent styles\nconst emphasizedStyle = {\n  visibility: 'visible !important'\n};\nexport default function useWatermark(markStyle) {\n  const watermarkMap = React.useRef(new Map());\n  const appendWatermark = (base64Url, markWidth, container) => {\n    if (container) {\n      if (!watermarkMap.current.get(container)) {\n        const newWatermarkEle = document.createElement('div');\n        watermarkMap.current.set(container, newWatermarkEle);\n      }\n      const watermarkEle = watermarkMap.current.get(container);\n      watermarkEle.setAttribute('style', getStyleStr(Object.assign(Object.assign(Object.assign({}, markStyle), {\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`\n      }), emphasizedStyle)));\n      // Prevents using the browser `Hide Element` to hide watermarks\n      watermarkEle.removeAttribute('class');\n      watermarkEle.removeAttribute('hidden');\n      if (watermarkEle.parentElement !== container) {\n        container.append(watermarkEle);\n      }\n    }\n    return watermarkMap.current.get(container);\n  };\n  const removeWatermark = container => {\n    const watermarkEle = watermarkMap.current.get(container);\n    if (watermarkEle && container) {\n      container.removeChild(watermarkEle);\n    }\n    watermarkMap.current.delete(container);\n  };\n  const isWatermarkEle = ele => Array.from(watermarkMap.current.values()).includes(ele);\n  return [appendWatermark, removeWatermark, isWatermarkEle];\n}", "map": {"version": 3, "names": ["React", "getStyleStr", "BaseSize", "FontGap", "emphasizedStyle", "visibility", "useWatermark", "mark<PERSON><PERSON><PERSON>", "watermarkMap", "useRef", "Map", "appendWatermark", "base64Url", "<PERSON><PERSON><PERSON><PERSON>", "container", "current", "get", "newWatermarkEle", "document", "createElement", "set", "watermarkEle", "setAttribute", "Object", "assign", "backgroundImage", "backgroundSize", "Math", "floor", "removeAttribute", "parentElement", "append", "removeWatermark", "<PERSON><PERSON><PERSON><PERSON>", "delete", "isWatermarkEle", "ele", "Array", "from", "values", "includes"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/watermark/useWatermark.js"], "sourcesContent": ["import * as React from 'react';\nimport { getStyleStr } from './utils';\n/**\n * Base size of the canvas, 1 for parallel layout and 2 for alternate layout\n * Only alternate layout is currently supported\n */\nexport const BaseSize = 2;\nexport const FontGap = 3;\n// Prevent external hidden elements from adding accent styles\nconst emphasizedStyle = {\n  visibility: 'visible !important'\n};\nexport default function useWatermark(markStyle) {\n  const watermarkMap = React.useRef(new Map());\n  const appendWatermark = (base64Url, markWidth, container) => {\n    if (container) {\n      if (!watermarkMap.current.get(container)) {\n        const newWatermarkEle = document.createElement('div');\n        watermarkMap.current.set(container, newWatermarkEle);\n      }\n      const watermarkEle = watermarkMap.current.get(container);\n      watermarkEle.setAttribute('style', getStyleStr(Object.assign(Object.assign(Object.assign({}, markStyle), {\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`\n      }), emphasizedStyle)));\n      // Prevents using the browser `Hide Element` to hide watermarks\n      watermarkEle.removeAttribute('class');\n      watermarkEle.removeAttribute('hidden');\n      if (watermarkEle.parentElement !== container) {\n        container.append(watermarkEle);\n      }\n    }\n    return watermarkMap.current.get(container);\n  };\n  const removeWatermark = container => {\n    const watermarkEle = watermarkMap.current.get(container);\n    if (watermarkEle && container) {\n      container.removeChild(watermarkEle);\n    }\n    watermarkMap.current.delete(container);\n  };\n  const isWatermarkEle = ele => Array.from(watermarkMap.current.values()).includes(ele);\n  return [appendWatermark, removeWatermark, isWatermarkEle];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,SAAS;AACrC;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG,CAAC;AACzB,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB;AACA,MAAMC,eAAe,GAAG;EACtBC,UAAU,EAAE;AACd,CAAC;AACD,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9C,MAAMC,YAAY,GAAGR,KAAK,CAACS,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAC5C,MAAMC,eAAe,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,SAAS,KAAK;IAC3D,IAAIA,SAAS,EAAE;MACb,IAAI,CAACN,YAAY,CAACO,OAAO,CAACC,GAAG,CAACF,SAAS,CAAC,EAAE;QACxC,MAAMG,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACrDX,YAAY,CAACO,OAAO,CAACK,GAAG,CAACN,SAAS,EAAEG,eAAe,CAAC;MACtD;MACA,MAAMI,YAAY,GAAGb,YAAY,CAACO,OAAO,CAACC,GAAG,CAACF,SAAS,CAAC;MACxDO,YAAY,CAACC,YAAY,CAAC,OAAO,EAAErB,WAAW,CAACsB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,SAAS,CAAC,EAAE;QACvGkB,eAAe,EAAE,QAAQb,SAAS,IAAI;QACtCc,cAAc,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACf,SAAS,CAAC;MAC1C,CAAC,CAAC,EAAET,eAAe,CAAC,CAAC,CAAC;MACtB;MACAiB,YAAY,CAACQ,eAAe,CAAC,OAAO,CAAC;MACrCR,YAAY,CAACQ,eAAe,CAAC,QAAQ,CAAC;MACtC,IAAIR,YAAY,CAACS,aAAa,KAAKhB,SAAS,EAAE;QAC5CA,SAAS,CAACiB,MAAM,CAACV,YAAY,CAAC;MAChC;IACF;IACA,OAAOb,YAAY,CAACO,OAAO,CAACC,GAAG,CAACF,SAAS,CAAC;EAC5C,CAAC;EACD,MAAMkB,eAAe,GAAGlB,SAAS,IAAI;IACnC,MAAMO,YAAY,GAAGb,YAAY,CAACO,OAAO,CAACC,GAAG,CAACF,SAAS,CAAC;IACxD,IAAIO,YAAY,IAAIP,SAAS,EAAE;MAC7BA,SAAS,CAACmB,WAAW,CAACZ,YAAY,CAAC;IACrC;IACAb,YAAY,CAACO,OAAO,CAACmB,MAAM,CAACpB,SAAS,CAAC;EACxC,CAAC;EACD,MAAMqB,cAAc,GAAGC,GAAG,IAAIC,KAAK,CAACC,IAAI,CAAC9B,YAAY,CAACO,OAAO,CAACwB,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACJ,GAAG,CAAC;EACrF,OAAO,CAACzB,eAAe,EAAEqB,eAAe,EAAEG,cAAc,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}