{"ast": null, "code": "\"use client\";\n\nimport { TreeNode } from 'rc-tree';\nimport DirectoryTree from './DirectoryTree';\nimport TreePure from './Tree';\nconst Tree = TreePure;\nTree.DirectoryTree = DirectoryTree;\nTree.TreeNode = TreeNode;\nexport default Tree;", "map": {"version": 3, "names": ["TreeNode", "DirectoryTree", "TreePure", "Tree"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/tree/index.js"], "sourcesContent": ["\"use client\";\n\nimport { TreeNode } from 'rc-tree';\nimport DirectoryTree from './DirectoryTree';\nimport TreePure from './Tree';\nconst Tree = TreePure;\nTree.DirectoryTree = DirectoryTree;\nTree.TreeNode = TreeNode;\nexport default Tree;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,SAAS;AAClC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,QAAQ;AAC7B,MAAMC,IAAI,GAAGD,QAAQ;AACrBC,IAAI,CAACF,aAAa,GAAGA,aAAa;AAClCE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;AACxB,eAAeG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}