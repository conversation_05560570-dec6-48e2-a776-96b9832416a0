{"ast": null, "code": "import classNames from 'classnames';\nconst _InputStatuses = ['warning', 'error', ''];\nexport function getStatusClassNames(prefixCls, status, hasFeedback) {\n  return classNames({\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  });\n}\nexport const getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;", "map": {"version": 3, "names": ["classNames", "_InputStatuses", "getStatusClassNames", "prefixCls", "status", "hasFeedback", "getMergedStatus", "contextStatus", "customStatus"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/statusUtils.js"], "sourcesContent": ["import classNames from 'classnames';\nconst _InputStatuses = ['warning', 'error', ''];\nexport function getStatusClassNames(prefixCls, status, hasFeedback) {\n  return classNames({\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  });\n}\nexport const getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,MAAMC,cAAc,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC;AAC/C,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EAClE,OAAOL,UAAU,CAAC;IAChB,CAAC,GAAGG,SAAS,iBAAiB,GAAGC,MAAM,KAAK,SAAS;IACrD,CAAC,GAAGD,SAAS,iBAAiB,GAAGC,MAAM,KAAK,SAAS;IACrD,CAAC,GAAGD,SAAS,eAAe,GAAGC,MAAM,KAAK,OAAO;IACjD,CAAC,GAAGD,SAAS,oBAAoB,GAAGC,MAAM,KAAK,YAAY;IAC3D,CAAC,GAAGD,SAAS,eAAe,GAAGE;EACjC,CAAC,CAAC;AACJ;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAKA,YAAY,IAAID,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}