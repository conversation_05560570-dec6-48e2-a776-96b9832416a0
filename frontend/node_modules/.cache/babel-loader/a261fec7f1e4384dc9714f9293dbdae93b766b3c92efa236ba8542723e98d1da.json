{"ast": null, "code": "import { createContext } from 'react';\n/** @private Internal context. Do not use in your production. */\nconst SliderInternalContext = /*#__PURE__*/createContext({});\nexport default SliderInternalContext;", "map": {"version": 3, "names": ["createContext", "SliderInternalContext"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/slider/Context.js"], "sourcesContent": ["import { createContext } from 'react';\n/** @private Internal context. Do not use in your production. */\nconst SliderInternalContext = /*#__PURE__*/createContext({});\nexport default SliderInternalContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC;AACA,MAAMC,qBAAqB,GAAG,aAAaD,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5D,eAAeC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}