{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Base from './Base';\nconst Paragraph = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(Base, Object.assign({\n  ref: ref\n}, props, {\n  component: \"div\"\n}))));\nexport default Paragraph;", "map": {"version": 3, "names": ["React", "Base", "Paragraph", "forwardRef", "props", "ref", "createElement", "Object", "assign", "component"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/typography/Paragraph.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport Base from './Base';\nconst Paragraph = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(Base, Object.assign({\n  ref: ref\n}, props, {\n  component: \"div\"\n}))));\nexport default Paragraph;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,MAAMC,SAAS,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,MAAM,aAAaL,KAAK,CAACM,aAAa,CAACL,IAAI,EAAEM,MAAM,CAACC,MAAM,CAAC;EACpHH,GAAG,EAAEA;AACP,CAAC,EAAED,KAAK,EAAE;EACRK,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}