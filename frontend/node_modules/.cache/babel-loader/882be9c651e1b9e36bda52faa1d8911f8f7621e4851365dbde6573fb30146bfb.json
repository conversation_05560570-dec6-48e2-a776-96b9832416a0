{"ast": null, "code": "import { prepareComponentToken, prepareToken } from '.';\nimport capitalize from '../../_util/capitalize';\nimport { genSubStyleComponent } from '../../theme/internal';\nconst genTagStatusStyle = (token, status, cssVariableType) => {\n  const capitalizedCssVariableType = capitalize(cssVariableType);\n  return {\n    [`${token.componentCls}${token.componentCls}-${status}`]: {\n      color: token[`color${cssVariableType}`],\n      background: token[`color${capitalizedCssVariableType}Bg`],\n      borderColor: token[`color${capitalizedCssVariableType}Border`],\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'status'], token => {\n  const tagToken = prepareToken(token);\n  return [genTagStatusStyle(tagToken, 'success', 'Success'), genTagStatusStyle(tagToken, 'processing', 'Info'), genTagStatusStyle(tagToken, 'error', 'Error'), genTagStatusStyle(tagToken, 'warning', 'Warning')];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["prepareComponentToken", "prepareToken", "capitalize", "genSubStyleComponent", "genTagStatusStyle", "token", "status", "cssVariableType", "capitalizedCssVariableType", "componentCls", "color", "background", "borderColor", "tagToken"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/tag/style/statusCmp.js"], "sourcesContent": ["import { prepareComponentToken, prepareToken } from '.';\nimport capitalize from '../../_util/capitalize';\nimport { genSubStyleComponent } from '../../theme/internal';\nconst genTagStatusStyle = (token, status, cssVariableType) => {\n  const capitalizedCssVariableType = capitalize(cssVariableType);\n  return {\n    [`${token.componentCls}${token.componentCls}-${status}`]: {\n      color: token[`color${cssVariableType}`],\n      background: token[`color${capitalizedCssVariableType}Bg`],\n      borderColor: token[`color${capitalizedCssVariableType}Border`],\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'status'], token => {\n  const tagToken = prepareToken(token);\n  return [genTagStatusStyle(tagToken, 'success', 'Success'), genTagStatusStyle(tagToken, 'processing', 'Info'), genTagStatusStyle(tagToken, 'error', 'Error'), genTagStatusStyle(tagToken, 'warning', 'Warning')];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,YAAY,QAAQ,GAAG;AACvD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEC,eAAe,KAAK;EAC5D,MAAMC,0BAA0B,GAAGN,UAAU,CAACK,eAAe,CAAC;EAC9D,OAAO;IACL,CAAC,GAAGF,KAAK,CAACI,YAAY,GAAGJ,KAAK,CAACI,YAAY,IAAIH,MAAM,EAAE,GAAG;MACxDI,KAAK,EAAEL,KAAK,CAAC,QAAQE,eAAe,EAAE,CAAC;MACvCI,UAAU,EAAEN,KAAK,CAAC,QAAQG,0BAA0B,IAAI,CAAC;MACzDI,WAAW,EAAEP,KAAK,CAAC,QAAQG,0BAA0B,QAAQ,CAAC;MAC9D,CAAC,IAAIH,KAAK,CAACI,YAAY,aAAa,GAAG;QACrCG,WAAW,EAAE;MACf;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAeT,oBAAoB,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAEE,KAAK,IAAI;EAC9D,MAAMQ,QAAQ,GAAGZ,YAAY,CAACI,KAAK,CAAC;EACpC,OAAO,CAACD,iBAAiB,CAACS,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,EAAET,iBAAiB,CAACS,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,EAAET,iBAAiB,CAACS,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,EAAET,iBAAiB,CAACS,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACjN,CAAC,EAAEb,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}