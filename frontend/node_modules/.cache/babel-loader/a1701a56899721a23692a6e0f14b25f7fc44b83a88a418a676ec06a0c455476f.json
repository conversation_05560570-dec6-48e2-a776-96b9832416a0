{"ast": null, "code": "\"use client\";\n\nimport { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport useResponsiveObserver from '../../_util/responsiveObserver';\nfunction useBreakpoint(refreshOnChange = true, defaultScreens = {}) {\n  const screensRef = useRef(defaultScreens);\n  const forceUpdate = useForceUpdate();\n  const responsiveObserver = useResponsiveObserver();\n  useLayoutEffect(() => {\n    const token = responsiveObserver.subscribe(supportScreens => {\n      screensRef.current = supportScreens;\n      if (refreshOnChange) {\n        forceUpdate();\n      }\n    });\n    return () => responsiveObserver.unsubscribe(token);\n  }, []);\n  return screensRef.current;\n}\nexport default useBreakpoint;", "map": {"version": 3, "names": ["useRef", "useLayoutEffect", "useForceUpdate", "useResponsiveObserver", "useBreakpoint", "refreshOnChange", "defaultScreens", "screensRef", "forceUpdate", "responsiveObserver", "token", "subscribe", "supportScreens", "current", "unsubscribe"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/grid/hooks/useBreakpoint.js"], "sourcesContent": ["\"use client\";\n\nimport { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport useResponsiveObserver from '../../_util/responsiveObserver';\nfunction useBreakpoint(refreshOnChange = true, defaultScreens = {}) {\n  const screensRef = useRef(defaultScreens);\n  const forceUpdate = useForceUpdate();\n  const responsiveObserver = useResponsiveObserver();\n  useLayoutEffect(() => {\n    const token = responsiveObserver.subscribe(supportScreens => {\n      screensRef.current = supportScreens;\n      if (refreshOnChange) {\n        forceUpdate();\n      }\n    });\n    return () => responsiveObserver.unsubscribe(token);\n  }, []);\n  return screensRef.current;\n}\nexport default useBreakpoint;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,SAASC,aAAaA,CAACC,eAAe,GAAG,IAAI,EAAEC,cAAc,GAAG,CAAC,CAAC,EAAE;EAClE,MAAMC,UAAU,GAAGP,MAAM,CAACM,cAAc,CAAC;EACzC,MAAME,WAAW,GAAGN,cAAc,CAAC,CAAC;EACpC,MAAMO,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;EAClDF,eAAe,CAAC,MAAM;IACpB,MAAMS,KAAK,GAAGD,kBAAkB,CAACE,SAAS,CAACC,cAAc,IAAI;MAC3DL,UAAU,CAACM,OAAO,GAAGD,cAAc;MACnC,IAAIP,eAAe,EAAE;QACnBG,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF,OAAO,MAAMC,kBAAkB,CAACK,WAAW,CAACJ,KAAK,CAAC;EACpD,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,UAAU,CAACM,OAAO;AAC3B;AACA,eAAeT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}