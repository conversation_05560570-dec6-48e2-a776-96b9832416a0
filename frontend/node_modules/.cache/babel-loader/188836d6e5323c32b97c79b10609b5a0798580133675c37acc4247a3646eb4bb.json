{"ast": null, "code": "/**\n * Since Select, TreeSelect, Cascader is same Select like component.\n * We just use same hook to handle this logic.\n *\n * If `suffixIcon` is not equal to `null`, always show it.\n */\nexport default function useShowArrow(suffixIcon, showArrow) {\n  return showArrow !== undefined ? showArrow : suffixIcon !== null;\n}", "map": {"version": 3, "names": ["useShowArrow", "suffixIcon", "showArrow", "undefined"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/select/useShowArrow.js"], "sourcesContent": ["/**\n * Since Select, TreeSelect, Cascader is same Select like component.\n * We just use same hook to handle this logic.\n *\n * If `suffixIcon` is not equal to `null`, always show it.\n */\nexport default function useShowArrow(suffixIcon, showArrow) {\n  return showArrow !== undefined ? showArrow : suffixIcon !== null;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAE;EAC1D,OAAOA,SAAS,KAAKC,SAAS,GAAGD,SAAS,GAAGD,UAAU,KAAK,IAAI;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}