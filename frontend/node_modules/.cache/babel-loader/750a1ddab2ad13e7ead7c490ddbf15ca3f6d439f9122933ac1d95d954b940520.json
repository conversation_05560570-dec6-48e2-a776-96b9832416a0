{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport { LayoutContext } from './context';\nimport useHasSider from './hooks/useHasSider';\nimport useStyle from './style';\nfunction generator({\n  suffixCls,\n  tagName,\n  displayName\n}) {\n  return BasicComponent => {\n    const Adapter = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(BasicComponent, Object.assign({\n      ref: ref,\n      suffixCls: suffixCls,\n      tagName: tagName\n    }, props))));\n    if (process.env.NODE_ENV !== 'production') {\n      Adapter.displayName = displayName;\n    }\n    return Adapter;\n  };\n}\nconst Basic = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      suffixCls,\n      className,\n      tagName: TagName\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"suffixCls\", \"className\", \"tagName\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const [wrapSSR, hashId, cssVarCls] = useStyle(prefixCls);\n  const prefixWithSuffixCls = suffixCls ? `${prefixCls}-${suffixCls}` : prefixCls;\n  return wrapSSR(/*#__PURE__*/React.createElement(TagName, Object.assign({\n    className: classNames(customizePrefixCls || prefixWithSuffixCls, className, hashId, cssVarCls),\n    ref: ref\n  }, others)));\n});\nconst BasicLayout = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    direction\n  } = React.useContext(ConfigContext);\n  const [siders, setSiders] = React.useState([]);\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      children,\n      hasSider,\n      tagName: Tag,\n      style\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"children\", \"hasSider\", \"tagName\", \"style\"]);\n  const passedProps = omit(others, ['suffixCls']);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('layout');\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const mergedHasSider = useHasSider(siders, children, hasSider);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-has-sider`]: mergedHasSider,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const contextValue = React.useMemo(() => ({\n    siderHook: {\n      addSider: id => {\n        setSiders(prev => [].concat(_toConsumableArray(prev), [id]));\n      },\n      removeSider: id => {\n        setSiders(prev => prev.filter(currentId => currentId !== id));\n      }\n    }\n  }), []);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, Object.assign({\n    ref: ref,\n    className: classString,\n    style: Object.assign(Object.assign({}, contextStyle), style)\n  }, passedProps), children)));\n});\nconst Layout = generator({\n  tagName: 'div',\n  displayName: 'Layout'\n})(BasicLayout);\nconst Header = generator({\n  suffixCls: 'header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nconst Footer = generator({\n  suffixCls: 'footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nconst Content = generator({\n  suffixCls: 'content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Content, Footer, Header };\nexport default Layout;", "map": {"version": 3, "names": ["_toConsumableArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "omit", "ConfigContext", "useComponentConfig", "LayoutContext", "useHasSider", "useStyle", "generator", "suffixCls", "tagName", "displayName", "BasicComponent", "Adapter", "forwardRef", "props", "ref", "createElement", "assign", "process", "env", "NODE_ENV", "Basic", "prefixCls", "customizePrefixCls", "className", "TagName", "others", "getPrefixCls", "useContext", "wrapSSR", "hashId", "cssVarCls", "prefixWithSuffixCls", "BasicLayout", "direction", "siders", "setSiders", "useState", "rootClassName", "children", "hasSider", "Tag", "style", "passedProps", "contextClassName", "contextStyle", "mergedHasSider", "wrapCSSVar", "classString", "contextValue", "useMemo", "<PERSON>r<PERSON><PERSON>", "addSider", "id", "prev", "concat", "removeSider", "filter", "currentId", "Provider", "value", "Layout", "Header", "Footer", "Content"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/layout/layout.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport { LayoutContext } from './context';\nimport useHasSider from './hooks/useHasSider';\nimport useStyle from './style';\nfunction generator({\n  suffixCls,\n  tagName,\n  displayName\n}) {\n  return BasicComponent => {\n    const Adapter = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(BasicComponent, Object.assign({\n      ref: ref,\n      suffixCls: suffixCls,\n      tagName: tagName\n    }, props))));\n    if (process.env.NODE_ENV !== 'production') {\n      Adapter.displayName = displayName;\n    }\n    return Adapter;\n  };\n}\nconst Basic = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      suffixCls,\n      className,\n      tagName: TagName\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"suffixCls\", \"className\", \"tagName\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const [wrapSSR, hashId, cssVarCls] = useStyle(prefixCls);\n  const prefixWithSuffixCls = suffixCls ? `${prefixCls}-${suffixCls}` : prefixCls;\n  return wrapSSR(/*#__PURE__*/React.createElement(TagName, Object.assign({\n    className: classNames(customizePrefixCls || prefixWithSuffixCls, className, hashId, cssVarCls),\n    ref: ref\n  }, others)));\n});\nconst BasicLayout = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    direction\n  } = React.useContext(ConfigContext);\n  const [siders, setSiders] = React.useState([]);\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      children,\n      hasSider,\n      tagName: Tag,\n      style\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"children\", \"hasSider\", \"tagName\", \"style\"]);\n  const passedProps = omit(others, ['suffixCls']);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('layout');\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const mergedHasSider = useHasSider(siders, children, hasSider);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-has-sider`]: mergedHasSider,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const contextValue = React.useMemo(() => ({\n    siderHook: {\n      addSider: id => {\n        setSiders(prev => [].concat(_toConsumableArray(prev), [id]));\n      },\n      removeSider: id => {\n        setSiders(prev => prev.filter(currentId => currentId !== id));\n      }\n    }\n  }), []);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, Object.assign({\n    ref: ref,\n    className: classString,\n    style: Object.assign(Object.assign({}, contextStyle), style)\n  }, passedProps), children)));\n});\nconst Layout = generator({\n  tagName: 'div',\n  displayName: 'Layout'\n})(BasicLayout);\nconst Header = generator({\n  suffixCls: 'header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nconst Footer = generator({\n  suffixCls: 'footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nconst Content = generator({\n  suffixCls: 'content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Content, Footer, Header };\nexport default Layout;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,SAASA,CAAC;EACjBC,SAAS;EACTC,OAAO;EACPC;AACF,CAAC,EAAE;EACD,OAAOC,cAAc,IAAI;IACvB,MAAMC,OAAO,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,MAAM,aAAahB,KAAK,CAACiB,aAAa,CAACL,cAAc,EAAErB,MAAM,CAAC2B,MAAM,CAAC;MAC5HF,GAAG,EAAEA,GAAG;MACRP,SAAS,EAAEA,SAAS;MACpBC,OAAO,EAAEA;IACX,CAAC,EAAEK,KAAK,CAAC,CAAC,CAAC,CAAC;IACZ,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCR,OAAO,CAACF,WAAW,GAAGA,WAAW;IACnC;IACA,OAAOE,OAAO;EAChB,CAAC;AACH;AACA,MAAMS,KAAK,GAAG,aAAatB,KAAK,CAACc,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC1D,MAAM;MACFO,SAAS,EAAEC,kBAAkB;MAC7Bf,SAAS;MACTgB,SAAS;MACTf,OAAO,EAAEgB;IACX,CAAC,GAAGX,KAAK;IACTY,MAAM,GAAGzC,MAAM,CAAC6B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;EAC5E,MAAM;IACJa;EACF,CAAC,GAAG5B,KAAK,CAAC6B,UAAU,CAAC1B,aAAa,CAAC;EACnC,MAAMoB,SAAS,GAAGK,YAAY,CAAC,QAAQ,EAAEJ,kBAAkB,CAAC;EAC5D,MAAM,CAACM,OAAO,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAACgB,SAAS,CAAC;EACxD,MAAMU,mBAAmB,GAAGxB,SAAS,GAAG,GAAGc,SAAS,IAAId,SAAS,EAAE,GAAGc,SAAS;EAC/E,OAAOO,OAAO,CAAC,aAAa9B,KAAK,CAACiB,aAAa,CAACS,OAAO,EAAEnC,MAAM,CAAC2B,MAAM,CAAC;IACrEO,SAAS,EAAExB,UAAU,CAACuB,kBAAkB,IAAIS,mBAAmB,EAAER,SAAS,EAAEM,MAAM,EAAEC,SAAS,CAAC;IAC9FhB,GAAG,EAAEA;EACP,CAAC,EAAEW,MAAM,CAAC,CAAC,CAAC;AACd,CAAC,CAAC;AACF,MAAMO,WAAW,GAAG,aAAalC,KAAK,CAACc,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChE,MAAM;IACJmB;EACF,CAAC,GAAGnC,KAAK,CAAC6B,UAAU,CAAC1B,aAAa,CAAC;EACnC,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,KAAK,CAACsC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM;MACFf,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTc,aAAa;MACbC,QAAQ;MACRC,QAAQ;MACR/B,OAAO,EAAEgC,GAAG;MACZC;IACF,CAAC,GAAG5B,KAAK;IACTY,MAAM,GAAGzC,MAAM,CAAC6B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;EACjH,MAAM6B,WAAW,GAAG1C,IAAI,CAACyB,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC;EAC/C,MAAM;IACJC,YAAY;IACZH,SAAS,EAAEoB,gBAAgB;IAC3BF,KAAK,EAAEG;EACT,CAAC,GAAG1C,kBAAkB,CAAC,QAAQ,CAAC;EAChC,MAAMmB,SAAS,GAAGK,YAAY,CAAC,QAAQ,EAAEJ,kBAAkB,CAAC;EAC5D,MAAMuB,cAAc,GAAGzC,WAAW,CAAC8B,MAAM,EAAEI,QAAQ,EAAEC,QAAQ,CAAC;EAC9D,MAAM,CAACO,UAAU,EAAEjB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAACgB,SAAS,CAAC;EAC3D,MAAM0B,WAAW,GAAGhD,UAAU,CAACsB,SAAS,EAAE;IACxC,CAAC,GAAGA,SAAS,YAAY,GAAGwB,cAAc;IAC1C,CAAC,GAAGxB,SAAS,MAAM,GAAGY,SAAS,KAAK;EACtC,CAAC,EAAEU,gBAAgB,EAAEpB,SAAS,EAAEc,aAAa,EAAER,MAAM,EAAEC,SAAS,CAAC;EACjE,MAAMkB,YAAY,GAAGlD,KAAK,CAACmD,OAAO,CAAC,OAAO;IACxCC,SAAS,EAAE;MACTC,QAAQ,EAAEC,EAAE,IAAI;QACdjB,SAAS,CAACkB,IAAI,IAAI,EAAE,CAACC,MAAM,CAACvE,kBAAkB,CAACsE,IAAI,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC,CAAC;MAC9D,CAAC;MACDG,WAAW,EAAEH,EAAE,IAAI;QACjBjB,SAAS,CAACkB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACC,SAAS,IAAIA,SAAS,KAAKL,EAAE,CAAC,CAAC;MAC/D;IACF;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAON,UAAU,CAAC,aAAahD,KAAK,CAACiB,aAAa,CAACZ,aAAa,CAACuD,QAAQ,EAAE;IACzEC,KAAK,EAAEX;EACT,CAAC,EAAE,aAAalD,KAAK,CAACiB,aAAa,CAACyB,GAAG,EAAEnD,MAAM,CAAC2B,MAAM,CAAC;IACrDF,GAAG,EAAEA,GAAG;IACRS,SAAS,EAAEwB,WAAW;IACtBN,KAAK,EAAEpD,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAE4B,YAAY,CAAC,EAAEH,KAAK;EAC7D,CAAC,EAAEC,WAAW,CAAC,EAAEJ,QAAQ,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AACF,MAAMsB,MAAM,GAAGtD,SAAS,CAAC;EACvBE,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;AACf,CAAC,CAAC,CAACuB,WAAW,CAAC;AACf,MAAM6B,MAAM,GAAGvD,SAAS,CAAC;EACvBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACW,KAAK,CAAC;AACT,MAAM0C,MAAM,GAAGxD,SAAS,CAAC;EACvBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE;AACf,CAAC,CAAC,CAACW,KAAK,CAAC;AACT,MAAM2C,OAAO,GAAGzD,SAAS,CAAC;EACxBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE;AACf,CAAC,CAAC,CAACW,KAAK,CAAC;AACT,SAAS2C,OAAO,EAAED,MAAM,EAAED,MAAM;AAChC,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}