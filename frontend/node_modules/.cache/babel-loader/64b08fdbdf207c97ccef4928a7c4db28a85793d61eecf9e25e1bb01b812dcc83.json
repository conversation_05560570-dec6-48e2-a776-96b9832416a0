{"ast": null, "code": "import React from 'react';\nimport toList from '../_util/toList';\nexport const FontGap = 3;\nconst prepareCanvas = (width, height, ratio = 1) => {\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  const realWidth = width * ratio;\n  const realHeight = height * ratio;\n  canvas.setAttribute('width', `${realWidth}px`);\n  canvas.setAttribute('height', `${realHeight}px`);\n  ctx.save();\n  return [ctx, canvas, realWidth, realHeight];\n};\n// Get boundary of rotated text\nconst getRotatePos = (x, y, angle) => {\n  const targetX = x * Math.cos(angle) - y * Math.sin(angle);\n  const targetY = x * Math.sin(angle) + y * Math.cos(angle);\n  return [targetX, targetY];\n};\n/**\n * Get the clips of text content.\n * This is a lazy hook function since SSR no need this\n */\nconst useClips = () => {\n  // Get single clips\n  const getClips = (content, rotate, ratio, width, height, font, gapX, gapY) => {\n    // ================= Text / Image =================\n    const [ctx, canvas, contentWidth, contentHeight] = prepareCanvas(width, height, ratio);\n    if (content instanceof HTMLImageElement) {\n      // Image\n      ctx.drawImage(content, 0, 0, contentWidth, contentHeight);\n    } else {\n      // Text\n      const {\n        color,\n        fontSize,\n        fontStyle,\n        fontWeight,\n        fontFamily,\n        textAlign\n      } = font;\n      const mergedFontSize = Number(fontSize) * ratio;\n      ctx.font = `${fontStyle} normal ${fontWeight} ${mergedFontSize}px/${height}px ${fontFamily}`;\n      ctx.fillStyle = color;\n      ctx.textAlign = textAlign;\n      ctx.textBaseline = 'top';\n      const contents = toList(content);\n      contents === null || contents === void 0 ? void 0 : contents.forEach((item, index) => {\n        ctx.fillText(item !== null && item !== void 0 ? item : '', contentWidth / 2, index * (mergedFontSize + FontGap * ratio));\n      });\n    }\n    // ==================== Rotate ====================\n    const angle = Math.PI / 180 * Number(rotate);\n    const maxSize = Math.max(width, height);\n    const [rCtx, rCanvas, realMaxSize] = prepareCanvas(maxSize, maxSize, ratio);\n    // Copy from `ctx` and rotate\n    rCtx.translate(realMaxSize / 2, realMaxSize / 2);\n    rCtx.rotate(angle);\n    if (contentWidth > 0 && contentHeight > 0) {\n      rCtx.drawImage(canvas, -contentWidth / 2, -contentHeight / 2);\n    }\n    let left = 0;\n    let right = 0;\n    let top = 0;\n    let bottom = 0;\n    const halfWidth = contentWidth / 2;\n    const halfHeight = contentHeight / 2;\n    const points = [[0 - halfWidth, 0 - halfHeight], [0 + halfWidth, 0 - halfHeight], [0 + halfWidth, 0 + halfHeight], [0 - halfWidth, 0 + halfHeight]];\n    points.forEach(([x, y]) => {\n      const [targetX, targetY] = getRotatePos(x, y, angle);\n      left = Math.min(left, targetX);\n      right = Math.max(right, targetX);\n      top = Math.min(top, targetY);\n      bottom = Math.max(bottom, targetY);\n    });\n    const cutLeft = left + realMaxSize / 2;\n    const cutTop = top + realMaxSize / 2;\n    const cutWidth = right - left;\n    const cutHeight = bottom - top;\n    // ================ Fill Alternate ================\n    const realGapX = gapX * ratio;\n    const realGapY = gapY * ratio;\n    const filledWidth = (cutWidth + realGapX) * 2;\n    const filledHeight = cutHeight + realGapY;\n    const [fCtx, fCanvas] = prepareCanvas(filledWidth, filledHeight);\n    const drawImg = (targetX = 0, targetY = 0) => {\n      fCtx.drawImage(rCanvas, cutLeft, cutTop, cutWidth, cutHeight, targetX, targetY, cutWidth, cutHeight);\n    };\n    drawImg();\n    drawImg(cutWidth + realGapX, -cutHeight / 2 - realGapY / 2);\n    drawImg(cutWidth + realGapX, +cutHeight / 2 + realGapY / 2);\n    return [fCanvas.toDataURL(), filledWidth / ratio, filledHeight / ratio];\n  };\n  return React.useCallback(getClips, []);\n};\nexport default useClips;", "map": {"version": 3, "names": ["React", "toList", "FontGap", "prepare<PERSON><PERSON><PERSON>", "width", "height", "ratio", "canvas", "document", "createElement", "ctx", "getContext", "realWidth", "realHeight", "setAttribute", "save", "getRotatePos", "x", "y", "angle", "targetX", "Math", "cos", "sin", "targetY", "useClips", "getClips", "content", "rotate", "font", "gapX", "gapY", "contentWidth", "contentHeight", "HTMLImageElement", "drawImage", "color", "fontSize", "fontStyle", "fontWeight", "fontFamily", "textAlign", "mergedFontSize", "Number", "fillStyle", "textBaseline", "contents", "for<PERSON>ach", "item", "index", "fillText", "PI", "maxSize", "max", "rCtx", "rCanvas", "realMaxSize", "translate", "left", "right", "top", "bottom", "halfWidth", "halfHeight", "points", "min", "cutLeft", "cutTop", "cutWidth", "cutHeight", "realGapX", "realGapY", "filled<PERSON><PERSON><PERSON>", "filledHeight", "fCtx", "fCanvas", "drawImg", "toDataURL", "useCallback"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/watermark/useClips.js"], "sourcesContent": ["import React from 'react';\nimport toList from '../_util/toList';\nexport const FontGap = 3;\nconst prepareCanvas = (width, height, ratio = 1) => {\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  const realWidth = width * ratio;\n  const realHeight = height * ratio;\n  canvas.setAttribute('width', `${realWidth}px`);\n  canvas.setAttribute('height', `${realHeight}px`);\n  ctx.save();\n  return [ctx, canvas, realWidth, realHeight];\n};\n// Get boundary of rotated text\nconst getRotatePos = (x, y, angle) => {\n  const targetX = x * Math.cos(angle) - y * Math.sin(angle);\n  const targetY = x * Math.sin(angle) + y * Math.cos(angle);\n  return [targetX, targetY];\n};\n/**\n * Get the clips of text content.\n * This is a lazy hook function since SSR no need this\n */\nconst useClips = () => {\n  // Get single clips\n  const getClips = (content, rotate, ratio, width, height, font, gapX, gapY) => {\n    // ================= Text / Image =================\n    const [ctx, canvas, contentWidth, contentHeight] = prepareCanvas(width, height, ratio);\n    if (content instanceof HTMLImageElement) {\n      // Image\n      ctx.drawImage(content, 0, 0, contentWidth, contentHeight);\n    } else {\n      // Text\n      const {\n        color,\n        fontSize,\n        fontStyle,\n        fontWeight,\n        fontFamily,\n        textAlign\n      } = font;\n      const mergedFontSize = Number(fontSize) * ratio;\n      ctx.font = `${fontStyle} normal ${fontWeight} ${mergedFontSize}px/${height}px ${fontFamily}`;\n      ctx.fillStyle = color;\n      ctx.textAlign = textAlign;\n      ctx.textBaseline = 'top';\n      const contents = toList(content);\n      contents === null || contents === void 0 ? void 0 : contents.forEach((item, index) => {\n        ctx.fillText(item !== null && item !== void 0 ? item : '', contentWidth / 2, index * (mergedFontSize + FontGap * ratio));\n      });\n    }\n    // ==================== Rotate ====================\n    const angle = Math.PI / 180 * Number(rotate);\n    const maxSize = Math.max(width, height);\n    const [rCtx, rCanvas, realMaxSize] = prepareCanvas(maxSize, maxSize, ratio);\n    // Copy from `ctx` and rotate\n    rCtx.translate(realMaxSize / 2, realMaxSize / 2);\n    rCtx.rotate(angle);\n    if (contentWidth > 0 && contentHeight > 0) {\n      rCtx.drawImage(canvas, -contentWidth / 2, -contentHeight / 2);\n    }\n    let left = 0;\n    let right = 0;\n    let top = 0;\n    let bottom = 0;\n    const halfWidth = contentWidth / 2;\n    const halfHeight = contentHeight / 2;\n    const points = [[0 - halfWidth, 0 - halfHeight], [0 + halfWidth, 0 - halfHeight], [0 + halfWidth, 0 + halfHeight], [0 - halfWidth, 0 + halfHeight]];\n    points.forEach(([x, y]) => {\n      const [targetX, targetY] = getRotatePos(x, y, angle);\n      left = Math.min(left, targetX);\n      right = Math.max(right, targetX);\n      top = Math.min(top, targetY);\n      bottom = Math.max(bottom, targetY);\n    });\n    const cutLeft = left + realMaxSize / 2;\n    const cutTop = top + realMaxSize / 2;\n    const cutWidth = right - left;\n    const cutHeight = bottom - top;\n    // ================ Fill Alternate ================\n    const realGapX = gapX * ratio;\n    const realGapY = gapY * ratio;\n    const filledWidth = (cutWidth + realGapX) * 2;\n    const filledHeight = cutHeight + realGapY;\n    const [fCtx, fCanvas] = prepareCanvas(filledWidth, filledHeight);\n    const drawImg = (targetX = 0, targetY = 0) => {\n      fCtx.drawImage(rCanvas, cutLeft, cutTop, cutWidth, cutHeight, targetX, targetY, cutWidth, cutHeight);\n    };\n    drawImg();\n    drawImg(cutWidth + realGapX, -cutHeight / 2 - realGapY / 2);\n    drawImg(cutWidth + realGapX, +cutHeight / 2 + realGapY / 2);\n    return [fCanvas.toDataURL(), filledWidth / ratio, filledHeight / ratio];\n  };\n  return React.useCallback(getClips, []);\n};\nexport default useClips;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,KAAK;EAClD,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACnC,MAAMC,SAAS,GAAGR,KAAK,GAAGE,KAAK;EAC/B,MAAMO,UAAU,GAAGR,MAAM,GAAGC,KAAK;EACjCC,MAAM,CAACO,YAAY,CAAC,OAAO,EAAE,GAAGF,SAAS,IAAI,CAAC;EAC9CL,MAAM,CAACO,YAAY,CAAC,QAAQ,EAAE,GAAGD,UAAU,IAAI,CAAC;EAChDH,GAAG,CAACK,IAAI,CAAC,CAAC;EACV,OAAO,CAACL,GAAG,EAAEH,MAAM,EAAEK,SAAS,EAAEC,UAAU,CAAC;AAC7C,CAAC;AACD;AACA,MAAMG,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,KAAK;EACpC,MAAMC,OAAO,GAAGH,CAAC,GAAGI,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,GAAGD,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACJ,KAAK,CAAC;EACzD,MAAMK,OAAO,GAAGP,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACJ,KAAK,CAAC,GAAGD,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC;EACzD,OAAO,CAACC,OAAO,EAAEI,OAAO,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB;EACA,MAAMC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,MAAM,EAAEtB,KAAK,EAAEF,KAAK,EAAEC,MAAM,EAAEwB,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAC5E;IACA,MAAM,CAACrB,GAAG,EAAEH,MAAM,EAAEyB,YAAY,EAAEC,aAAa,CAAC,GAAG9B,aAAa,CAACC,KAAK,EAAEC,MAAM,EAAEC,KAAK,CAAC;IACtF,IAAIqB,OAAO,YAAYO,gBAAgB,EAAE;MACvC;MACAxB,GAAG,CAACyB,SAAS,CAACR,OAAO,EAAE,CAAC,EAAE,CAAC,EAAEK,YAAY,EAAEC,aAAa,CAAC;IAC3D,CAAC,MAAM;MACL;MACA,MAAM;QACJG,KAAK;QACLC,QAAQ;QACRC,SAAS;QACTC,UAAU;QACVC,UAAU;QACVC;MACF,CAAC,GAAGZ,IAAI;MACR,MAAMa,cAAc,GAAGC,MAAM,CAACN,QAAQ,CAAC,GAAG/B,KAAK;MAC/CI,GAAG,CAACmB,IAAI,GAAG,GAAGS,SAAS,WAAWC,UAAU,IAAIG,cAAc,MAAMrC,MAAM,MAAMmC,UAAU,EAAE;MAC5F9B,GAAG,CAACkC,SAAS,GAAGR,KAAK;MACrB1B,GAAG,CAAC+B,SAAS,GAAGA,SAAS;MACzB/B,GAAG,CAACmC,YAAY,GAAG,KAAK;MACxB,MAAMC,QAAQ,GAAG7C,MAAM,CAAC0B,OAAO,CAAC;MAChCmB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QACpFvC,GAAG,CAACwC,QAAQ,CAACF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE,EAAEhB,YAAY,GAAG,CAAC,EAAEiB,KAAK,IAAIP,cAAc,GAAGxC,OAAO,GAAGI,KAAK,CAAC,CAAC;MAC1H,CAAC,CAAC;IACJ;IACA;IACA,MAAMa,KAAK,GAAGE,IAAI,CAAC8B,EAAE,GAAG,GAAG,GAAGR,MAAM,CAACf,MAAM,CAAC;IAC5C,MAAMwB,OAAO,GAAG/B,IAAI,CAACgC,GAAG,CAACjD,KAAK,EAAEC,MAAM,CAAC;IACvC,MAAM,CAACiD,IAAI,EAAEC,OAAO,EAAEC,WAAW,CAAC,GAAGrD,aAAa,CAACiD,OAAO,EAAEA,OAAO,EAAE9C,KAAK,CAAC;IAC3E;IACAgD,IAAI,CAACG,SAAS,CAACD,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG,CAAC,CAAC;IAChDF,IAAI,CAAC1B,MAAM,CAACT,KAAK,CAAC;IAClB,IAAIa,YAAY,GAAG,CAAC,IAAIC,aAAa,GAAG,CAAC,EAAE;MACzCqB,IAAI,CAACnB,SAAS,CAAC5B,MAAM,EAAE,CAACyB,YAAY,GAAG,CAAC,EAAE,CAACC,aAAa,GAAG,CAAC,CAAC;IAC/D;IACA,IAAIyB,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,MAAM,GAAG,CAAC;IACd,MAAMC,SAAS,GAAG9B,YAAY,GAAG,CAAC;IAClC,MAAM+B,UAAU,GAAG9B,aAAa,GAAG,CAAC;IACpC,MAAM+B,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGF,SAAS,EAAE,CAAC,GAAGC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAGD,SAAS,EAAE,CAAC,GAAGC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAGD,SAAS,EAAE,CAAC,GAAGC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAGD,SAAS,EAAE,CAAC,GAAGC,UAAU,CAAC,CAAC;IACnJC,MAAM,CAACjB,OAAO,CAAC,CAAC,CAAC9B,CAAC,EAAEC,CAAC,CAAC,KAAK;MACzB,MAAM,CAACE,OAAO,EAAEI,OAAO,CAAC,GAAGR,YAAY,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpDuC,IAAI,GAAGrC,IAAI,CAAC4C,GAAG,CAACP,IAAI,EAAEtC,OAAO,CAAC;MAC9BuC,KAAK,GAAGtC,IAAI,CAACgC,GAAG,CAACM,KAAK,EAAEvC,OAAO,CAAC;MAChCwC,GAAG,GAAGvC,IAAI,CAAC4C,GAAG,CAACL,GAAG,EAAEpC,OAAO,CAAC;MAC5BqC,MAAM,GAAGxC,IAAI,CAACgC,GAAG,CAACQ,MAAM,EAAErC,OAAO,CAAC;IACpC,CAAC,CAAC;IACF,MAAM0C,OAAO,GAAGR,IAAI,GAAGF,WAAW,GAAG,CAAC;IACtC,MAAMW,MAAM,GAAGP,GAAG,GAAGJ,WAAW,GAAG,CAAC;IACpC,MAAMY,QAAQ,GAAGT,KAAK,GAAGD,IAAI;IAC7B,MAAMW,SAAS,GAAGR,MAAM,GAAGD,GAAG;IAC9B;IACA,MAAMU,QAAQ,GAAGxC,IAAI,GAAGxB,KAAK;IAC7B,MAAMiE,QAAQ,GAAGxC,IAAI,GAAGzB,KAAK;IAC7B,MAAMkE,WAAW,GAAG,CAACJ,QAAQ,GAAGE,QAAQ,IAAI,CAAC;IAC7C,MAAMG,YAAY,GAAGJ,SAAS,GAAGE,QAAQ;IACzC,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAGxE,aAAa,CAACqE,WAAW,EAAEC,YAAY,CAAC;IAChE,MAAMG,OAAO,GAAGA,CAACxD,OAAO,GAAG,CAAC,EAAEI,OAAO,GAAG,CAAC,KAAK;MAC5CkD,IAAI,CAACvC,SAAS,CAACoB,OAAO,EAAEW,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEjD,OAAO,EAAEI,OAAO,EAAE4C,QAAQ,EAAEC,SAAS,CAAC;IACtG,CAAC;IACDO,OAAO,CAAC,CAAC;IACTA,OAAO,CAACR,QAAQ,GAAGE,QAAQ,EAAE,CAACD,SAAS,GAAG,CAAC,GAAGE,QAAQ,GAAG,CAAC,CAAC;IAC3DK,OAAO,CAACR,QAAQ,GAAGE,QAAQ,EAAE,CAACD,SAAS,GAAG,CAAC,GAAGE,QAAQ,GAAG,CAAC,CAAC;IAC3D,OAAO,CAACI,OAAO,CAACE,SAAS,CAAC,CAAC,EAAEL,WAAW,GAAGlE,KAAK,EAAEmE,YAAY,GAAGnE,KAAK,CAAC;EACzE,CAAC;EACD,OAAON,KAAK,CAAC8E,WAAW,CAACpD,QAAQ,EAAE,EAAE,CAAC;AACxC,CAAC;AACD,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}