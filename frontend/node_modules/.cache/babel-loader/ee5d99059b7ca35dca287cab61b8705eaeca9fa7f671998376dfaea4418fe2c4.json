{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { Result, Button } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(Result, {\n        status: \"500\",\n        title: \"500\",\n        subTitle: \"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u51FA\\u73B0\\u4E86\\u9519\\u8BEF\\u3002\",\n        extra: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: this.handleReload,\n          children: \"\\u5237\\u65B0\\u9875\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "Result", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleReload", "window", "location", "reload", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "render", "status", "title", "subTitle", "extra", "type", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ReactNode } from 'react';\nimport { Result, Button } from 'antd';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <Result\n          status=\"500\"\n          title=\"500\"\n          subTitle=\"抱歉，页面出现了错误。\"\n          extra={\n            <Button type=\"primary\" onClick={this.handleReload}>\n              刷新页面\n            </Button>\n          }\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAmB,OAAO;AACnD,SAASC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtC,MAAMC,aAAa,SAASL,SAAS,CAAe;EAClDM,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KAYfC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAbC,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOC,wBAAwBA,CAACC,KAAY,EAAS;IACnD,OAAO;MAAEF,QAAQ,EAAE,IAAI;MAAEE;IAAM,CAAC;EAClC;EAEAC,iBAAiBA,CAACD,KAAY,EAAEE,SAA0B,EAAE;IAC1DC,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;EACnE;EAMAE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACET,OAAA,CAACH,MAAM;QACLmB,MAAM,EAAC,KAAK;QACZC,KAAK,EAAC,KAAK;QACXC,QAAQ,EAAC,oEAAa;QACtBC,KAAK,eACHnB,OAAA,CAACF,MAAM;UAACsB,IAAI,EAAC,SAAS;UAACC,OAAO,EAAE,IAAI,CAACjB,YAAa;UAAAkB,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN;IAEA,OAAO,IAAI,CAACvB,KAAK,CAACmB,QAAQ;EAC5B;AACF;AAEA,eAAerB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}