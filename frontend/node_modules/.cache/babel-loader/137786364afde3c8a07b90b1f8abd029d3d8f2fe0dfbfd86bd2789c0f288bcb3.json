{"ast": null, "code": "export const getRenderPropValue = propValue => {\n  if (!propValue) {\n    return null;\n  }\n  return typeof propValue === 'function' ? propValue() : propValue;\n};", "map": {"version": 3, "names": ["getRenderPropValue", "propValue"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/getRenderPropValue.js"], "sourcesContent": ["export const getRenderPropValue = propValue => {\n  if (!propValue) {\n    return null;\n  }\n  return typeof propValue === 'function' ? propValue() : propValue;\n};"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAGC,SAAS,IAAI;EAC7C,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,OAAO,OAAOA,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;AAClE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}