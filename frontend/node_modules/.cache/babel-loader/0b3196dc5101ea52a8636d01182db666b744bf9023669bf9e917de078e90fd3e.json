{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    lineHeightHeading3,\n    iconCls,\n    padding,\n    paddingXL,\n    paddingXS,\n    paddingLG,\n    marginXS,\n    lineHeight\n  } = token;\n  return {\n    // Result\n    [componentCls]: {\n      padding: `${unit(token.calc(paddingLG).mul(2).equal())} ${unit(paddingXL)}`,\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    },\n    // Exception Status image\n    [`${componentCls} ${componentCls}-image`]: {\n      width: token.imageWidth,\n      height: token.imageHeight,\n      margin: 'auto'\n    },\n    [`${componentCls} ${componentCls}-icon`]: {\n      marginBottom: paddingLG,\n      textAlign: 'center',\n      [`& > ${iconCls}`]: {\n        fontSize: token.iconFontSize\n      }\n    },\n    [`${componentCls} ${componentCls}-title`]: {\n      color: token.colorTextHeading,\n      fontSize: token.titleFontSize,\n      lineHeight: lineHeightHeading3,\n      marginBlock: marginXS,\n      textAlign: 'center'\n    },\n    [`${componentCls} ${componentCls}-subtitle`]: {\n      color: token.colorTextDescription,\n      fontSize: token.subtitleFontSize,\n      lineHeight,\n      textAlign: 'center'\n    },\n    [`${componentCls} ${componentCls}-content`]: {\n      marginTop: paddingLG,\n      padding: `${unit(paddingLG)} ${unit(token.calc(padding).mul(2.5).equal())}`,\n      backgroundColor: token.colorFillAlter\n    },\n    [`${componentCls} ${componentCls}-extra`]: {\n      margin: token.extraMargin,\n      textAlign: 'center',\n      '& > *': {\n        marginInlineEnd: paddingXS,\n        '&:last-child': {\n          marginInlineEnd: 0\n        }\n      }\n    }\n  };\n};\nconst genStatusIconStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-success ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultSuccessIconColor\n    },\n    [`${componentCls}-error ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultErrorIconColor\n    },\n    [`${componentCls}-info ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultInfoIconColor\n    },\n    [`${componentCls}-warning ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultWarningIconColor\n    }\n  };\n};\nconst genResultStyle = token => [genBaseStyle(token), genStatusIconStyle(token)];\nconst getStyle = token => genResultStyle(token);\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  titleFontSize: token.fontSizeHeading3,\n  subtitleFontSize: token.fontSize,\n  iconFontSize: token.fontSizeHeading3 * 3,\n  extraMargin: `${token.paddingLG}px 0 0 0`\n});\nexport default genStyleHooks('Result', token => {\n  const resultInfoIconColor = token.colorInfo;\n  const resultErrorIconColor = token.colorError;\n  const resultSuccessIconColor = token.colorSuccess;\n  const resultWarningIconColor = token.colorWarning;\n  const resultToken = mergeToken(token, {\n    resultInfoIconColor,\n    resultErrorIconColor,\n    resultSuccessIconColor,\n    resultWarningIconColor,\n    imageWidth: 250,\n    imageHeight: 295\n  });\n  return [getStyle(resultToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "genStyleHooks", "mergeToken", "genBaseStyle", "token", "componentCls", "lineHeightHeading3", "iconCls", "padding", "paddingXL", "paddingXS", "paddingLG", "marginXS", "lineHeight", "calc", "mul", "equal", "direction", "width", "imageWidth", "height", "imageHeight", "margin", "marginBottom", "textAlign", "fontSize", "iconFontSize", "color", "colorTextHeading", "titleFontSize", "marginBlock", "colorTextDescription", "subtitleFontSize", "marginTop", "backgroundColor", "colorFillAlter", "extraMargin", "marginInlineEnd", "genStatusIconStyle", "resultSuccessIconColor", "resultErrorIconColor", "resultInfoIconColor", "resultWarningIconColor", "genResultStyle", "getStyle", "prepareComponentToken", "fontSizeHeading3", "colorInfo", "colorError", "colorSuccess", "colorWarning", "resultToken"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/result/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    lineHeightHeading3,\n    iconCls,\n    padding,\n    paddingXL,\n    paddingXS,\n    paddingLG,\n    marginXS,\n    lineHeight\n  } = token;\n  return {\n    // Result\n    [componentCls]: {\n      padding: `${unit(token.calc(paddingLG).mul(2).equal())} ${unit(paddingXL)}`,\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    },\n    // Exception Status image\n    [`${componentCls} ${componentCls}-image`]: {\n      width: token.imageWidth,\n      height: token.imageHeight,\n      margin: 'auto'\n    },\n    [`${componentCls} ${componentCls}-icon`]: {\n      marginBottom: paddingLG,\n      textAlign: 'center',\n      [`& > ${iconCls}`]: {\n        fontSize: token.iconFontSize\n      }\n    },\n    [`${componentCls} ${componentCls}-title`]: {\n      color: token.colorTextHeading,\n      fontSize: token.titleFontSize,\n      lineHeight: lineHeightHeading3,\n      marginBlock: marginXS,\n      textAlign: 'center'\n    },\n    [`${componentCls} ${componentCls}-subtitle`]: {\n      color: token.colorTextDescription,\n      fontSize: token.subtitleFontSize,\n      lineHeight,\n      textAlign: 'center'\n    },\n    [`${componentCls} ${componentCls}-content`]: {\n      marginTop: paddingLG,\n      padding: `${unit(paddingLG)} ${unit(token.calc(padding).mul(2.5).equal())}`,\n      backgroundColor: token.colorFillAlter\n    },\n    [`${componentCls} ${componentCls}-extra`]: {\n      margin: token.extraMargin,\n      textAlign: 'center',\n      '& > *': {\n        marginInlineEnd: paddingXS,\n        '&:last-child': {\n          marginInlineEnd: 0\n        }\n      }\n    }\n  };\n};\nconst genStatusIconStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-success ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultSuccessIconColor\n    },\n    [`${componentCls}-error ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultErrorIconColor\n    },\n    [`${componentCls}-info ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultInfoIconColor\n    },\n    [`${componentCls}-warning ${componentCls}-icon > ${iconCls}`]: {\n      color: token.resultWarningIconColor\n    }\n  };\n};\nconst genResultStyle = token => [genBaseStyle(token), genStatusIconStyle(token)];\nconst getStyle = token => genResultStyle(token);\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  titleFontSize: token.fontSizeHeading3,\n  subtitleFontSize: token.fontSize,\n  iconFontSize: token.fontSizeHeading3 * 3,\n  extraMargin: `${token.paddingLG}px 0 0 0`\n});\nexport default genStyleHooks('Result', token => {\n  const resultInfoIconColor = token.colorInfo;\n  const resultErrorIconColor = token.colorError;\n  const resultSuccessIconColor = token.colorSuccess;\n  const resultWarningIconColor = token.colorWarning;\n  const resultToken = mergeToken(token, {\n    resultInfoIconColor,\n    resultErrorIconColor,\n    resultSuccessIconColor,\n    resultWarningIconColor,\n    imageWidth: 250,\n    imageHeight: 295\n  });\n  return [getStyle(resultToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,kBAAkB;IAClBC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGT,KAAK;EACT,OAAO;IACL;IACA,CAACC,YAAY,GAAG;MACdG,OAAO,EAAE,GAAGR,IAAI,CAACI,KAAK,CAACU,IAAI,CAACH,SAAS,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAIhB,IAAI,CAACS,SAAS,CAAC,EAAE;MAC3E;MACA,OAAO,EAAE;QACPQ,SAAS,EAAE;MACb;IACF,CAAC;IACD;IACA,CAAC,GAAGZ,YAAY,IAAIA,YAAY,QAAQ,GAAG;MACzCa,KAAK,EAAEd,KAAK,CAACe,UAAU;MACvBC,MAAM,EAAEhB,KAAK,CAACiB,WAAW;MACzBC,MAAM,EAAE;IACV,CAAC;IACD,CAAC,GAAGjB,YAAY,IAAIA,YAAY,OAAO,GAAG;MACxCkB,YAAY,EAAEZ,SAAS;MACvBa,SAAS,EAAE,QAAQ;MACnB,CAAC,OAAOjB,OAAO,EAAE,GAAG;QAClBkB,QAAQ,EAAErB,KAAK,CAACsB;MAClB;IACF,CAAC;IACD,CAAC,GAAGrB,YAAY,IAAIA,YAAY,QAAQ,GAAG;MACzCsB,KAAK,EAAEvB,KAAK,CAACwB,gBAAgB;MAC7BH,QAAQ,EAAErB,KAAK,CAACyB,aAAa;MAC7BhB,UAAU,EAAEP,kBAAkB;MAC9BwB,WAAW,EAAElB,QAAQ;MACrBY,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAGnB,YAAY,IAAIA,YAAY,WAAW,GAAG;MAC5CsB,KAAK,EAAEvB,KAAK,CAAC2B,oBAAoB;MACjCN,QAAQ,EAAErB,KAAK,CAAC4B,gBAAgB;MAChCnB,UAAU;MACVW,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAGnB,YAAY,IAAIA,YAAY,UAAU,GAAG;MAC3C4B,SAAS,EAAEtB,SAAS;MACpBH,OAAO,EAAE,GAAGR,IAAI,CAACW,SAAS,CAAC,IAAIX,IAAI,CAACI,KAAK,CAACU,IAAI,CAACN,OAAO,CAAC,CAACO,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;MAC3EkB,eAAe,EAAE9B,KAAK,CAAC+B;IACzB,CAAC;IACD,CAAC,GAAG9B,YAAY,IAAIA,YAAY,QAAQ,GAAG;MACzCiB,MAAM,EAAElB,KAAK,CAACgC,WAAW;MACzBZ,SAAS,EAAE,QAAQ;MACnB,OAAO,EAAE;QACPa,eAAe,EAAE3B,SAAS;QAC1B,cAAc,EAAE;UACd2B,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,kBAAkB,GAAGlC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZE;EACF,CAAC,GAAGH,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,YAAYA,YAAY,WAAWE,OAAO,EAAE,GAAG;MAC7DoB,KAAK,EAAEvB,KAAK,CAACmC;IACf,CAAC;IACD,CAAC,GAAGlC,YAAY,UAAUA,YAAY,WAAWE,OAAO,EAAE,GAAG;MAC3DoB,KAAK,EAAEvB,KAAK,CAACoC;IACf,CAAC;IACD,CAAC,GAAGnC,YAAY,SAASA,YAAY,WAAWE,OAAO,EAAE,GAAG;MAC1DoB,KAAK,EAAEvB,KAAK,CAACqC;IACf,CAAC;IACD,CAAC,GAAGpC,YAAY,YAAYA,YAAY,WAAWE,OAAO,EAAE,GAAG;MAC7DoB,KAAK,EAAEvB,KAAK,CAACsC;IACf;EACF,CAAC;AACH,CAAC;AACD,MAAMC,cAAc,GAAGvC,KAAK,IAAI,CAACD,YAAY,CAACC,KAAK,CAAC,EAAEkC,kBAAkB,CAAClC,KAAK,CAAC,CAAC;AAChF,MAAMwC,QAAQ,GAAGxC,KAAK,IAAIuC,cAAc,CAACvC,KAAK,CAAC;AAC/C;AACA,OAAO,MAAMyC,qBAAqB,GAAGzC,KAAK,KAAK;EAC7CyB,aAAa,EAAEzB,KAAK,CAAC0C,gBAAgB;EACrCd,gBAAgB,EAAE5B,KAAK,CAACqB,QAAQ;EAChCC,YAAY,EAAEtB,KAAK,CAAC0C,gBAAgB,GAAG,CAAC;EACxCV,WAAW,EAAE,GAAGhC,KAAK,CAACO,SAAS;AACjC,CAAC,CAAC;AACF,eAAeV,aAAa,CAAC,QAAQ,EAAEG,KAAK,IAAI;EAC9C,MAAMqC,mBAAmB,GAAGrC,KAAK,CAAC2C,SAAS;EAC3C,MAAMP,oBAAoB,GAAGpC,KAAK,CAAC4C,UAAU;EAC7C,MAAMT,sBAAsB,GAAGnC,KAAK,CAAC6C,YAAY;EACjD,MAAMP,sBAAsB,GAAGtC,KAAK,CAAC8C,YAAY;EACjD,MAAMC,WAAW,GAAGjD,UAAU,CAACE,KAAK,EAAE;IACpCqC,mBAAmB;IACnBD,oBAAoB;IACpBD,sBAAsB;IACtBG,sBAAsB;IACtBvB,UAAU,EAAE,GAAG;IACfE,WAAW,EAAE;EACf,CAAC,CAAC;EACF,OAAO,CAACuB,QAAQ,CAACO,WAAW,CAAC,CAAC;AAChC,CAAC,EAAEN,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}