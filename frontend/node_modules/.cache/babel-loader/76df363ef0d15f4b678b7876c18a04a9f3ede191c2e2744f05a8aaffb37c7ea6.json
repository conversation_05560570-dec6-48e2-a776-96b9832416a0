{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst Avatar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      shape,\n      size: customSize,\n      src,\n      srcSet,\n      icon,\n      className,\n      rootClassName,\n      style,\n      alt,\n      draggable,\n      children,\n      crossOrigin,\n      gap = 4,\n      onError\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"rootClassName\", \"style\", \"alt\", \"draggable\", \"children\", \"crossOrigin\", \"gap\", \"onError\"]);\n  const [scale, setScale] = React.useState(1);\n  const [mounted, setMounted] = React.useState(false);\n  const [isImgExist, setIsImgExist] = React.useState(true);\n  const avatarNodeRef = React.useRef(null);\n  const avatarChildrenRef = React.useRef(null);\n  const avatarNodeMergedRef = composeRef(ref, avatarNodeRef);\n  const {\n    getPrefixCls,\n    avatar\n  } = React.useContext(ConfigContext);\n  const avatarCtx = React.useContext(AvatarContext);\n  const setScaleParam = () => {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    const childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    const nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    setIsImgExist(true);\n    setScale(1);\n  }, [src]);\n  React.useEffect(setScaleParam, [gap]);\n  const handleImgLoadError = () => {\n    const errorFlag = onError === null || onError === void 0 ? void 0 : onError();\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  const size = useSize(ctxSize => {\n    var _a, _b;\n    return (_b = (_a = customSize !== null && customSize !== void 0 ? customSize : avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.size) !== null && _a !== void 0 ? _a : ctxSize) !== null && _b !== void 0 ? _b : 'default';\n  });\n  const needResponsive = Object.keys(typeof size === 'object' ? size || {} : {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const responsiveSizeStyle = React.useMemo(() => {\n    if (typeof size !== 'object') {\n      return {};\n    }\n    const currentBreakpoint = responsiveArray.find(screen => screens[screen]);\n    const currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      fontSize: currentSize && (icon || children) ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  const mergedShape = shape || (avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.shape) || 'circle';\n  const classString = classNames(prefixCls, sizeCls, avatar === null || avatar === void 0 ? void 0 : avatar.className, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-image`]: hasImageElement || src && isImgExist,\n    [`${prefixCls}-icon`]: !!icon\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  let childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    const transformString = `scale(${scale})`;\n    const childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      ref: avatarChildrenRef,\n      style: Object.assign({}, childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, others, {\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, sizeStyle), responsiveSizeStyle), avatar === null || avatar === void 0 ? void 0 : avatar.style), style),\n    className: classString,\n    ref: avatarNodeMergedRef\n  }), childrenToRender));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ResizeObserver", "composeRef", "responsiveArray", "devUseW<PERSON>ning", "ConfigContext", "useCSSVarCls", "useSize", "useBreakpoint", "AvatarContext", "useStyle", "Avatar", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "shape", "size", "customSize", "src", "srcSet", "icon", "className", "rootClassName", "style", "alt", "draggable", "children", "crossOrigin", "gap", "onError", "others", "scale", "setScale", "useState", "mounted", "setMounted", "isImgExist", "setIsImgExist", "avatarNodeRef", "useRef", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarNodeMergedRef", "getPrefixCls", "avatar", "useContext", "avatarCtx", "setScaleParam", "current", "children<PERSON><PERSON>th", "offsetWidth", "nodeWidth", "useEffect", "handleImgLoadError", "errorFlag", "ctxSize", "_a", "_b", "needResponsive", "keys", "some", "key", "includes", "screens", "responsiveSizeStyle", "useMemo", "currentBreakpoint", "find", "screen", "currentSize", "width", "height", "fontSize", "process", "env", "NODE_ENV", "warning", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "sizeCls", "hasImageElement", "isValidElement", "mergedShape", "classString", "sizeStyle", "children<PERSON><PERSON><PERSON><PERSON>", "createElement", "transformString", "childrenStyle", "msTransform", "WebkitTransform", "transform", "onResize", "assign", "opacity", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/avatar/Avatar.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst Avatar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      shape,\n      size: customSize,\n      src,\n      srcSet,\n      icon,\n      className,\n      rootClassName,\n      style,\n      alt,\n      draggable,\n      children,\n      crossOrigin,\n      gap = 4,\n      onError\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"rootClassName\", \"style\", \"alt\", \"draggable\", \"children\", \"crossOrigin\", \"gap\", \"onError\"]);\n  const [scale, setScale] = React.useState(1);\n  const [mounted, setMounted] = React.useState(false);\n  const [isImgExist, setIsImgExist] = React.useState(true);\n  const avatarNodeRef = React.useRef(null);\n  const avatarChildrenRef = React.useRef(null);\n  const avatarNodeMergedRef = composeRef(ref, avatarNodeRef);\n  const {\n    getPrefixCls,\n    avatar\n  } = React.useContext(ConfigContext);\n  const avatarCtx = React.useContext(AvatarContext);\n  const setScaleParam = () => {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    const childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    const nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    setIsImgExist(true);\n    setScale(1);\n  }, [src]);\n  React.useEffect(setScaleParam, [gap]);\n  const handleImgLoadError = () => {\n    const errorFlag = onError === null || onError === void 0 ? void 0 : onError();\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  const size = useSize(ctxSize => {\n    var _a, _b;\n    return (_b = (_a = customSize !== null && customSize !== void 0 ? customSize : avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.size) !== null && _a !== void 0 ? _a : ctxSize) !== null && _b !== void 0 ? _b : 'default';\n  });\n  const needResponsive = Object.keys(typeof size === 'object' ? size || {} : {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const responsiveSizeStyle = React.useMemo(() => {\n    if (typeof size !== 'object') {\n      return {};\n    }\n    const currentBreakpoint = responsiveArray.find(screen => screens[screen]);\n    const currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      fontSize: currentSize && (icon || children) ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  const mergedShape = shape || (avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.shape) || 'circle';\n  const classString = classNames(prefixCls, sizeCls, avatar === null || avatar === void 0 ? void 0 : avatar.className, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-image`]: hasImageElement || src && isImgExist,\n    [`${prefixCls}-icon`]: !!icon\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  let childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    const transformString = `scale(${scale})`;\n    const childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      ref: avatarChildrenRef,\n      style: Object.assign({}, childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, others, {\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, sizeStyle), responsiveSizeStyle), avatar === null || avatar === void 0 ? void 0 : avatar.style), style),\n    className: classString,\n    ref: avatarNodeMergedRef\n  }), childrenToRender));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,MAAM,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC3D,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLC,IAAI,EAAEC,UAAU;MAChBC,GAAG;MACHC,MAAM;MACNC,IAAI;MACJC,SAAS;MACTC,aAAa;MACbC,KAAK;MACLC,GAAG;MACHC,SAAS;MACTC,QAAQ;MACRC,WAAW;MACXC,GAAG,GAAG,CAAC;MACPC;IACF,CAAC,GAAGlB,KAAK;IACTmB,MAAM,GAAG/C,MAAM,CAAC4B,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EACzL,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,KAAK,CAACoC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGxC,KAAK,CAACoC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMK,aAAa,GAAGzC,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,iBAAiB,GAAG3C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAME,mBAAmB,GAAGzC,UAAU,CAACY,GAAG,EAAE0B,aAAa,CAAC;EAC1D,MAAM;IACJI,YAAY;IACZC;EACF,CAAC,GAAG9C,KAAK,CAAC+C,UAAU,CAACzC,aAAa,CAAC;EACnC,MAAM0C,SAAS,GAAGhD,KAAK,CAAC+C,UAAU,CAACrC,aAAa,CAAC;EACjD,MAAMuC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACN,iBAAiB,CAACO,OAAO,IAAI,CAACT,aAAa,CAACS,OAAO,EAAE;MACxD;IACF;IACA,MAAMC,aAAa,GAAGR,iBAAiB,CAACO,OAAO,CAACE,WAAW,CAAC,CAAC;IAC7D,MAAMC,SAAS,GAAGZ,aAAa,CAACS,OAAO,CAACE,WAAW;IACnD;IACA,IAAID,aAAa,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;MAC1C,IAAItB,GAAG,GAAG,CAAC,GAAGsB,SAAS,EAAE;QACvBlB,QAAQ,CAACkB,SAAS,GAAGtB,GAAG,GAAG,CAAC,GAAGoB,aAAa,GAAG,CAACE,SAAS,GAAGtB,GAAG,GAAG,CAAC,IAAIoB,aAAa,GAAG,CAAC,CAAC;MAC3F;IACF;EACF,CAAC;EACDnD,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpBhB,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNtC,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpBd,aAAa,CAAC,IAAI,CAAC;IACnBL,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAACd,GAAG,CAAC,CAAC;EACTrB,KAAK,CAACsD,SAAS,CAACL,aAAa,EAAE,CAAClB,GAAG,CAAC,CAAC;EACrC,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,SAAS,GAAGxB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC7E,IAAIwB,SAAS,KAAK,KAAK,EAAE;MACvBhB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EACD,MAAMrB,IAAI,GAAGX,OAAO,CAACiD,OAAO,IAAI;IAC9B,IAAIC,EAAE,EAAEC,EAAE;IACV,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGtC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG4B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC7B,IAAI,MAAM,IAAI,IAAIuC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS;EAC3O,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGrE,MAAM,CAACsE,IAAI,CAAC,OAAO1C,IAAI,KAAK,QAAQ,GAAGA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC2C,IAAI,CAACC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC;EAC/I,MAAME,OAAO,GAAGxD,aAAa,CAACmD,cAAc,CAAC;EAC7C,MAAMM,mBAAmB,GAAGlE,KAAK,CAACmE,OAAO,CAAC,MAAM;IAC9C,IAAI,OAAOhD,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAO,CAAC,CAAC;IACX;IACA,MAAMiD,iBAAiB,GAAGhE,eAAe,CAACiE,IAAI,CAACC,MAAM,IAAIL,OAAO,CAACK,MAAM,CAAC,CAAC;IACzE,MAAMC,WAAW,GAAGpD,IAAI,CAACiD,iBAAiB,CAAC;IAC3C,OAAOG,WAAW,GAAG;MACnBC,KAAK,EAAED,WAAW;MAClBE,MAAM,EAAEF,WAAW;MACnBG,QAAQ,EAAEH,WAAW,KAAKhD,IAAI,IAAIM,QAAQ,CAAC,GAAG0C,WAAW,GAAG,CAAC,GAAG;IAClE,CAAC,GAAG,CAAC,CAAC;EACR,CAAC,EAAE,CAACN,OAAO,EAAE9C,IAAI,CAAC,CAAC;EACnB,IAAIwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGzE,aAAa,CAAC,QAAQ,CAAC;IACvCsE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAE,OAAOvD,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACzB,MAAM,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,8EAA8EyB,IAAI,0CAA0C,CAAC,GAAG,KAAK,CAAC;EACpP;EACA,MAAMP,SAAS,GAAG6B,YAAY,CAAC,QAAQ,EAAE5B,kBAAkB,CAAC;EAC5D,MAAM8D,OAAO,GAAGxE,YAAY,CAACS,SAAS,CAAC;EACvC,MAAM,CAACgE,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGvE,QAAQ,CAACK,SAAS,EAAE+D,OAAO,CAAC;EACpE,MAAMI,OAAO,GAAGlF,UAAU,CAAC;IACzB,CAAC,GAAGe,SAAS,KAAK,GAAGG,IAAI,KAAK,OAAO;IACrC,CAAC,GAAGH,SAAS,KAAK,GAAGG,IAAI,KAAK;EAChC,CAAC,CAAC;EACF,MAAMiE,eAAe,GAAG,aAAapF,KAAK,CAACqF,cAAc,CAAChE,GAAG,CAAC;EAC9D,MAAMiE,WAAW,GAAGpE,KAAK,KAAK8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC9B,KAAK,CAAC,IAAI,QAAQ;EAChH,MAAMqE,WAAW,GAAGtF,UAAU,CAACe,SAAS,EAAEmE,OAAO,EAAErC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACtB,SAAS,EAAE,GAAGR,SAAS,IAAIsE,WAAW,EAAE,EAAE;IAClJ,CAAC,GAAGtE,SAAS,QAAQ,GAAGoE,eAAe,IAAI/D,GAAG,IAAIkB,UAAU;IAC5D,CAAC,GAAGvB,SAAS,OAAO,GAAG,CAAC,CAACO;EAC3B,CAAC,EAAE2D,SAAS,EAAEH,OAAO,EAAEvD,SAAS,EAAEC,aAAa,EAAEwD,MAAM,CAAC;EACxD,MAAMO,SAAS,GAAG,OAAOrE,IAAI,KAAK,QAAQ,GAAG;IAC3CqD,KAAK,EAAErD,IAAI;IACXsD,MAAM,EAAEtD,IAAI;IACZuD,QAAQ,EAAEnD,IAAI,GAAGJ,IAAI,GAAG,CAAC,GAAG;EAC9B,CAAC,GAAG,CAAC,CAAC;EACN,IAAIsE,gBAAgB;EACpB,IAAI,OAAOpE,GAAG,KAAK,QAAQ,IAAIkB,UAAU,EAAE;IACzCkD,gBAAgB,GAAG,aAAazF,KAAK,CAAC0F,aAAa,CAAC,KAAK,EAAE;MACzDrE,GAAG,EAAEA,GAAG;MACRO,SAAS,EAAEA,SAAS;MACpBN,MAAM,EAAEA,MAAM;MACdU,OAAO,EAAEuB,kBAAkB;MAC3B5B,GAAG,EAAEA,GAAG;MACRG,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIsD,eAAe,EAAE;IAC1BK,gBAAgB,GAAGpE,GAAG;EACxB,CAAC,MAAM,IAAIE,IAAI,EAAE;IACfkE,gBAAgB,GAAGlE,IAAI;EACzB,CAAC,MAAM,IAAIc,OAAO,IAAIH,KAAK,KAAK,CAAC,EAAE;IACjC,MAAMyD,eAAe,GAAG,SAASzD,KAAK,GAAG;IACzC,MAAM0D,aAAa,GAAG;MACpBC,WAAW,EAAEF,eAAe;MAC5BG,eAAe,EAAEH,eAAe;MAChCI,SAAS,EAAEJ;IACb,CAAC;IACDF,gBAAgB,GAAG,aAAazF,KAAK,CAAC0F,aAAa,CAACxF,cAAc,EAAE;MAClE8F,QAAQ,EAAE/C;IACZ,CAAC,EAAE,aAAajD,KAAK,CAAC0F,aAAa,CAAC,MAAM,EAAE;MAC1ClE,SAAS,EAAE,GAAGR,SAAS,SAAS;MAChCD,GAAG,EAAE4B,iBAAiB;MACtBjB,KAAK,EAAEnC,MAAM,CAAC0G,MAAM,CAAC,CAAC,CAAC,EAAEL,aAAa;IACxC,CAAC,EAAE/D,QAAQ,CAAC,CAAC;EACf,CAAC,MAAM;IACL4D,gBAAgB,GAAG,aAAazF,KAAK,CAAC0F,aAAa,CAAC,MAAM,EAAE;MAC1DlE,SAAS,EAAE,GAAGR,SAAS,SAAS;MAChCU,KAAK,EAAE;QACLwE,OAAO,EAAE;MACX,CAAC;MACDnF,GAAG,EAAE4B;IACP,CAAC,EAAEd,QAAQ,CAAC;EACd;EACA,OAAOmD,UAAU,CAAC,aAAahF,KAAK,CAAC0F,aAAa,CAAC,MAAM,EAAEnG,MAAM,CAAC0G,MAAM,CAAC,CAAC,CAAC,EAAEhE,MAAM,EAAE;IACnFP,KAAK,EAAEnC,MAAM,CAAC0G,MAAM,CAAC1G,MAAM,CAAC0G,MAAM,CAAC1G,MAAM,CAAC0G,MAAM,CAAC1G,MAAM,CAAC0G,MAAM,CAAC,CAAC,CAAC,EAAET,SAAS,CAAC,EAAEtB,mBAAmB,CAAC,EAAEpB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACpB,KAAK,CAAC,EAAEA,KAAK,CAAC;IAC1KF,SAAS,EAAE+D,WAAW;IACtBxE,GAAG,EAAE6B;EACP,CAAC,CAAC,EAAE6C,gBAAgB,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjE,MAAM,CAACuF,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAevF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}