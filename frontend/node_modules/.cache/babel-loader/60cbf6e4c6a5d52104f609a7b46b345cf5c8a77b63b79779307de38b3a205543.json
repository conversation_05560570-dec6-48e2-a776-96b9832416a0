{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { isPresetSize, isValidGapNumber } from '../_util/gapSize';\nimport { useComponentConfig } from '../config-provider/context';\nimport Compact from './Compact';\nimport { SpaceContextProvider } from './context';\nimport Item from './Item';\nimport useStyle from './style';\nexport { SpaceContext } from './context';\nconst InternalSpace = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n    getPrefixCls,\n    direction: directionConfig,\n    size: contextSize,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('space');\n  const {\n      size = contextSize !== null && contextSize !== void 0 ? contextSize : 'small',\n      align,\n      className,\n      rootClassName,\n      children,\n      direction = 'horizontal',\n      prefixCls: customizePrefixCls,\n      split,\n      style,\n      wrap = false,\n      classNames: customClassNames,\n      styles\n    } = props,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"rootClassName\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\", \"classNames\", \"styles\"]);\n  const [horizontalSize, verticalSize] = Array.isArray(size) ? size : [size, size];\n  const isPresetVerticalSize = isPresetSize(verticalSize);\n  const isPresetHorizontalSize = isPresetSize(horizontalSize);\n  const isValidVerticalSize = isValidGapNumber(verticalSize);\n  const isValidHorizontalSize = isValidGapNumber(horizontalSize);\n  const childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  const mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  const prefixCls = getPrefixCls('space', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, contextClassName, hashId, `${prefixCls}-${direction}`, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-align-${mergedAlign}`]: mergedAlign,\n    [`${prefixCls}-gap-row-${verticalSize}`]: isPresetVerticalSize,\n    [`${prefixCls}-gap-col-${horizontalSize}`]: isPresetHorizontalSize\n  }, className, rootClassName, cssVarCls);\n  const itemClassName = classNames(`${prefixCls}-item`, (_a = customClassNames === null || customClassNames === void 0 ? void 0 : customClassNames.item) !== null && _a !== void 0 ? _a : contextClassNames.item);\n  // Calculate latest one\n  let latestIndex = 0;\n  const nodes = childNodes.map((child, i) => {\n    var _a;\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    const key = (child === null || child === void 0 ? void 0 : child.key) || `${itemClassName}-${i}`;\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      index: i,\n      split: split,\n      style: (_a = styles === null || styles === void 0 ? void 0 : styles.item) !== null && _a !== void 0 ? _a : contextStyles.item\n    }, child);\n  });\n  const spaceContext = React.useMemo(() => ({\n    latestIndex\n  }), [latestIndex]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  const gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n  }\n  if (!isPresetHorizontalSize && isValidHorizontalSize) {\n    gapStyle.columnGap = horizontalSize;\n  }\n  if (!isPresetVerticalSize && isValidVerticalSize) {\n    gapStyle.rowGap = verticalSize;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: cls,\n    style: Object.assign(Object.assign(Object.assign({}, gapStyle), contextStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContextProvider, {\n    value: spaceContext\n  }, nodes)));\n});\nconst Space = InternalSpace;\nSpace.Compact = Compact;\nif (process.env.NODE_ENV !== 'production') {\n  Space.displayName = 'Space';\n}\nexport default Space;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "toArray", "isPresetSize", "isValidGapNumber", "useComponentConfig", "Compact", "SpaceContextProvider", "<PERSON><PERSON>", "useStyle", "SpaceContext", "InternalSpace", "forwardRef", "props", "ref", "_a", "getPrefixCls", "direction", "directionConfig", "size", "contextSize", "className", "contextClassName", "style", "contextStyle", "contextClassNames", "styles", "contextStyles", "align", "rootClassName", "children", "prefixCls", "customizePrefixCls", "split", "wrap", "customClassNames", "otherProps", "horizontalSize", "verticalSize", "Array", "isArray", "isPresetVerticalSize", "isPresetHorizontalSize", "isValidVerticalSize", "isValidHorizontalSize", "childNodes", "keepEmpty", "mergedAlign", "undefined", "wrapCSSVar", "hashId", "cssVarCls", "cls", "itemClassName", "item", "latestIndex", "nodes", "map", "child", "key", "createElement", "index", "spaceContext", "useMemo", "gapStyle", "flexWrap", "columnGap", "rowGap", "assign", "value", "Space", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/space/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { isPresetSize, isValidGapNumber } from '../_util/gapSize';\nimport { useComponentConfig } from '../config-provider/context';\nimport Compact from './Compact';\nimport { SpaceContextProvider } from './context';\nimport Item from './Item';\nimport useStyle from './style';\nexport { SpaceContext } from './context';\nconst InternalSpace = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n    getPrefixCls,\n    direction: directionConfig,\n    size: contextSize,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('space');\n  const {\n      size = contextSize !== null && contextSize !== void 0 ? contextSize : 'small',\n      align,\n      className,\n      rootClassName,\n      children,\n      direction = 'horizontal',\n      prefixCls: customizePrefixCls,\n      split,\n      style,\n      wrap = false,\n      classNames: customClassNames,\n      styles\n    } = props,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"rootClassName\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\", \"classNames\", \"styles\"]);\n  const [horizontalSize, verticalSize] = Array.isArray(size) ? size : [size, size];\n  const isPresetVerticalSize = isPresetSize(verticalSize);\n  const isPresetHorizontalSize = isPresetSize(horizontalSize);\n  const isValidVerticalSize = isValidGapNumber(verticalSize);\n  const isValidHorizontalSize = isValidGapNumber(horizontalSize);\n  const childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  const mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  const prefixCls = getPrefixCls('space', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, contextClassName, hashId, `${prefixCls}-${direction}`, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-align-${mergedAlign}`]: mergedAlign,\n    [`${prefixCls}-gap-row-${verticalSize}`]: isPresetVerticalSize,\n    [`${prefixCls}-gap-col-${horizontalSize}`]: isPresetHorizontalSize\n  }, className, rootClassName, cssVarCls);\n  const itemClassName = classNames(`${prefixCls}-item`, (_a = customClassNames === null || customClassNames === void 0 ? void 0 : customClassNames.item) !== null && _a !== void 0 ? _a : contextClassNames.item);\n  // Calculate latest one\n  let latestIndex = 0;\n  const nodes = childNodes.map((child, i) => {\n    var _a;\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    const key = (child === null || child === void 0 ? void 0 : child.key) || `${itemClassName}-${i}`;\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      index: i,\n      split: split,\n      style: (_a = styles === null || styles === void 0 ? void 0 : styles.item) !== null && _a !== void 0 ? _a : contextStyles.item\n    }, child);\n  });\n  const spaceContext = React.useMemo(() => ({\n    latestIndex\n  }), [latestIndex]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  const gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n  }\n  if (!isPresetHorizontalSize && isValidHorizontalSize) {\n    gapStyle.columnGap = horizontalSize;\n  }\n  if (!isPresetVerticalSize && isValidVerticalSize) {\n    gapStyle.rowGap = verticalSize;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: cls,\n    style: Object.assign(Object.assign(Object.assign({}, gapStyle), contextStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContextProvider, {\n    value: spaceContext\n  }, nodes)));\n});\nconst Space = InternalSpace;\nSpace.Compact = Compact;\nif (process.env.NODE_ENV !== 'production') {\n  Space.displayName = 'Space';\n}\nexport default Space;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,kBAAkB;AACjE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,oBAAoB,QAAQ,WAAW;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,aAAa,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAClE,IAAIC,EAAE;EACN,MAAM;IACJC,YAAY;IACZC,SAAS,EAAEC,eAAe;IAC1BC,IAAI,EAAEC,WAAW;IACjBC,SAAS,EAAEC,gBAAgB;IAC3BC,KAAK,EAAEC,YAAY;IACnBvB,UAAU,EAAEwB,iBAAiB;IAC7BC,MAAM,EAAEC;EACV,CAAC,GAAGtB,kBAAkB,CAAC,OAAO,CAAC;EAC/B,MAAM;MACFc,IAAI,GAAGC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,OAAO;MAC7EQ,KAAK;MACLP,SAAS;MACTQ,aAAa;MACbC,QAAQ;MACRb,SAAS,GAAG,YAAY;MACxBc,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLV,KAAK;MACLW,IAAI,GAAG,KAAK;MACZjC,UAAU,EAAEkC,gBAAgB;MAC5BT;IACF,CAAC,GAAGb,KAAK;IACTuB,UAAU,GAAGlD,MAAM,CAAC2B,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;EACrK,MAAM,CAACwB,cAAc,EAAEC,YAAY,CAAC,GAAGC,KAAK,CAACC,OAAO,CAACrB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;EAChF,MAAMsB,oBAAoB,GAAGtC,YAAY,CAACmC,YAAY,CAAC;EACvD,MAAMI,sBAAsB,GAAGvC,YAAY,CAACkC,cAAc,CAAC;EAC3D,MAAMM,mBAAmB,GAAGvC,gBAAgB,CAACkC,YAAY,CAAC;EAC1D,MAAMM,qBAAqB,GAAGxC,gBAAgB,CAACiC,cAAc,CAAC;EAC9D,MAAMQ,UAAU,GAAG3C,OAAO,CAAC4B,QAAQ,EAAE;IACnCgB,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGnB,KAAK,KAAKoB,SAAS,IAAI/B,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAGW,KAAK;EACxF,MAAMG,SAAS,GAAGf,YAAY,CAAC,OAAO,EAAEgB,kBAAkB,CAAC;EAC3D,MAAM,CAACiB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAACsB,SAAS,CAAC;EAC3D,MAAMqB,GAAG,GAAGnD,UAAU,CAAC8B,SAAS,EAAET,gBAAgB,EAAE4B,MAAM,EAAE,GAAGnB,SAAS,IAAId,SAAS,EAAE,EAAE;IACvF,CAAC,GAAGc,SAAS,MAAM,GAAGb,eAAe,KAAK,KAAK;IAC/C,CAAC,GAAGa,SAAS,UAAUgB,WAAW,EAAE,GAAGA,WAAW;IAClD,CAAC,GAAGhB,SAAS,YAAYO,YAAY,EAAE,GAAGG,oBAAoB;IAC9D,CAAC,GAAGV,SAAS,YAAYM,cAAc,EAAE,GAAGK;EAC9C,CAAC,EAAErB,SAAS,EAAEQ,aAAa,EAAEsB,SAAS,CAAC;EACvC,MAAME,aAAa,GAAGpD,UAAU,CAAC,GAAG8B,SAAS,OAAO,EAAE,CAAChB,EAAE,GAAGoB,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACmB,IAAI,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGU,iBAAiB,CAAC6B,IAAI,CAAC;EAC/M;EACA,IAAIC,WAAW,GAAG,CAAC;EACnB,MAAMC,KAAK,GAAGX,UAAU,CAACY,GAAG,CAAC,CAACC,KAAK,EAAE7D,CAAC,KAAK;IACzC,IAAIkB,EAAE;IACN,IAAI2C,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKV,SAAS,EAAE;MACzCO,WAAW,GAAG1D,CAAC;IACjB;IACA,MAAM8D,GAAG,GAAG,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,GAAG,KAAK,GAAGN,aAAa,IAAIxD,CAAC,EAAE;IAChG,OAAO,aAAaG,KAAK,CAAC4D,aAAa,CAACpD,IAAI,EAAE;MAC5Ca,SAAS,EAAEgC,aAAa;MACxBM,GAAG,EAAEA,GAAG;MACRE,KAAK,EAAEhE,CAAC;MACRoC,KAAK,EAAEA,KAAK;MACZV,KAAK,EAAE,CAACR,EAAE,GAAGW,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4B,IAAI,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGY,aAAa,CAAC2B;IAC3H,CAAC,EAAEI,KAAK,CAAC;EACX,CAAC,CAAC;EACF,MAAMI,YAAY,GAAG9D,KAAK,CAAC+D,OAAO,CAAC,OAAO;IACxCR;EACF,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAClB;EACA,IAAIV,UAAU,CAAC/C,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,MAAMkE,QAAQ,GAAG,CAAC,CAAC;EACnB,IAAI9B,IAAI,EAAE;IACR8B,QAAQ,CAACC,QAAQ,GAAG,MAAM;EAC5B;EACA,IAAI,CAACvB,sBAAsB,IAAIE,qBAAqB,EAAE;IACpDoB,QAAQ,CAACE,SAAS,GAAG7B,cAAc;EACrC;EACA,IAAI,CAACI,oBAAoB,IAAIE,mBAAmB,EAAE;IAChDqB,QAAQ,CAACG,MAAM,GAAG7B,YAAY;EAChC;EACA,OAAOW,UAAU,CAAC,aAAajD,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAErE,MAAM,CAAC6E,MAAM,CAAC;IACtEtD,GAAG,EAAEA,GAAG;IACRO,SAAS,EAAE+B,GAAG;IACd7B,KAAK,EAAEhC,MAAM,CAAC6E,MAAM,CAAC7E,MAAM,CAAC6E,MAAM,CAAC7E,MAAM,CAAC6E,MAAM,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAAC,EAAExC,YAAY,CAAC,EAAED,KAAK;EACtF,CAAC,EAAEa,UAAU,CAAC,EAAE,aAAapC,KAAK,CAAC4D,aAAa,CAACrD,oBAAoB,EAAE;IACrE8D,KAAK,EAAEP;EACT,CAAC,EAAEN,KAAK,CAAC,CAAC,CAAC;AACb,CAAC,CAAC;AACF,MAAMc,KAAK,GAAG3D,aAAa;AAC3B2D,KAAK,CAAChE,OAAO,GAAGA,OAAO;AACvB,IAAIiE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,CAACI,WAAW,GAAG,OAAO;AAC7B;AACA,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}