{"ast": null, "code": "\"use client\";\n\nimport getDesignToken from './getDesignToken';\nimport { defaultConfig, DesignTokenContext as InternalDesignTokenContext, useToken as useInternalToken } from './internal';\nimport compactAlgorithm from './themes/compact';\nimport darkAlgorithm from './themes/dark';\nimport defaultAlgorithm from './themes/default';\n// ZombieJ: We export as object to user but array in internal.\n// This is used to minimize the bundle size for antd package but safe to refactor as object also.\n// Please do not export internal `useToken` directly to avoid something export unexpected.\n/** Get current context Design Token. Will be different if you are using nest theme config. */\nfunction useToken() {\n  const [theme, token, hashId] = useInternalToken();\n  return {\n    theme,\n    token,\n    hashId\n  };\n}\nexport default {\n  /** Default seedToken */\n  defaultSeed: defaultConfig.token,\n  useToken,\n  defaultAlgorithm,\n  darkAlgorithm,\n  compactAlgorithm,\n  getDesignToken,\n  /**\n   * @private Private variable\n   * @warring 🔥 Do not use in production. 🔥\n   */\n  defaultConfig,\n  /**\n   * @private Private variable\n   * @warring 🔥 Do not use in production. 🔥\n   */\n  _internalContext: InternalDesignTokenContext\n};", "map": {"version": 3, "names": ["getDesignToken", "defaultConfig", "DesignTokenContext", "InternalDesignTokenContext", "useToken", "useInternalToken", "compactAlgorithm", "darkAlgorithm", "defaultAlgorithm", "theme", "token", "hashId", "defaultSeed", "_internalContext"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/theme/index.js"], "sourcesContent": ["\"use client\";\n\nimport getDesignToken from './getDesignToken';\nimport { defaultConfig, DesignTokenContext as InternalDesignTokenContext, useToken as useInternalToken } from './internal';\nimport compactAlgorithm from './themes/compact';\nimport darkAlgorithm from './themes/dark';\nimport defaultAlgorithm from './themes/default';\n// ZombieJ: We export as object to user but array in internal.\n// This is used to minimize the bundle size for antd package but safe to refactor as object also.\n// Please do not export internal `useToken` directly to avoid something export unexpected.\n/** Get current context Design Token. Will be different if you are using nest theme config. */\nfunction useToken() {\n  const [theme, token, hashId] = useInternalToken();\n  return {\n    theme,\n    token,\n    hashId\n  };\n}\nexport default {\n  /** Default seedToken */\n  defaultSeed: defaultConfig.token,\n  useToken,\n  defaultAlgorithm,\n  darkAlgorithm,\n  compactAlgorithm,\n  getDesignToken,\n  /**\n   * @private Private variable\n   * @warring 🔥 Do not use in production. 🔥\n   */\n  defaultConfig,\n  /**\n   * @private Private variable\n   * @warring 🔥 Do not use in production. 🔥\n   */\n  _internalContext: InternalDesignTokenContext\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,SAASC,aAAa,EAAEC,kBAAkB,IAAIC,0BAA0B,EAAEC,QAAQ,IAAIC,gBAAgB,QAAQ,YAAY;AAC1H,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C;AACA;AACA;AACA;AACA,SAASJ,QAAQA,CAAA,EAAG;EAClB,MAAM,CAACK,KAAK,EAAEC,KAAK,EAAEC,MAAM,CAAC,GAAGN,gBAAgB,CAAC,CAAC;EACjD,OAAO;IACLI,KAAK;IACLC,KAAK;IACLC;EACF,CAAC;AACH;AACA,eAAe;EACb;EACAC,WAAW,EAAEX,aAAa,CAACS,KAAK;EAChCN,QAAQ;EACRI,gBAAgB;EAChBD,aAAa;EACbD,gBAAgB;EAChBN,cAAc;EACd;AACF;AACA;AACA;EACEC,aAAa;EACb;AACF;AACA;AACA;EACEY,gBAAgB,EAAEV;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}