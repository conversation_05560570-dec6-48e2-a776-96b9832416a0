{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport Upload from './Upload';\nconst Dragger = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      style,\n      height,\n      hasControlInside = false\n    } = _a,\n    restProps = __rest(_a, [\"style\", \"height\", \"hasControlInside\"]);\n  return /*#__PURE__*/React.createElement(Upload, Object.assign({\n    ref: ref,\n    hasControlInside: hasControlInside\n  }, restProps, {\n    type: \"drag\",\n    style: Object.assign(Object.assign({}, style), {\n      height\n    })\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Dragger.displayName = 'Dragger';\n}\nexport default Dragger;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "Upload", "<PERSON><PERSON>", "forwardRef", "_a", "ref", "style", "height", "hasControlInside", "restProps", "createElement", "assign", "type", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/upload/Dragger.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport Upload from './Upload';\nconst Dragger = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      style,\n      height,\n      hasControlInside = false\n    } = _a,\n    restProps = __rest(_a, [\"style\", \"height\", \"hasControlInside\"]);\n  return /*#__PURE__*/React.createElement(Upload, Object.assign({\n    ref: ref,\n    hasControlInside: hasControlInside\n  }, restProps, {\n    type: \"drag\",\n    style: Object.assign(Object.assign({}, style), {\n      height\n    })\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Dragger.displayName = 'Dragger';\n}\nexport default Dragger;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,MAAMC,OAAO,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,CAACC,EAAE,EAAEC,GAAG,KAAK;EACzD,IAAI;MACAC,KAAK;MACLC,MAAM;MACNC,gBAAgB,GAAG;IACrB,CAAC,GAAGJ,EAAE;IACNK,SAAS,GAAGvB,MAAM,CAACkB,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;EACjE,OAAO,aAAaJ,KAAK,CAACU,aAAa,CAACT,MAAM,EAAEV,MAAM,CAACoB,MAAM,CAAC;IAC5DN,GAAG,EAAEA,GAAG;IACRG,gBAAgB,EAAEA;EACpB,CAAC,EAAEC,SAAS,EAAE;IACZG,IAAI,EAAE,MAAM;IACZN,KAAK,EAAEf,MAAM,CAACoB,MAAM,CAACpB,MAAM,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE;MAC7CC;IACF,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCb,OAAO,CAACc,WAAW,GAAG,SAAS;AACjC;AACA,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}