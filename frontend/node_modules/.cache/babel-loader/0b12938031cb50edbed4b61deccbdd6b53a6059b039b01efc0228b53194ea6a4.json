{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { ConfigContext } from '../config-provider';\nimport useBreakpoint from './hooks/useBreakpoint';\nimport useGutter from './hooks/useGutter';\nimport RowContext from './RowContext';\nimport { useRowStyle } from './style';\nconst _RowAligns = ['top', 'middle', 'bottom', 'stretch'];\nconst _RowJustify = ['start', 'end', 'center', 'space-around', 'space-between', 'space-evenly'];\nfunction useMergedPropByScreen(oriProp, screen) {\n  const [prop, setProp] = React.useState(typeof oriProp === 'string' ? oriProp : '');\n  const calcMergedAlignOrJustify = () => {\n    if (typeof oriProp === 'string') {\n      setProp(oriProp);\n    }\n    if (typeof oriProp !== 'object') {\n      return;\n    }\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      // if do not match, do nothing\n      if (!screen || !screen[breakpoint]) {\n        continue;\n      }\n      const curVal = oriProp[breakpoint];\n      if (curVal !== undefined) {\n        setProp(curVal);\n        return;\n      }\n    }\n  };\n  React.useEffect(() => {\n    calcMergedAlignOrJustify();\n  }, [JSON.stringify(oriProp), screen]);\n  return prop;\n}\nconst Row = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      justify,\n      align,\n      className,\n      style,\n      children,\n      gutter = 0,\n      wrap\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"justify\", \"align\", \"className\", \"style\", \"children\", \"gutter\", \"wrap\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const screens = useBreakpoint(true, null);\n  const mergedAlign = useMergedPropByScreen(align, screens);\n  const mergedJustify = useMergedPropByScreen(justify, screens);\n  const prefixCls = getPrefixCls('row', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useRowStyle(prefixCls);\n  const gutters = useGutter(gutter, screens);\n  const classes = classNames(prefixCls, {\n    [`${prefixCls}-no-wrap`]: wrap === false,\n    [`${prefixCls}-${mergedJustify}`]: mergedJustify,\n    [`${prefixCls}-${mergedAlign}`]: mergedAlign,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, hashId, cssVarCls);\n  // Add gutter related style\n  const rowStyle = {};\n  const horizontalGutter = gutters[0] != null && gutters[0] > 0 ? gutters[0] / -2 : undefined;\n  if (horizontalGutter) {\n    rowStyle.marginLeft = horizontalGutter;\n    rowStyle.marginRight = horizontalGutter;\n  }\n  // \"gutters\" is a new array in each rendering phase, it'll make 'React.useMemo' effectless.\n  // So we deconstruct \"gutters\" variable here.\n  const [gutterH, gutterV] = gutters;\n  rowStyle.rowGap = gutterV;\n  const rowContext = React.useMemo(() => ({\n    gutter: [gutterH, gutterV],\n    wrap\n  }), [gutterH, gutterV, wrap]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RowContext.Provider, {\n    value: rowContext\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classes,\n    style: Object.assign(Object.assign({}, rowStyle), style),\n    ref: ref\n  }), children)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Row.displayName = 'Row';\n}\nexport default Row;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "responsiveArray", "ConfigContext", "useBreakpoint", "useGutter", "RowContext", "useRowStyle", "_RowAligns", "_RowJustify", "useMergedPropByScreen", "oriProp", "screen", "prop", "setProp", "useState", "calcMergedAlignOrJustify", "breakpoint", "curVal", "undefined", "useEffect", "JSON", "stringify", "Row", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "justify", "align", "className", "style", "children", "gutter", "wrap", "others", "getPrefixCls", "direction", "useContext", "screens", "mergedAlign", "mergedJustify", "wrapCSSVar", "hashId", "cssVarCls", "gutters", "classes", "rowStyle", "horizontalGutter", "marginLeft", "marginRight", "gutterH", "gutterV", "rowGap", "rowContext", "useMemo", "createElement", "Provider", "value", "assign", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/grid/row.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { ConfigContext } from '../config-provider';\nimport useBreakpoint from './hooks/useBreakpoint';\nimport useGutter from './hooks/useGutter';\nimport RowContext from './RowContext';\nimport { useRowStyle } from './style';\nconst _RowAligns = ['top', 'middle', 'bottom', 'stretch'];\nconst _RowJustify = ['start', 'end', 'center', 'space-around', 'space-between', 'space-evenly'];\nfunction useMergedPropByScreen(oriProp, screen) {\n  const [prop, setProp] = React.useState(typeof oriProp === 'string' ? oriProp : '');\n  const calcMergedAlignOrJustify = () => {\n    if (typeof oriProp === 'string') {\n      setProp(oriProp);\n    }\n    if (typeof oriProp !== 'object') {\n      return;\n    }\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      // if do not match, do nothing\n      if (!screen || !screen[breakpoint]) {\n        continue;\n      }\n      const curVal = oriProp[breakpoint];\n      if (curVal !== undefined) {\n        setProp(curVal);\n        return;\n      }\n    }\n  };\n  React.useEffect(() => {\n    calcMergedAlignOrJustify();\n  }, [JSON.stringify(oriProp), screen]);\n  return prop;\n}\nconst Row = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      justify,\n      align,\n      className,\n      style,\n      children,\n      gutter = 0,\n      wrap\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"justify\", \"align\", \"className\", \"style\", \"children\", \"gutter\", \"wrap\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const screens = useBreakpoint(true, null);\n  const mergedAlign = useMergedPropByScreen(align, screens);\n  const mergedJustify = useMergedPropByScreen(justify, screens);\n  const prefixCls = getPrefixCls('row', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useRowStyle(prefixCls);\n  const gutters = useGutter(gutter, screens);\n  const classes = classNames(prefixCls, {\n    [`${prefixCls}-no-wrap`]: wrap === false,\n    [`${prefixCls}-${mergedJustify}`]: mergedJustify,\n    [`${prefixCls}-${mergedAlign}`]: mergedAlign,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, hashId, cssVarCls);\n  // Add gutter related style\n  const rowStyle = {};\n  const horizontalGutter = gutters[0] != null && gutters[0] > 0 ? gutters[0] / -2 : undefined;\n  if (horizontalGutter) {\n    rowStyle.marginLeft = horizontalGutter;\n    rowStyle.marginRight = horizontalGutter;\n  }\n  // \"gutters\" is a new array in each rendering phase, it'll make 'React.useMemo' effectless.\n  // So we deconstruct \"gutters\" variable here.\n  const [gutterH, gutterV] = gutters;\n  rowStyle.rowGap = gutterV;\n  const rowContext = React.useMemo(() => ({\n    gutter: [gutterH, gutterV],\n    wrap\n  }), [gutterH, gutterV, wrap]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RowContext.Provider, {\n    value: rowContext\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classes,\n    style: Object.assign(Object.assign({}, rowStyle), style),\n    ref: ref\n  }), children)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Row.displayName = 'Row';\n}\nexport default Row;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,WAAW,QAAQ,SAAS;AACrC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;AACzD,MAAMC,WAAW,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,CAAC;AAC/F,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,KAAK,CAACe,QAAQ,CAAC,OAAOJ,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,EAAE,CAAC;EAClF,MAAMK,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,OAAOL,OAAO,KAAK,QAAQ,EAAE;MAC/BG,OAAO,CAACH,OAAO,CAAC;IAClB;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B;IACF;IACA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,eAAe,CAACJ,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAMoB,UAAU,GAAGf,eAAe,CAACL,CAAC,CAAC;MACrC;MACA,IAAI,CAACe,MAAM,IAAI,CAACA,MAAM,CAACK,UAAU,CAAC,EAAE;QAClC;MACF;MACA,MAAMC,MAAM,GAAGP,OAAO,CAACM,UAAU,CAAC;MAClC,IAAIC,MAAM,KAAKC,SAAS,EAAE;QACxBL,OAAO,CAACI,MAAM,CAAC;QACf;MACF;IACF;EACF,CAAC;EACDlB,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpBJ,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACK,IAAI,CAACC,SAAS,CAACX,OAAO,CAAC,EAAEC,MAAM,CAAC,CAAC;EACrC,OAAOC,IAAI;AACb;AACA,MAAMU,GAAG,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACxD,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,OAAO;MACPC,KAAK;MACLC,SAAS;MACTC,KAAK;MACLC,QAAQ;MACRC,MAAM,GAAG,CAAC;MACVC;IACF,CAAC,GAAGV,KAAK;IACTW,MAAM,GAAGlD,MAAM,CAACuC,KAAK,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC/G,MAAM;IACJY,YAAY;IACZC;EACF,CAAC,GAAGtC,KAAK,CAACuC,UAAU,CAACpC,aAAa,CAAC;EACnC,MAAMqC,OAAO,GAAGpC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMqC,WAAW,GAAG/B,qBAAqB,CAACoB,KAAK,EAAEU,OAAO,CAAC;EACzD,MAAME,aAAa,GAAGhC,qBAAqB,CAACmB,OAAO,EAAEW,OAAO,CAAC;EAC7D,MAAMb,SAAS,GAAGU,YAAY,CAAC,KAAK,EAAET,kBAAkB,CAAC;EACzD,MAAM,CAACe,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,WAAW,CAACoB,SAAS,CAAC;EAC9D,MAAMmB,OAAO,GAAGzC,SAAS,CAAC6B,MAAM,EAAEM,OAAO,CAAC;EAC1C,MAAMO,OAAO,GAAG9C,UAAU,CAAC0B,SAAS,EAAE;IACpC,CAAC,GAAGA,SAAS,UAAU,GAAGQ,IAAI,KAAK,KAAK;IACxC,CAAC,GAAGR,SAAS,IAAIe,aAAa,EAAE,GAAGA,aAAa;IAChD,CAAC,GAAGf,SAAS,IAAIc,WAAW,EAAE,GAAGA,WAAW;IAC5C,CAAC,GAAGd,SAAS,MAAM,GAAGW,SAAS,KAAK;EACtC,CAAC,EAAEP,SAAS,EAAEa,MAAM,EAAEC,SAAS,CAAC;EAChC;EACA,MAAMG,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,gBAAgB,GAAGH,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG3B,SAAS;EAC3F,IAAI8B,gBAAgB,EAAE;IACpBD,QAAQ,CAACE,UAAU,GAAGD,gBAAgB;IACtCD,QAAQ,CAACG,WAAW,GAAGF,gBAAgB;EACzC;EACA;EACA;EACA,MAAM,CAACG,OAAO,EAAEC,OAAO,CAAC,GAAGP,OAAO;EAClCE,QAAQ,CAACM,MAAM,GAAGD,OAAO;EACzB,MAAME,UAAU,GAAGvD,KAAK,CAACwD,OAAO,CAAC,OAAO;IACtCtB,MAAM,EAAE,CAACkB,OAAO,EAAEC,OAAO,CAAC;IAC1BlB;EACF,CAAC,CAAC,EAAE,CAACiB,OAAO,EAAEC,OAAO,EAAElB,IAAI,CAAC,CAAC;EAC7B,OAAOQ,UAAU,CAAC,aAAa3C,KAAK,CAACyD,aAAa,CAACnD,UAAU,CAACoD,QAAQ,EAAE;IACtEC,KAAK,EAAEJ;EACT,CAAC,EAAE,aAAavD,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAElE,MAAM,CAACqE,MAAM,CAAC,CAAC,CAAC,EAAExB,MAAM,EAAE;IACnEL,SAAS,EAAEgB,OAAO;IAClBf,KAAK,EAAEzC,MAAM,CAACqE,MAAM,CAACrE,MAAM,CAACqE,MAAM,CAAC,CAAC,CAAC,EAAEZ,QAAQ,CAAC,EAAEhB,KAAK,CAAC;IACxDN,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEO,QAAQ,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC;AACF,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCxC,GAAG,CAACyC,WAAW,GAAG,KAAK;AACzB;AACA,eAAezC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}