{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport { NoFormStyle } from '../form/context';\nimport { NoCompactStyle } from '../space/Compact';\nconst ContextIsolator = props => {\n  const {\n    space,\n    form,\n    children\n  } = props;\n  if (children === undefined || children === null) {\n    return null;\n  }\n  let result = children;\n  if (form) {\n    result = /*#__PURE__*/React.createElement(NoFormStyle, {\n      override: true,\n      status: true\n    }, result);\n  }\n  if (space) {\n    result = /*#__PURE__*/React.createElement(NoCompactStyle, null, result);\n  }\n  return result;\n};\nexport default ContextIsolator;", "map": {"version": 3, "names": ["React", "NoFormStyle", "NoCompactStyle", "ContextIsolator", "props", "space", "form", "children", "undefined", "result", "createElement", "override", "status"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/ContextIsolator.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { NoFormStyle } from '../form/context';\nimport { NoCompactStyle } from '../space/Compact';\nconst ContextIsolator = props => {\n  const {\n    space,\n    form,\n    children\n  } = props;\n  if (children === undefined || children === null) {\n    return null;\n  }\n  let result = children;\n  if (form) {\n    result = /*#__PURE__*/React.createElement(NoFormStyle, {\n      override: true,\n      status: true\n    }, result);\n  }\n  if (space) {\n    result = /*#__PURE__*/React.createElement(NoCompactStyle, null, result);\n  }\n  return result;\n};\nexport default ContextIsolator;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGH,KAAK;EACT,IAAIG,QAAQ,KAAKC,SAAS,IAAID,QAAQ,KAAK,IAAI,EAAE;IAC/C,OAAO,IAAI;EACb;EACA,IAAIE,MAAM,GAAGF,QAAQ;EACrB,IAAID,IAAI,EAAE;IACRG,MAAM,GAAG,aAAaT,KAAK,CAACU,aAAa,CAACT,WAAW,EAAE;MACrDU,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;IACV,CAAC,EAAEH,MAAM,CAAC;EACZ;EACA,IAAIJ,KAAK,EAAE;IACTI,MAAM,GAAG,aAAaT,KAAK,CAACU,aAAa,CAACR,cAAc,EAAE,IAAI,EAAEO,MAAM,CAAC;EACzE;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}