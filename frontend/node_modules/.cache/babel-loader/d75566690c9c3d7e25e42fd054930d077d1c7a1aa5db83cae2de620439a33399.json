{"ast": null, "code": "\"use client\";\n\nimport Table from './Table';\nexport default Table;", "map": {"version": 3, "names": ["Table"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/table/index.js"], "sourcesContent": ["\"use client\";\n\nimport Table from './Table';\nexport default Table;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,SAAS;AAC3B,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}