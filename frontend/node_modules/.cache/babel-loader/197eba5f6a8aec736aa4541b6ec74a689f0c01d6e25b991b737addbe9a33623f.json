{"ast": null, "code": "import { useCallback, useState } from 'react';\n/**\n * @title multipleSelect hooks\n * @description multipleSelect by hold down shift key\n */\nexport default function useMultipleSelect(getKey) {\n  const [prevSelectedIndex, setPrevSelectedIndex] = useState(null);\n  const multipleSelect = useCallback((currentSelectedIndex, data, selectedKeys) => {\n    const configPrevSelectedIndex = prevSelectedIndex !== null && prevSelectedIndex !== void 0 ? prevSelectedIndex : currentSelectedIndex;\n    // add/delete the selected range\n    const startIndex = Math.min(configPrevSelectedIndex || 0, currentSelectedIndex);\n    const endIndex = Math.max(configPrevSelectedIndex || 0, currentSelectedIndex);\n    const rangeKeys = data.slice(startIndex, endIndex + 1).map(item => getKey(item));\n    const shouldSelected = rangeKeys.some(rangeKey => !selectedKeys.has(rangeKey));\n    const changedKeys = [];\n    rangeKeys.forEach(item => {\n      if (shouldSelected) {\n        if (!selectedKeys.has(item)) {\n          changedKeys.push(item);\n        }\n        selectedKeys.add(item);\n      } else {\n        selectedKeys.delete(item);\n        changedKeys.push(item);\n      }\n    });\n    setPrevSelectedIndex(shouldSelected ? endIndex : null);\n    return changedKeys;\n  }, [prevSelectedIndex]);\n  const updatePrevSelectedIndex = val => {\n    setPrevSelectedIndex(val);\n  };\n  return [multipleSelect, updatePrevSelectedIndex];\n}", "map": {"version": 3, "names": ["useCallback", "useState", "useMultipleSelect", "<PERSON><PERSON><PERSON>", "prevSelectedIndex", "setPrevSelectedIndex", "multipleSelect", "currentSelectedIndex", "data", "<PERSON><PERSON><PERSON><PERSON>", "configPrevSelectedIndex", "startIndex", "Math", "min", "endIndex", "max", "rangeKeys", "slice", "map", "item", "shouldSelected", "some", "rangeKey", "has", "changed<PERSON><PERSON><PERSON>", "for<PERSON>ach", "push", "add", "delete", "updatePrevSelectedIndex", "val"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/hooks/useMultipleSelect.js"], "sourcesContent": ["import { useCallback, useState } from 'react';\n/**\n * @title multipleSelect hooks\n * @description multipleSelect by hold down shift key\n */\nexport default function useMultipleSelect(getKey) {\n  const [prevSelectedIndex, setPrevSelectedIndex] = useState(null);\n  const multipleSelect = useCallback((currentSelectedIndex, data, selectedKeys) => {\n    const configPrevSelectedIndex = prevSelectedIndex !== null && prevSelectedIndex !== void 0 ? prevSelectedIndex : currentSelectedIndex;\n    // add/delete the selected range\n    const startIndex = Math.min(configPrevSelectedIndex || 0, currentSelectedIndex);\n    const endIndex = Math.max(configPrevSelectedIndex || 0, currentSelectedIndex);\n    const rangeKeys = data.slice(startIndex, endIndex + 1).map(item => getKey(item));\n    const shouldSelected = rangeKeys.some(rangeKey => !selectedKeys.has(rangeKey));\n    const changedKeys = [];\n    rangeKeys.forEach(item => {\n      if (shouldSelected) {\n        if (!selectedKeys.has(item)) {\n          changedKeys.push(item);\n        }\n        selectedKeys.add(item);\n      } else {\n        selectedKeys.delete(item);\n        changedKeys.push(item);\n      }\n    });\n    setPrevSelectedIndex(shouldSelected ? endIndex : null);\n    return changedKeys;\n  }, [prevSelectedIndex]);\n  const updatePrevSelectedIndex = val => {\n    setPrevSelectedIndex(val);\n  };\n  return [multipleSelect, updatePrevSelectedIndex];\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAC7C;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EAChD,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGJ,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAMK,cAAc,GAAGN,WAAW,CAAC,CAACO,oBAAoB,EAAEC,IAAI,EAAEC,YAAY,KAAK;IAC/E,MAAMC,uBAAuB,GAAGN,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGG,oBAAoB;IACrI;IACA,MAAMI,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACH,uBAAuB,IAAI,CAAC,EAAEH,oBAAoB,CAAC;IAC/E,MAAMO,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAACL,uBAAuB,IAAI,CAAC,EAAEH,oBAAoB,CAAC;IAC7E,MAAMS,SAAS,GAAGR,IAAI,CAACS,KAAK,CAACN,UAAU,EAAEG,QAAQ,GAAG,CAAC,CAAC,CAACI,GAAG,CAACC,IAAI,IAAIhB,MAAM,CAACgB,IAAI,CAAC,CAAC;IAChF,MAAMC,cAAc,GAAGJ,SAAS,CAACK,IAAI,CAACC,QAAQ,IAAI,CAACb,YAAY,CAACc,GAAG,CAACD,QAAQ,CAAC,CAAC;IAC9E,MAAME,WAAW,GAAG,EAAE;IACtBR,SAAS,CAACS,OAAO,CAACN,IAAI,IAAI;MACxB,IAAIC,cAAc,EAAE;QAClB,IAAI,CAACX,YAAY,CAACc,GAAG,CAACJ,IAAI,CAAC,EAAE;UAC3BK,WAAW,CAACE,IAAI,CAACP,IAAI,CAAC;QACxB;QACAV,YAAY,CAACkB,GAAG,CAACR,IAAI,CAAC;MACxB,CAAC,MAAM;QACLV,YAAY,CAACmB,MAAM,CAACT,IAAI,CAAC;QACzBK,WAAW,CAACE,IAAI,CAACP,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;IACFd,oBAAoB,CAACe,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC;IACtD,OAAOU,WAAW;EACpB,CAAC,EAAE,CAACpB,iBAAiB,CAAC,CAAC;EACvB,MAAMyB,uBAAuB,GAAGC,GAAG,IAAI;IACrCzB,oBAAoB,CAACyB,GAAG,CAAC;EAC3B,CAAC;EACD,OAAO,CAACxB,cAAc,EAAEuB,uBAAuB,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}