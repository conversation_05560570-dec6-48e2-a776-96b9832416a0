{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Text');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'usage', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "omit", "devUseW<PERSON>ning", "Base", "Text", "_a", "ref", "ellipsis", "restProps", "mergedEllipsis", "useMemo", "process", "env", "NODE_ENV", "warning", "createElement", "assign", "component", "forwardRef"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/typography/Text.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Text');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'usage', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,MAAMC,IAAI,GAAGA,CAACC,EAAE,EAAEC,GAAG,KAAK;EACxB,IAAI;MACAC;IACF,CAAC,GAAGF,EAAE;IACNG,SAAS,GAAGtB,MAAM,CAACmB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACtC,MAAMI,cAAc,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM;IACzC,IAAIH,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5C,OAAON,IAAI,CAACM,QAAQ,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC/C;IACA,OAAOA,QAAQ;EACjB,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGZ,aAAa,CAAC,iBAAiB,CAAC;IAChDS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,OAAOP,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,IAAI,EAAE,YAAY,IAAIA,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAIA,QAAQ,CAAC,EAAE,OAAO,EAAE,yDAAyD,CAAC,GAAG,KAAK,CAAC;EACjO;EACA,OAAO,aAAaP,KAAK,CAACe,aAAa,CAACZ,IAAI,EAAEZ,MAAM,CAACyB,MAAM,CAAC;IAC1DV,GAAG,EAAEA;EACP,CAAC,EAAEE,SAAS,EAAE;IACZD,QAAQ,EAAEE,cAAc;IACxBQ,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAajB,KAAK,CAACkB,UAAU,CAACd,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}