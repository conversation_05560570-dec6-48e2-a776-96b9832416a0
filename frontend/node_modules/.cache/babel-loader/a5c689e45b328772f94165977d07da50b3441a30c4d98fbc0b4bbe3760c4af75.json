{"ast": null, "code": "import { useContext } from 'react';\nimport DisabledContext from '../DisabledContext';\nimport SizeContext from '../SizeContext';\nfunction useConfig() {\n  const componentDisabled = useContext(DisabledContext);\n  const componentSize = useContext(SizeContext);\n  return {\n    componentDisabled,\n    componentSize\n  };\n}\nexport default useConfig;", "map": {"version": 3, "names": ["useContext", "DisabledContext", "SizeContext", "useConfig", "componentDisabled", "componentSize"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/config-provider/hooks/useConfig.js"], "sourcesContent": ["import { useContext } from 'react';\nimport DisabledContext from '../DisabledContext';\nimport SizeContext from '../SizeContext';\nfunction useConfig() {\n  const componentDisabled = useContext(DisabledContext);\n  const componentSize = useContext(SizeContext);\n  return {\n    componentDisabled,\n    componentSize\n  };\n}\nexport default useConfig;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,SAASA,CAAA,EAAG;EACnB,MAAMC,iBAAiB,GAAGJ,UAAU,CAACC,eAAe,CAAC;EACrD,MAAMI,aAAa,GAAGL,UAAU,CAACE,WAAW,CAAC;EAC7C,OAAO;IACLE,iBAAiB;IACjBC;EACF,CAAC;AACH;AACA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}