{"ast": null, "code": "import React from 'react';\nconst DescriptionsContext = /*#__PURE__*/React.createContext({});\nexport default DescriptionsContext;", "map": {"version": 3, "names": ["React", "DescriptionsContext", "createContext"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/descriptions/DescriptionsContext.js"], "sourcesContent": ["import React from 'react';\nconst DescriptionsContext = /*#__PURE__*/React.createContext({});\nexport default DescriptionsContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,mBAAmB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAChE,eAAeD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}