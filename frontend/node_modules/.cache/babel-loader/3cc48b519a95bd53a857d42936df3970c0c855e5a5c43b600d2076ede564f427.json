{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Title');\n    process.env.NODE_ENV !== \"production\" ? warning(TITLE_ELE_LIST.includes(level), 'usage', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n  }\n  const component = TITLE_ELE_LIST.includes(level) ? `h${level}` : `h1`;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "devUseW<PERSON>ning", "Base", "TITLE_ELE_LIST", "Title", "forwardRef", "props", "ref", "level", "restProps", "process", "env", "NODE_ENV", "warning", "includes", "component", "createElement", "assign"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/typography/Title.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Title');\n    process.env.NODE_ENV !== \"production\" ? warning(TITLE_ELE_LIST.includes(level), 'usage', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n  }\n  const component = TITLE_ELE_LIST.includes(level) ? `h${level}` : `h1`;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,MAAMC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC1D,MAAM;MACFC,KAAK,GAAG;IACV,CAAC,GAAGF,KAAK;IACTG,SAAS,GAAGvB,MAAM,CAACoB,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;EACtC,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGZ,aAAa,CAAC,kBAAkB,CAAC;IACjDS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAACV,cAAc,CAACW,QAAQ,CAACN,KAAK,CAAC,EAAE,OAAO,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;EAC3L;EACA,MAAMO,SAAS,GAAGZ,cAAc,CAACW,QAAQ,CAACN,KAAK,CAAC,GAAG,IAAIA,KAAK,EAAE,GAAG,IAAI;EACrE,OAAO,aAAaR,KAAK,CAACgB,aAAa,CAACd,IAAI,EAAEX,MAAM,CAAC0B,MAAM,CAAC;IAC1DV,GAAG,EAAEA;EACP,CAAC,EAAEE,SAAS,EAAE;IACZM,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAeX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}