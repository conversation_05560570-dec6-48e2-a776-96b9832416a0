{"ast": null, "code": "import { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedEmptyStyle = token => {\n  const {\n    componentCls,\n    margin,\n    marginXS,\n    marginXL,\n    fontSize,\n    lineHeight\n  } = token;\n  return {\n    [componentCls]: {\n      marginInline: marginXS,\n      fontSize,\n      lineHeight,\n      textAlign: 'center',\n      // 原来 &-image 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构\n      [`${componentCls}-image`]: {\n        height: token.emptyImgHeight,\n        marginBottom: marginXS,\n        opacity: token.opacityImage,\n        img: {\n          height: '100%'\n        },\n        svg: {\n          maxWidth: '100%',\n          height: '100%',\n          margin: 'auto'\n        }\n      },\n      [`${componentCls}-description`]: {\n        color: token.colorTextDescription\n      },\n      // 原来 &-footer 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构\n      [`${componentCls}-footer`]: {\n        marginTop: margin\n      },\n      '&-normal': {\n        marginBlock: marginXL,\n        color: token.colorTextDescription,\n        [`${componentCls}-description`]: {\n          color: token.colorTextDescription\n        },\n        [`${componentCls}-image`]: {\n          height: token.emptyImgHeightMD\n        }\n      },\n      '&-small': {\n        marginBlock: marginXS,\n        color: token.colorTextDescription,\n        [`${componentCls}-image`]: {\n          height: token.emptyImgHeightSM\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Empty', token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    calc\n  } = token;\n  const emptyToken = mergeToken(token, {\n    emptyImgCls: `${componentCls}-img`,\n    emptyImgHeight: calc(controlHeightLG).mul(2.5).equal(),\n    emptyImgHeightMD: controlHeightLG,\n    emptyImgHeightSM: calc(controlHeightLG).mul(0.875).equal()\n  });\n  return [genSharedEmptyStyle(emptyToken)];\n});", "map": {"version": 3, "names": ["genStyleHooks", "mergeToken", "genSharedEmptyStyle", "token", "componentCls", "margin", "marginXS", "marginXL", "fontSize", "lineHeight", "marginInline", "textAlign", "height", "emptyImgHeight", "marginBottom", "opacity", "opacityImage", "img", "svg", "max<PERSON><PERSON><PERSON>", "color", "colorTextDescription", "marginTop", "marginBlock", "emptyImgHeightMD", "emptyImgHeightSM", "controlHeightLG", "calc", "emptyToken", "emptyImgCls", "mul", "equal"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/empty/style/index.js"], "sourcesContent": ["import { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedEmptyStyle = token => {\n  const {\n    componentCls,\n    margin,\n    marginXS,\n    marginXL,\n    fontSize,\n    lineHeight\n  } = token;\n  return {\n    [componentCls]: {\n      marginInline: marginXS,\n      fontSize,\n      lineHeight,\n      textAlign: 'center',\n      // 原来 &-image 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构\n      [`${componentCls}-image`]: {\n        height: token.emptyImgHeight,\n        marginBottom: marginXS,\n        opacity: token.opacityImage,\n        img: {\n          height: '100%'\n        },\n        svg: {\n          maxWidth: '100%',\n          height: '100%',\n          margin: 'auto'\n        }\n      },\n      [`${componentCls}-description`]: {\n        color: token.colorTextDescription\n      },\n      // 原来 &-footer 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构\n      [`${componentCls}-footer`]: {\n        marginTop: margin\n      },\n      '&-normal': {\n        marginBlock: marginXL,\n        color: token.colorTextDescription,\n        [`${componentCls}-description`]: {\n          color: token.colorTextDescription\n        },\n        [`${componentCls}-image`]: {\n          height: token.emptyImgHeightMD\n        }\n      },\n      '&-small': {\n        marginBlock: marginXS,\n        color: token.colorTextDescription,\n        [`${componentCls}-image`]: {\n          height: token.emptyImgHeightSM\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Empty', token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    calc\n  } = token;\n  const emptyToken = mergeToken(token, {\n    emptyImgCls: `${componentCls}-img`,\n    emptyImgHeight: calc(controlHeightLG).mul(2.5).equal(),\n    emptyImgHeightMD: controlHeightLG,\n    emptyImgHeightSM: calc(controlHeightLG).mul(0.875).equal()\n  });\n  return [genSharedEmptyStyle(emptyToken)];\n});"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,mBAAmB,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGN,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdM,YAAY,EAAEJ,QAAQ;MACtBE,QAAQ;MACRC,UAAU;MACVE,SAAS,EAAE,QAAQ;MACnB;MACA,CAAC,GAAGP,YAAY,QAAQ,GAAG;QACzBQ,MAAM,EAAET,KAAK,CAACU,cAAc;QAC5BC,YAAY,EAAER,QAAQ;QACtBS,OAAO,EAAEZ,KAAK,CAACa,YAAY;QAC3BC,GAAG,EAAE;UACHL,MAAM,EAAE;QACV,CAAC;QACDM,GAAG,EAAE;UACHC,QAAQ,EAAE,MAAM;UAChBP,MAAM,EAAE,MAAM;UACdP,MAAM,EAAE;QACV;MACF,CAAC;MACD,CAAC,GAAGD,YAAY,cAAc,GAAG;QAC/BgB,KAAK,EAAEjB,KAAK,CAACkB;MACf,CAAC;MACD;MACA,CAAC,GAAGjB,YAAY,SAAS,GAAG;QAC1BkB,SAAS,EAAEjB;MACb,CAAC;MACD,UAAU,EAAE;QACVkB,WAAW,EAAEhB,QAAQ;QACrBa,KAAK,EAAEjB,KAAK,CAACkB,oBAAoB;QACjC,CAAC,GAAGjB,YAAY,cAAc,GAAG;UAC/BgB,KAAK,EAAEjB,KAAK,CAACkB;QACf,CAAC;QACD,CAAC,GAAGjB,YAAY,QAAQ,GAAG;UACzBQ,MAAM,EAAET,KAAK,CAACqB;QAChB;MACF,CAAC;MACD,SAAS,EAAE;QACTD,WAAW,EAAEjB,QAAQ;QACrBc,KAAK,EAAEjB,KAAK,CAACkB,oBAAoB;QACjC,CAAC,GAAGjB,YAAY,QAAQ,GAAG;UACzBQ,MAAM,EAAET,KAAK,CAACsB;QAChB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAezB,aAAa,CAAC,OAAO,EAAEG,KAAK,IAAI;EAC7C,MAAM;IACJC,YAAY;IACZsB,eAAe;IACfC;EACF,CAAC,GAAGxB,KAAK;EACT,MAAMyB,UAAU,GAAG3B,UAAU,CAACE,KAAK,EAAE;IACnC0B,WAAW,EAAE,GAAGzB,YAAY,MAAM;IAClCS,cAAc,EAAEc,IAAI,CAACD,eAAe,CAAC,CAACI,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IACtDP,gBAAgB,EAAEE,eAAe;IACjCD,gBAAgB,EAAEE,IAAI,CAACD,eAAe,CAAC,CAACI,GAAG,CAAC,KAAK,CAAC,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO,CAAC7B,mBAAmB,CAAC0B,UAAU,CAAC,CAAC;AAC1C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}