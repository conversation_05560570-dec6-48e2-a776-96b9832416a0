{"ast": null, "code": "\"use client\";\n\nimport Group from './group';\nimport InternalRadio from './radio';\nimport Button from './radioButton';\nexport { Button, Group };\nconst Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nRadio.__ANT_RADIO = true;\nexport default Radio;", "map": {"version": 3, "names": ["Group", "InternalRadio", "<PERSON><PERSON>", "Radio", "__ANT_RADIO"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/radio/index.js"], "sourcesContent": ["\"use client\";\n\nimport Group from './group';\nimport InternalRadio from './radio';\nimport Button from './radioButton';\nexport { Button, Group };\nconst Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nRadio.__ANT_RADIO = true;\nexport default Radio;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASA,MAAM,EAAEF,KAAK;AACtB,MAAMG,KAAK,GAAGF,aAAa;AAC3BE,KAAK,CAACD,MAAM,GAAGA,MAAM;AACrBC,KAAK,CAACH,KAAK,GAAGA,KAAK;AACnBG,KAAK,CAACC,WAAW,GAAG,IAAI;AACxB,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}