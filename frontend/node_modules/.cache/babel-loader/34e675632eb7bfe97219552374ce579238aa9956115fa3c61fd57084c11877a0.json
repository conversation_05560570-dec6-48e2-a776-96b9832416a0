{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Popover from '../popover';\nimport Avatar from './Avatar';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst AvatarContextProvider = props => {\n  const {\n    size,\n    shape\n  } = React.useContext(AvatarContext);\n  const avatarContextValue = React.useMemo(() => ({\n    size: props.size || size,\n    shape: props.shape || shape\n  }), [props.size, props.shape, size, shape]);\n  return /*#__PURE__*/React.createElement(AvatarContext.Provider, {\n    value: avatarContextValue\n  }, props.children);\n};\nconst AvatarGroup = props => {\n  var _a, _b, _c, _d;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    maxCount,\n    maxStyle,\n    size,\n    shape,\n    maxPopoverPlacement,\n    maxPopoverTrigger,\n    children,\n    max\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar.Group');\n    [['maxCount', 'max={{ count: number }}'], ['maxStyle', 'max={{ style: CSSProperties }}'], ['maxPopoverPlacement', 'max={{ popover: PopoverProps }}'], ['maxPopoverTrigger', 'max={{ popover: PopoverProps }}']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const cls = classNames(groupPrefixCls, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl'\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const childrenWithProps = toArray(children).map((child, index) => cloneElement(child, {\n    // eslint-disable-next-line react/no-array-index-key\n    key: `avatar-key-${index}`\n  }));\n  const mergeCount = (max === null || max === void 0 ? void 0 : max.count) || maxCount;\n  const numOfChildren = childrenWithProps.length;\n  if (mergeCount && mergeCount < numOfChildren) {\n    const childrenShow = childrenWithProps.slice(0, mergeCount);\n    const childrenHidden = childrenWithProps.slice(mergeCount, numOfChildren);\n    const mergeStyle = (max === null || max === void 0 ? void 0 : max.style) || maxStyle;\n    const mergePopoverTrigger = ((_a = max === null || max === void 0 ? void 0 : max.popover) === null || _a === void 0 ? void 0 : _a.trigger) || maxPopoverTrigger || 'hover';\n    const mergePopoverPlacement = ((_b = max === null || max === void 0 ? void 0 : max.popover) === null || _b === void 0 ? void 0 : _b.placement) || maxPopoverPlacement || 'top';\n    const mergeProps = Object.assign(Object.assign({\n      content: childrenHidden\n    }, max === null || max === void 0 ? void 0 : max.popover), {\n      classNames: {\n        root: classNames(`${groupPrefixCls}-popover`, (_d = (_c = max === null || max === void 0 ? void 0 : max.popover) === null || _c === void 0 ? void 0 : _c.classNames) === null || _d === void 0 ? void 0 : _d.root)\n      },\n      placement: mergePopoverPlacement,\n      trigger: mergePopoverTrigger\n    });\n    childrenShow.push(/*#__PURE__*/React.createElement(Popover, Object.assign({\n      key: \"avatar-popover-key\",\n      destroyOnHidden: true\n    }, mergeProps), /*#__PURE__*/React.createElement(Avatar, {\n      style: mergeStyle\n    }, `+${numOfChildren - mergeCount}`)));\n    return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n      shape: shape,\n      size: size\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, childrenShow)));\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n    shape: shape,\n    size: size\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, childrenWithProps)));\n};\nexport default AvatarGroup;", "map": {"version": 3, "names": ["React", "classNames", "toArray", "cloneElement", "devUseW<PERSON>ning", "ConfigContext", "useCSSVarCls", "Popover", "Avatar", "AvatarContext", "useStyle", "AvatarContextProvider", "props", "size", "shape", "useContext", "avatarContextValue", "useMemo", "createElement", "Provider", "value", "children", "AvatarGroup", "_a", "_b", "_c", "_d", "getPrefixCls", "direction", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "maxCount", "maxStyle", "maxPopoverPlacement", "maxPopoverTrigger", "max", "process", "env", "NODE_ENV", "warning", "for<PERSON>ach", "deprecatedName", "newName", "deprecated", "groupPrefixCls", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "cls", "childrenWithProps", "map", "child", "index", "key", "mergeCount", "count", "numOfChildren", "length", "childrenShow", "slice", "childrenH<PERSON>den", "mergeStyle", "mergePopoverTrigger", "popover", "trigger", "mergePopoverPlacement", "placement", "mergeProps", "Object", "assign", "content", "root", "push", "destroyOnHidden"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/avatar/AvatarGroup.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Popover from '../popover';\nimport Avatar from './Avatar';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst AvatarContextProvider = props => {\n  const {\n    size,\n    shape\n  } = React.useContext(AvatarContext);\n  const avatarContextValue = React.useMemo(() => ({\n    size: props.size || size,\n    shape: props.shape || shape\n  }), [props.size, props.shape, size, shape]);\n  return /*#__PURE__*/React.createElement(AvatarContext.Provider, {\n    value: avatarContextValue\n  }, props.children);\n};\nconst AvatarGroup = props => {\n  var _a, _b, _c, _d;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    maxCount,\n    maxStyle,\n    size,\n    shape,\n    maxPopoverPlacement,\n    maxPopoverTrigger,\n    children,\n    max\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar.Group');\n    [['maxCount', 'max={{ count: number }}'], ['maxStyle', 'max={{ style: CSSProperties }}'], ['maxPopoverPlacement', 'max={{ popover: PopoverProps }}'], ['maxPopoverTrigger', 'max={{ popover: PopoverProps }}']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const cls = classNames(groupPrefixCls, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl'\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const childrenWithProps = toArray(children).map((child, index) => cloneElement(child, {\n    // eslint-disable-next-line react/no-array-index-key\n    key: `avatar-key-${index}`\n  }));\n  const mergeCount = (max === null || max === void 0 ? void 0 : max.count) || maxCount;\n  const numOfChildren = childrenWithProps.length;\n  if (mergeCount && mergeCount < numOfChildren) {\n    const childrenShow = childrenWithProps.slice(0, mergeCount);\n    const childrenHidden = childrenWithProps.slice(mergeCount, numOfChildren);\n    const mergeStyle = (max === null || max === void 0 ? void 0 : max.style) || maxStyle;\n    const mergePopoverTrigger = ((_a = max === null || max === void 0 ? void 0 : max.popover) === null || _a === void 0 ? void 0 : _a.trigger) || maxPopoverTrigger || 'hover';\n    const mergePopoverPlacement = ((_b = max === null || max === void 0 ? void 0 : max.popover) === null || _b === void 0 ? void 0 : _b.placement) || maxPopoverPlacement || 'top';\n    const mergeProps = Object.assign(Object.assign({\n      content: childrenHidden\n    }, max === null || max === void 0 ? void 0 : max.popover), {\n      classNames: {\n        root: classNames(`${groupPrefixCls}-popover`, (_d = (_c = max === null || max === void 0 ? void 0 : max.popover) === null || _c === void 0 ? void 0 : _c.classNames) === null || _d === void 0 ? void 0 : _d.root)\n      },\n      placement: mergePopoverPlacement,\n      trigger: mergePopoverTrigger\n    });\n    childrenShow.push(/*#__PURE__*/React.createElement(Popover, Object.assign({\n      key: \"avatar-popover-key\",\n      destroyOnHidden: true\n    }, mergeProps), /*#__PURE__*/React.createElement(Avatar, {\n      style: mergeStyle\n    }, `+${numOfChildren - mergeCount}`)));\n    return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n      shape: shape,\n      size: size\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, childrenShow)));\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n    shape: shape,\n    size: size\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, childrenWithProps)));\n};\nexport default AvatarGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,qBAAqB,GAAGC,KAAK,IAAI;EACrC,MAAM;IACJC,IAAI;IACJC;EACF,CAAC,GAAGd,KAAK,CAACe,UAAU,CAACN,aAAa,CAAC;EACnC,MAAMO,kBAAkB,GAAGhB,KAAK,CAACiB,OAAO,CAAC,OAAO;IAC9CJ,IAAI,EAAED,KAAK,CAACC,IAAI,IAAIA,IAAI;IACxBC,KAAK,EAAEF,KAAK,CAACE,KAAK,IAAIA;EACxB,CAAC,CAAC,EAAE,CAACF,KAAK,CAACC,IAAI,EAAED,KAAK,CAACE,KAAK,EAAED,IAAI,EAAEC,KAAK,CAAC,CAAC;EAC3C,OAAO,aAAad,KAAK,CAACkB,aAAa,CAACT,aAAa,CAACU,QAAQ,EAAE;IAC9DC,KAAK,EAAEJ;EACT,CAAC,EAAEJ,KAAK,CAACS,QAAQ,CAAC;AACpB,CAAC;AACD,MAAMC,WAAW,GAAGV,KAAK,IAAI;EAC3B,IAAIW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAG5B,KAAK,CAACe,UAAU,CAACV,aAAa,CAAC;EACnC,MAAM;IACJwB,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRtB,IAAI;IACJC,KAAK;IACLsB,mBAAmB;IACnBC,iBAAiB;IACjBhB,QAAQ;IACRiB;EACF,CAAC,GAAG1B,KAAK;EACT,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGtC,aAAa,CAAC,cAAc,CAAC;IAC7C,CAAC,CAAC,UAAU,EAAE,yBAAyB,CAAC,EAAE,CAAC,UAAU,EAAE,gCAAgC,CAAC,EAAE,CAAC,qBAAqB,EAAE,iCAAiC,CAAC,EAAE,CAAC,mBAAmB,EAAE,iCAAiC,CAAC,CAAC,CAACuC,OAAO,CAAC,CAAC,CAACC,cAAc,EAAEC,OAAO,CAAC,KAAK;MACrPH,OAAO,CAACI,UAAU,CAAC,EAAEF,cAAc,IAAIhC,KAAK,CAAC,EAAEgC,cAAc,EAAEC,OAAO,CAAC;IACzE,CAAC,CAAC;EACJ;EACA,MAAMhB,SAAS,GAAGF,YAAY,CAAC,QAAQ,EAAEG,kBAAkB,CAAC;EAC5D,MAAMiB,cAAc,GAAG,GAAGlB,SAAS,QAAQ;EAC3C,MAAMmB,OAAO,GAAG1C,YAAY,CAACuB,SAAS,CAAC;EACvC,MAAM,CAACoB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAACmB,SAAS,EAAEmB,OAAO,CAAC;EACpE,MAAMI,GAAG,GAAGnD,UAAU,CAAC8C,cAAc,EAAE;IACrC,CAAC,GAAGA,cAAc,MAAM,GAAGnB,SAAS,KAAK;EAC3C,CAAC,EAAEuB,SAAS,EAAEH,OAAO,EAAEjB,SAAS,EAAEC,aAAa,EAAEkB,MAAM,CAAC;EACxD,MAAMG,iBAAiB,GAAGnD,OAAO,CAACmB,QAAQ,CAAC,CAACiC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKrD,YAAY,CAACoD,KAAK,EAAE;IACpF;IACAE,GAAG,EAAE,cAAcD,KAAK;EAC1B,CAAC,CAAC,CAAC;EACH,MAAME,UAAU,GAAG,CAACpB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,KAAK,KAAKzB,QAAQ;EACpF,MAAM0B,aAAa,GAAGP,iBAAiB,CAACQ,MAAM;EAC9C,IAAIH,UAAU,IAAIA,UAAU,GAAGE,aAAa,EAAE;IAC5C,MAAME,YAAY,GAAGT,iBAAiB,CAACU,KAAK,CAAC,CAAC,EAAEL,UAAU,CAAC;IAC3D,MAAMM,cAAc,GAAGX,iBAAiB,CAACU,KAAK,CAACL,UAAU,EAAEE,aAAa,CAAC;IACzE,MAAMK,UAAU,GAAG,CAAC3B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACL,KAAK,KAAKE,QAAQ;IACpF,MAAM+B,mBAAmB,GAAG,CAAC,CAAC3C,EAAE,GAAGe,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC6B,OAAO,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C,OAAO,KAAK/B,iBAAiB,IAAI,OAAO;IAC1K,MAAMgC,qBAAqB,GAAG,CAAC,CAAC7C,EAAE,GAAGc,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC6B,OAAO,MAAM,IAAI,IAAI3C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8C,SAAS,KAAKlC,mBAAmB,IAAI,KAAK;IAC9K,MAAMmC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAC7CC,OAAO,EAAEV;IACX,CAAC,EAAE1B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC6B,OAAO,CAAC,EAAE;MACzDlE,UAAU,EAAE;QACV0E,IAAI,EAAE1E,UAAU,CAAC,GAAG8C,cAAc,UAAU,EAAE,CAACrB,EAAE,GAAG,CAACD,EAAE,GAAGa,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC6B,OAAO,MAAM,IAAI,IAAI1C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACxB,UAAU,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiD,IAAI;MACnN,CAAC;MACDL,SAAS,EAAED,qBAAqB;MAChCD,OAAO,EAAEF;IACX,CAAC,CAAC;IACFJ,YAAY,CAACc,IAAI,CAAC,aAAa5E,KAAK,CAACkB,aAAa,CAACX,OAAO,EAAEiE,MAAM,CAACC,MAAM,CAAC;MACxEhB,GAAG,EAAE,oBAAoB;MACzBoB,eAAe,EAAE;IACnB,CAAC,EAAEN,UAAU,CAAC,EAAE,aAAavE,KAAK,CAACkB,aAAa,CAACV,MAAM,EAAE;MACvDyB,KAAK,EAAEgC;IACT,CAAC,EAAE,IAAIL,aAAa,GAAGF,UAAU,EAAE,CAAC,CAAC,CAAC;IACtC,OAAOT,UAAU,CAAC,aAAajD,KAAK,CAACkB,aAAa,CAACP,qBAAqB,EAAE;MACxEG,KAAK,EAAEA,KAAK;MACZD,IAAI,EAAEA;IACR,CAAC,EAAE,aAAab,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;MACzCa,SAAS,EAAEqB,GAAG;MACdnB,KAAK,EAAEA;IACT,CAAC,EAAE6B,YAAY,CAAC,CAAC,CAAC;EACpB;EACA,OAAOb,UAAU,CAAC,aAAajD,KAAK,CAACkB,aAAa,CAACP,qBAAqB,EAAE;IACxEG,KAAK,EAAEA,KAAK;IACZD,IAAI,EAAEA;EACR,CAAC,EAAE,aAAab,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IACzCa,SAAS,EAAEqB,GAAG;IACdnB,KAAK,EAAEA;EACT,CAAC,EAAEoB,iBAAiB,CAAC,CAAC,CAAC;AACzB,CAAC;AACD,eAAe/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}