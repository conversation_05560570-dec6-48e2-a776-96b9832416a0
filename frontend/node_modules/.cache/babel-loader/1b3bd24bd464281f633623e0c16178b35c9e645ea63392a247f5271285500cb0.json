{"ast": null, "code": "\"use client\";\n\nimport <PERSON><PERSON> from './Dragger';\nimport InternalUpload, { LIST_IGNORE } from './Upload';\nconst Upload = InternalUpload;\nUpload.Dragger = Dragger;\nUpload.LIST_IGNORE = LIST_IGNORE;\nexport default Upload;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "InternalUpload", "LIST_IGNORE", "Upload"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/upload/index.js"], "sourcesContent": ["\"use client\";\n\nimport <PERSON><PERSON> from './Dragger';\nimport InternalUpload, { LIST_IGNORE } from './Upload';\nconst Upload = InternalUpload;\nUpload.Dragger = Dragger;\nUpload.LIST_IGNORE = LIST_IGNORE;\nexport default Upload;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,IAAIC,WAAW,QAAQ,UAAU;AACtD,MAAMC,MAAM,GAAGF,cAAc;AAC7BE,MAAM,CAACH,OAAO,GAAGA,OAAO;AACxBG,MAAM,CAACD,WAAW,GAAGA,WAAW;AAChC,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}