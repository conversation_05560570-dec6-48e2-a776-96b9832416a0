{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\n// CSSINJS\nimport useStyle from './style';\nimport TimelineItem from './TimelineItem';\nimport TimelineItemList from './TimelineItemList';\nimport useItems from './useItems';\nconst Timeline = props => {\n  const {\n    getPrefixCls,\n    direction,\n    timeline\n  } = React.useContext(ConfigContext);\n  const {\n      prefixCls: customizePrefixCls,\n      children,\n      items,\n      className,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"children\", \"items\", \"className\", \"style\"]);\n  const prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Timeline');\n    warning.deprecated(!children, 'Timeline.Item', 'items');\n  }\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedItems = useItems(items, children);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(TimelineItemList, Object.assign({}, restProps, {\n    className: classNames(timeline === null || timeline === void 0 ? void 0 : timeline.className, className, cssVarCls, rootCls),\n    style: Object.assign(Object.assign({}, timeline === null || timeline === void 0 ? void 0 : timeline.style), style),\n    prefixCls: prefixCls,\n    direction: direction,\n    items: mergedItems,\n    hashId: hashId\n  })));\n};\nTimeline.Item = TimelineItem;\nif (process.env.NODE_ENV !== 'production') {\n  Timeline.displayName = 'Timeline';\n}\nexport default Timeline;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "devUseW<PERSON>ning", "ConfigContext", "useCSSVarCls", "useStyle", "TimelineItem", "TimelineItemList", "useItems", "Timeline", "props", "getPrefixCls", "direction", "timeline", "useContext", "prefixCls", "customizePrefixCls", "children", "items", "className", "style", "restProps", "process", "env", "NODE_ENV", "warning", "deprecated", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "mergedItems", "createElement", "assign", "<PERSON><PERSON>", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/timeline/Timeline.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\n// CSSINJS\nimport useStyle from './style';\nimport TimelineItem from './TimelineItem';\nimport TimelineItemList from './TimelineItemList';\nimport useItems from './useItems';\nconst Timeline = props => {\n  const {\n    getPrefixCls,\n    direction,\n    timeline\n  } = React.useContext(ConfigContext);\n  const {\n      prefixCls: customizePrefixCls,\n      children,\n      items,\n      className,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"children\", \"items\", \"className\", \"style\"]);\n  const prefixCls = getPrefixCls('timeline', customizePrefixCls);\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Timeline');\n    warning.deprecated(!children, 'Timeline.Item', 'items');\n  }\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedItems = useItems(items, children);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(TimelineItemList, Object.assign({}, restProps, {\n    className: classNames(timeline === null || timeline === void 0 ? void 0 : timeline.className, className, cssVarCls, rootCls),\n    style: Object.assign(Object.assign({}, timeline === null || timeline === void 0 ? void 0 : timeline.style), style),\n    prefixCls: prefixCls,\n    direction: direction,\n    items: mergedItems,\n    hashId: hashId\n  })));\n};\nTimeline.Item = TimelineItem;\nif (process.env.NODE_ENV !== 'production') {\n  Timeline.displayName = 'Timeline';\n}\nexport default Timeline;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE;AACA,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,YAAY;AACjC,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGb,KAAK,CAACc,UAAU,CAACX,aAAa,CAAC;EACnC,MAAM;MACFY,SAAS,EAAEC,kBAAkB;MAC7BC,QAAQ;MACRC,KAAK;MACLC,SAAS;MACTC;IACF,CAAC,GAAGV,KAAK;IACTW,SAAS,GAAGnC,MAAM,CAACwB,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;EACrF,MAAMK,SAAS,GAAGJ,YAAY,CAAC,UAAU,EAAEK,kBAAkB,CAAC;EAC9D;EACA,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGvB,aAAa,CAAC,UAAU,CAAC;IACzCuB,OAAO,CAACC,UAAU,CAAC,CAACT,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC;EACzD;EACA;EACA,MAAMU,OAAO,GAAGvB,YAAY,CAACW,SAAS,CAAC;EACvC,MAAM,CAACa,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAACU,SAAS,EAAEY,OAAO,CAAC;EACpE,MAAMI,WAAW,GAAGvB,QAAQ,CAACU,KAAK,EAAED,QAAQ,CAAC;EAC7C,OAAOW,UAAU,CAAC,aAAa5B,KAAK,CAACgC,aAAa,CAACzB,gBAAgB,EAAEhB,MAAM,CAAC0C,MAAM,CAAC,CAAC,CAAC,EAAEZ,SAAS,EAAE;IAChGF,SAAS,EAAElB,UAAU,CAACY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,SAAS,EAAEA,SAAS,EAAEW,SAAS,EAAEH,OAAO,CAAC;IAC5HP,KAAK,EAAE7B,MAAM,CAAC0C,MAAM,CAAC1C,MAAM,CAAC0C,MAAM,CAAC,CAAC,CAAC,EAAEpB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACO,KAAK,CAAC,EAAEA,KAAK,CAAC;IAClHL,SAAS,EAAEA,SAAS;IACpBH,SAAS,EAAEA,SAAS;IACpBM,KAAK,EAAEa,WAAW;IAClBF,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACDpB,QAAQ,CAACyB,IAAI,GAAG5B,YAAY;AAC5B,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCf,QAAQ,CAAC0B,WAAW,GAAG,UAAU;AACnC;AACA,eAAe1B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}