{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext, useEffect, useState } from 'react';\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { composeRef } from \"rc-util/es/ref\";\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport FloatButtonGroupContext from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nconst BackTop = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      type = 'default',\n      shape = 'circle',\n      visibilityHeight = 400,\n      icon = /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null),\n      target,\n      onClick,\n      duration = 450\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"type\", \"shape\", \"visibilityHeight\", \"icon\", \"target\", \"onClick\", \"duration\"]);\n  const [visible, setVisible] = useState(visibilityHeight === 0);\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const getDefaultTarget = () => {\n    var _a;\n    return ((_a = internalRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || window;\n  };\n  const handleScroll = throttleByAnimationFrame(e => {\n    const scrollTop = getScroll(e.target);\n    setVisible(scrollTop >= visibilityHeight);\n  });\n  useEffect(() => {\n    const getTarget = target || getDefaultTarget;\n    const container = getTarget();\n    handleScroll({\n      target: container\n    });\n    container === null || container === void 0 ? void 0 : container.addEventListener('scroll', handleScroll);\n    return () => {\n      handleScroll.cancel();\n      container === null || container === void 0 ? void 0 : container.removeEventListener('scroll', handleScroll);\n    };\n  }, [target]);\n  const scrollToTop = e => {\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration\n    });\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const groupShape = useContext(FloatButtonGroupContext);\n  const mergedShape = groupShape || shape;\n  const contentProps = Object.assign({\n    prefixCls,\n    icon,\n    type,\n    shape: mergedShape\n  }, restProps);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: `${rootPrefixCls}-fade`\n  }, ({\n    className: motionClassName\n  }, setRef) => (/*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    ref: composeRef(internalRef, setRef)\n  }, contentProps, {\n    onClick: scrollToTop,\n    className: classNames(className, motionClassName)\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  BackTop.displayName = 'BackTop';\n}\nexport default BackTop;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useContext", "useEffect", "useState", "VerticalAlignTopOutlined", "classNames", "CSSMotion", "composeRef", "getScroll", "scrollTo", "throttleByAnimationFrame", "ConfigContext", "FloatButtonGroupContext", "FloatButton", "floatButtonPrefixCls", "BackTop", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "className", "type", "shape", "visibilityHeight", "icon", "createElement", "target", "onClick", "duration", "restProps", "visible", "setVisible", "internalRef", "useRef", "useImperativeHandle", "nativeElement", "current", "getDefaultTarget", "_a", "ownerDocument", "window", "handleScroll", "scrollTop", "get<PERSON><PERSON><PERSON>", "container", "addEventListener", "cancel", "removeEventListener", "scrollToTop", "getContainer", "getPrefixCls", "rootPrefixCls", "groupShape", "mergedShape", "contentProps", "assign", "motionName", "motionClassName", "setRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/float-button/BackTop.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext, useEffect, useState } from 'react';\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { composeRef } from \"rc-util/es/ref\";\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport FloatButtonGroupContext from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nconst BackTop = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      type = 'default',\n      shape = 'circle',\n      visibilityHeight = 400,\n      icon = /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null),\n      target,\n      onClick,\n      duration = 450\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"type\", \"shape\", \"visibilityHeight\", \"icon\", \"target\", \"onClick\", \"duration\"]);\n  const [visible, setVisible] = useState(visibilityHeight === 0);\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const getDefaultTarget = () => {\n    var _a;\n    return ((_a = internalRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || window;\n  };\n  const handleScroll = throttleByAnimationFrame(e => {\n    const scrollTop = getScroll(e.target);\n    setVisible(scrollTop >= visibilityHeight);\n  });\n  useEffect(() => {\n    const getTarget = target || getDefaultTarget;\n    const container = getTarget();\n    handleScroll({\n      target: container\n    });\n    container === null || container === void 0 ? void 0 : container.addEventListener('scroll', handleScroll);\n    return () => {\n      handleScroll.cancel();\n      container === null || container === void 0 ? void 0 : container.removeEventListener('scroll', handleScroll);\n    };\n  }, [target]);\n  const scrollToTop = e => {\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration\n    });\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const groupShape = useContext(FloatButtonGroupContext);\n  const mergedShape = groupShape || shape;\n  const contentProps = Object.assign({\n    prefixCls,\n    icon,\n    type,\n    shape: mergedShape\n  }, restProps);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: `${rootPrefixCls}-fade`\n  }, ({\n    className: motionClassName\n  }, setRef) => (/*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    ref: composeRef(internalRef, setRef)\n  }, contentProps, {\n    onClick: scrollToTop,\n    className: classNames(className, motionClassName)\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  BackTop.displayName = 'BackTop';\n}\nexport default BackTop;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,OAAOC,wBAAwB,MAAM,qDAAqD;AAC1F,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,wBAAwB,MAAM,mCAAmC;AACxE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,uBAAuB,MAAM,WAAW;AAC/C,OAAOC,WAAW,IAAIC,oBAAoB,QAAQ,eAAe;AACjE,MAAMC,OAAO,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC5D,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,IAAI,GAAG,SAAS;MAChBC,KAAK,GAAG,QAAQ;MAChBC,gBAAgB,GAAG,GAAG;MACtBC,IAAI,GAAG,aAAazB,KAAK,CAAC0B,aAAa,CAACtB,wBAAwB,EAAE,IAAI,CAAC;MACvEuB,MAAM;MACNC,OAAO;MACPC,QAAQ,GAAG;IACb,CAAC,GAAGZ,KAAK;IACTa,SAAS,GAAG5C,MAAM,CAAC+B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;EACrI,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAACqB,gBAAgB,KAAK,CAAC,CAAC;EAC9D,MAAMS,WAAW,GAAGjC,KAAK,CAACkC,MAAM,CAAC,IAAI,CAAC;EACtClC,KAAK,CAACmC,mBAAmB,CAACjB,GAAG,EAAE,OAAO;IACpCkB,aAAa,EAAEH,WAAW,CAACI;EAC7B,CAAC,CAAC,CAAC;EACH,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGN,WAAW,CAACI,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,aAAa,KAAKC,MAAM;EACrG,CAAC;EACD,MAAMC,YAAY,GAAGhC,wBAAwB,CAACtB,CAAC,IAAI;IACjD,MAAMuD,SAAS,GAAGnC,SAAS,CAACpB,CAAC,CAACuC,MAAM,CAAC;IACrCK,UAAU,CAACW,SAAS,IAAInB,gBAAgB,CAAC;EAC3C,CAAC,CAAC;EACFtB,SAAS,CAAC,MAAM;IACd,MAAM0C,SAAS,GAAGjB,MAAM,IAAIW,gBAAgB;IAC5C,MAAMO,SAAS,GAAGD,SAAS,CAAC,CAAC;IAC7BF,YAAY,CAAC;MACXf,MAAM,EAAEkB;IACV,CAAC,CAAC;IACFA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACxG,OAAO,MAAM;MACXA,YAAY,CAACK,MAAM,CAAC,CAAC;MACrBF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC7G,CAAC;EACH,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMsB,WAAW,GAAG7D,CAAC,IAAI;IACvBqB,QAAQ,CAAC,CAAC,EAAE;MACVyC,YAAY,EAAEvB,MAAM,IAAIW,gBAAgB;MACxCT;IACF,CAAC,CAAC;IACFD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACxC,CAAC,CAAC;EAC9D,CAAC;EACD,MAAM;IACJ+D;EACF,CAAC,GAAGlD,UAAU,CAACU,aAAa,CAAC;EAC7B,MAAMQ,SAAS,GAAGgC,YAAY,CAACrC,oBAAoB,EAAEM,kBAAkB,CAAC;EACxE,MAAMgC,aAAa,GAAGD,YAAY,CAAC,CAAC;EACpC,MAAME,UAAU,GAAGpD,UAAU,CAACW,uBAAuB,CAAC;EACtD,MAAM0C,WAAW,GAAGD,UAAU,IAAI9B,KAAK;EACvC,MAAMgC,YAAY,GAAGhE,MAAM,CAACiE,MAAM,CAAC;IACjCrC,SAAS;IACTM,IAAI;IACJH,IAAI;IACJC,KAAK,EAAE+B;EACT,CAAC,EAAExB,SAAS,CAAC;EACb,OAAO,aAAa9B,KAAK,CAAC0B,aAAa,CAACpB,SAAS,EAAE;IACjDyB,OAAO,EAAEA,OAAO;IAChB0B,UAAU,EAAE,GAAGL,aAAa;EAC9B,CAAC,EAAE,CAAC;IACF/B,SAAS,EAAEqC;EACb,CAAC,EAAEC,MAAM,MAAM,aAAa3D,KAAK,CAAC0B,aAAa,CAACb,WAAW,EAAEtB,MAAM,CAACiE,MAAM,CAAC;IACzEtC,GAAG,EAAEX,UAAU,CAAC0B,WAAW,EAAE0B,MAAM;EACrC,CAAC,EAAEJ,YAAY,EAAE;IACf3B,OAAO,EAAEqB,WAAW;IACpB5B,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEqC,eAAe;EAClD,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC/C,OAAO,CAACgD,WAAW,GAAG,SAAS;AACjC;AACA,eAAehD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}