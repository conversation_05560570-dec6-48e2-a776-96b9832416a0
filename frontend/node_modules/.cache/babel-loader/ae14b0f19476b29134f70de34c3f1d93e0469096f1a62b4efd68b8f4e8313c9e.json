{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { FastColor } from '@ant-design/fast-color';\nimport { useToken } from '../theme/internal';\nimport { useLocale } from '../locale';\nconst Simple = () => {\n  const [, token] = useToken();\n  const [locale] = useLocale('Empty');\n  const {\n    colorFill,\n    colorFillTertiary,\n    colorFillQuaternary,\n    colorBgContainer\n  } = token;\n  const {\n    borderColor,\n    shadowColor,\n    contentColor\n  } = useMemo(() => ({\n    borderColor: new FastColor(colorFill).onBackground(colorBgContainer).toHexString(),\n    shadowColor: new FastColor(colorFillTertiary).onBackground(colorBgContainer).toHexString(),\n    contentColor: new FastColor(colorFillQuaternary).onBackground(colorBgContainer).toHexString()\n  }), [colorFill, colorFillTertiary, colorFillQuaternary, colorBgContainer]);\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"title\", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    fill: shadowColor,\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    fillRule: \"nonzero\",\n    stroke: borderColor\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    fill: contentColor\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Simple.displayName = 'SimpleImage';\n}\nexport default Simple;", "map": {"version": 3, "names": ["React", "useMemo", "FastColor", "useToken", "useLocale", "Simple", "token", "locale", "colorFill", "colorFillTertiary", "colorFillQuaternary", "colorBgContainer", "borderColor", "shadowColor", "contentColor", "onBackground", "toHexString", "createElement", "width", "height", "viewBox", "xmlns", "description", "transform", "fill", "fillRule", "cx", "cy", "rx", "ry", "stroke", "d", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/empty/simple.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { FastColor } from '@ant-design/fast-color';\nimport { useToken } from '../theme/internal';\nimport { useLocale } from '../locale';\nconst Simple = () => {\n  const [, token] = useToken();\n  const [locale] = useLocale('Empty');\n  const {\n    colorFill,\n    colorFillTertiary,\n    colorFillQuaternary,\n    colorBgContainer\n  } = token;\n  const {\n    borderColor,\n    shadowColor,\n    contentColor\n  } = useMemo(() => ({\n    borderColor: new FastColor(colorFill).onBackground(colorBgContainer).toHexString(),\n    shadowColor: new FastColor(colorFillTertiary).onBackground(colorBgContainer).toHexString(),\n    contentColor: new FastColor(colorFillQuaternary).onBackground(colorBgContainer).toHexString()\n  }), [colorFill, colorFillTertiary, colorFillQuaternary, colorBgContainer]);\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    width: \"64\",\n    height: \"41\",\n    viewBox: \"0 0 64 41\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"title\", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(0 1)\",\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    fill: shadowColor,\n    cx: \"32\",\n    cy: \"33\",\n    rx: \"32\",\n    ry: \"7\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    fillRule: \"nonzero\",\n    stroke: borderColor\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\",\n    fill: contentColor\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Simple.displayName = 'SimpleImage';\n}\nexport default Simple;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,SAAS,QAAQ,WAAW;AACrC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAM,GAAGC,KAAK,CAAC,GAAGH,QAAQ,CAAC,CAAC;EAC5B,MAAM,CAACI,MAAM,CAAC,GAAGH,SAAS,CAAC,OAAO,CAAC;EACnC,MAAM;IACJI,SAAS;IACTC,iBAAiB;IACjBC,mBAAmB;IACnBC;EACF,CAAC,GAAGL,KAAK;EACT,MAAM;IACJM,WAAW;IACXC,WAAW;IACXC;EACF,CAAC,GAAGb,OAAO,CAAC,OAAO;IACjBW,WAAW,EAAE,IAAIV,SAAS,CAACM,SAAS,CAAC,CAACO,YAAY,CAACJ,gBAAgB,CAAC,CAACK,WAAW,CAAC,CAAC;IAClFH,WAAW,EAAE,IAAIX,SAAS,CAACO,iBAAiB,CAAC,CAACM,YAAY,CAACJ,gBAAgB,CAAC,CAACK,WAAW,CAAC,CAAC;IAC1FF,YAAY,EAAE,IAAIZ,SAAS,CAACQ,mBAAmB,CAAC,CAACK,YAAY,CAACJ,gBAAgB,CAAC,CAACK,WAAW,CAAC;EAC9F,CAAC,CAAC,EAAE,CAACR,SAAS,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAC1E,OAAO,aAAaX,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC,EAAE,aAAarB,KAAK,CAACiB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,CAACV,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,WAAW,KAAK,OAAO,CAAC,EAAE,aAAatB,KAAK,CAACiB,aAAa,CAAC,GAAG,EAAE;IACzKM,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAazB,KAAK,CAACiB,aAAa,CAAC,SAAS,EAAE;IAC7CO,IAAI,EAAEX,WAAW;IACjBa,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAa7B,KAAK,CAACiB,aAAa,CAAC,GAAG,EAAE;IACxCQ,QAAQ,EAAE,SAAS;IACnBK,MAAM,EAAElB;EACV,CAAC,EAAE,aAAaZ,KAAK,CAACiB,aAAa,CAAC,MAAM,EAAE;IAC1Cc,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACiB,aAAa,CAAC,MAAM,EAAE;IAC3Cc,CAAC,EAAE,+OAA+O;IAClPP,IAAI,EAAEV;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC7B,MAAM,CAAC8B,WAAW,GAAG,aAAa;AACpC;AACA,eAAe9B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}