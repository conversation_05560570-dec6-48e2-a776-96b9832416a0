{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Size ================================\nconst genSizeDividerStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-horizontal': {\n        [`&${componentCls}`]: {\n          '&-sm': {\n            marginBlock: token.marginXS\n          },\n          '&-md': {\n            marginBlock: token.margin\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst genSharedDividerStyle = token => {\n  const {\n    componentCls,\n    sizePaddingEdgeHorizontal,\n    colorSplit,\n    lineWidth,\n    textPaddingInline,\n    orientationMargin,\n    verticalMarginInline\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      borderBlockStart: `${unit(lineWidth)} solid ${colorSplit}`,\n      // vertical\n      '&-vertical': {\n        position: 'relative',\n        top: '-0.06em',\n        display: 'inline-block',\n        height: '0.9em',\n        marginInline: verticalMarginInline,\n        marginBlock: 0,\n        verticalAlign: 'middle',\n        borderTop: 0,\n        borderInlineStart: `${unit(lineWidth)} solid ${colorSplit}`\n      },\n      '&-horizontal': {\n        display: 'flex',\n        clear: 'both',\n        width: '100%',\n        minWidth: '100%',\n        // Fix https://github.com/ant-design/ant-design/issues/10914\n        margin: `${unit(token.marginLG)} 0`\n      },\n      [`&-horizontal${componentCls}-with-text`]: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: `${unit(token.dividerHorizontalWithTextGutterMargin)} 0`,\n        color: token.colorTextHeading,\n        fontWeight: 500,\n        fontSize: token.fontSizeLG,\n        whiteSpace: 'nowrap',\n        textAlign: 'center',\n        borderBlockStart: `0 ${colorSplit}`,\n        '&::before, &::after': {\n          position: 'relative',\n          width: '50%',\n          borderBlockStart: `${unit(lineWidth)} solid transparent`,\n          // Chrome not accept `inherit` in `border-top`\n          borderBlockStartColor: 'inherit',\n          borderBlockEnd: 0,\n          transform: 'translateY(50%)',\n          content: \"''\"\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-start`]: {\n        '&::before': {\n          width: `calc(${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end`]: {\n        '&::before': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(${orientationMargin} * 100%)`\n        }\n      },\n      [`${componentCls}-inner-text`]: {\n        display: 'inline-block',\n        paddingBlock: 0,\n        paddingInline: textPaddingInline\n      },\n      '&-dashed': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dashed',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dashed`]: {\n        '&::before, &::after': {\n          borderStyle: 'dashed none none'\n        }\n      },\n      [`&-vertical${componentCls}-dashed`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      '&-dotted': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dotted',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dotted`]: {\n        '&::before, &::after': {\n          borderStyle: 'dotted none none'\n        }\n      },\n      [`&-vertical${componentCls}-dotted`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      [`&-plain${componentCls}-with-text`]: {\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.fontSize\n      },\n      [`&-horizontal${componentCls}-with-text-start${componentCls}-no-default-orientation-margin-start`]: {\n        '&::before': {\n          width: 0\n        },\n        '&::after': {\n          width: '100%'\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineStart: sizePaddingEdgeHorizontal\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end${componentCls}-no-default-orientation-margin-end`]: {\n        '&::before': {\n          width: '100%'\n        },\n        '&::after': {\n          width: 0\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineEnd: sizePaddingEdgeHorizontal\n        }\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  textPaddingInline: '1em',\n  orientationMargin: 0.05,\n  verticalMarginInline: token.marginXS\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Divider', token => {\n  const dividerToken = mergeToken(token, {\n    dividerHorizontalWithTextGutterMargin: token.margin,\n    sizePaddingEdgeHorizontal: 0\n  });\n  return [genSharedDividerStyle(dividerToken), genSizeDividerStyle(dividerToken)];\n}, prepareComponentToken, {\n  unitless: {\n    orientationMargin: true\n  }\n});", "map": {"version": 3, "names": ["unit", "resetComponent", "genStyleHooks", "mergeToken", "genSizeDividerStyle", "token", "componentCls", "marginBlock", "marginXS", "margin", "genSharedDividerStyle", "sizePaddingEdgeHorizontal", "colorSplit", "lineWidth", "textPaddingInline", "<PERSON><PERSON><PERSON><PERSON>", "verticalMarginInline", "Object", "assign", "borderBlockStart", "position", "top", "display", "height", "marginInline", "verticalAlign", "borderTop", "borderInlineStart", "clear", "width", "min<PERSON><PERSON><PERSON>", "marginLG", "alignItems", "dividerHorizontalWithTextGutterMargin", "color", "colorTextHeading", "fontWeight", "fontSize", "fontSizeLG", "whiteSpace", "textAlign", "borderBlockStartColor", "borderBlockEnd", "transform", "content", "paddingBlock", "paddingInline", "background", "borderColor", "borderStyle", "borderWidth", "borderInlineStartWidth", "borderInlineEnd", "colorText", "paddingInlineStart", "paddingInlineEnd", "prepareComponentToken", "dividerToken", "unitless"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/divider/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Size ================================\nconst genSizeDividerStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-horizontal': {\n        [`&${componentCls}`]: {\n          '&-sm': {\n            marginBlock: token.marginXS\n          },\n          '&-md': {\n            marginBlock: token.margin\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst genSharedDividerStyle = token => {\n  const {\n    componentCls,\n    sizePaddingEdgeHorizontal,\n    colorSplit,\n    lineWidth,\n    textPaddingInline,\n    orientationMargin,\n    verticalMarginInline\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      borderBlockStart: `${unit(lineWidth)} solid ${colorSplit}`,\n      // vertical\n      '&-vertical': {\n        position: 'relative',\n        top: '-0.06em',\n        display: 'inline-block',\n        height: '0.9em',\n        marginInline: verticalMarginInline,\n        marginBlock: 0,\n        verticalAlign: 'middle',\n        borderTop: 0,\n        borderInlineStart: `${unit(lineWidth)} solid ${colorSplit}`\n      },\n      '&-horizontal': {\n        display: 'flex',\n        clear: 'both',\n        width: '100%',\n        minWidth: '100%',\n        // Fix https://github.com/ant-design/ant-design/issues/10914\n        margin: `${unit(token.marginLG)} 0`\n      },\n      [`&-horizontal${componentCls}-with-text`]: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: `${unit(token.dividerHorizontalWithTextGutterMargin)} 0`,\n        color: token.colorTextHeading,\n        fontWeight: 500,\n        fontSize: token.fontSizeLG,\n        whiteSpace: 'nowrap',\n        textAlign: 'center',\n        borderBlockStart: `0 ${colorSplit}`,\n        '&::before, &::after': {\n          position: 'relative',\n          width: '50%',\n          borderBlockStart: `${unit(lineWidth)} solid transparent`,\n          // Chrome not accept `inherit` in `border-top`\n          borderBlockStartColor: 'inherit',\n          borderBlockEnd: 0,\n          transform: 'translateY(50%)',\n          content: \"''\"\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-start`]: {\n        '&::before': {\n          width: `calc(${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end`]: {\n        '&::before': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(${orientationMargin} * 100%)`\n        }\n      },\n      [`${componentCls}-inner-text`]: {\n        display: 'inline-block',\n        paddingBlock: 0,\n        paddingInline: textPaddingInline\n      },\n      '&-dashed': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dashed',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dashed`]: {\n        '&::before, &::after': {\n          borderStyle: 'dashed none none'\n        }\n      },\n      [`&-vertical${componentCls}-dashed`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      '&-dotted': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dotted',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dotted`]: {\n        '&::before, &::after': {\n          borderStyle: 'dotted none none'\n        }\n      },\n      [`&-vertical${componentCls}-dotted`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      [`&-plain${componentCls}-with-text`]: {\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.fontSize\n      },\n      [`&-horizontal${componentCls}-with-text-start${componentCls}-no-default-orientation-margin-start`]: {\n        '&::before': {\n          width: 0\n        },\n        '&::after': {\n          width: '100%'\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineStart: sizePaddingEdgeHorizontal\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end${componentCls}-no-default-orientation-margin-end`]: {\n        '&::before': {\n          width: '100%'\n        },\n        '&::after': {\n          width: 0\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineEnd: sizePaddingEdgeHorizontal\n        }\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  textPaddingInline: '1em',\n  orientationMargin: 0.05,\n  verticalMarginInline: token.marginXS\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Divider', token => {\n  const dividerToken = mergeToken(token, {\n    dividerHorizontalWithTextGutterMargin: token.margin,\n    sizePaddingEdgeHorizontal: 0\n  });\n  return [genSharedDividerStyle(dividerToken), genSizeDividerStyle(dividerToken)];\n}, prepareComponentToken, {\n  unitless: {\n    orientationMargin: true\n  }\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,mBAAmB,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,cAAc,EAAE;QACd,CAAC,IAAIA,YAAY,EAAE,GAAG;UACpB,MAAM,EAAE;YACNC,WAAW,EAAEF,KAAK,CAACG;UACrB,CAAC;UACD,MAAM,EAAE;YACND,WAAW,EAAEF,KAAK,CAACI;UACrB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,MAAMC,qBAAqB,GAAGL,KAAK,IAAI;EACrC,MAAM;IACJC,YAAY;IACZK,yBAAyB;IACzBC,UAAU;IACVC,SAAS;IACTC,iBAAiB;IACjBC,iBAAiB;IACjBC;EACF,CAAC,GAAGX,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGW,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtEc,gBAAgB,EAAE,GAAGnB,IAAI,CAACa,SAAS,CAAC,UAAUD,UAAU,EAAE;MAC1D;MACA,YAAY,EAAE;QACZQ,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,SAAS;QACdC,OAAO,EAAE,cAAc;QACvBC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAER,oBAAoB;QAClCT,WAAW,EAAE,CAAC;QACdkB,aAAa,EAAE,QAAQ;QACvBC,SAAS,EAAE,CAAC;QACZC,iBAAiB,EAAE,GAAG3B,IAAI,CAACa,SAAS,CAAC,UAAUD,UAAU;MAC3D,CAAC;MACD,cAAc,EAAE;QACdU,OAAO,EAAE,MAAM;QACfM,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,MAAM;QAChB;QACArB,MAAM,EAAE,GAAGT,IAAI,CAACK,KAAK,CAAC0B,QAAQ,CAAC;MACjC,CAAC;MACD,CAAC,eAAezB,YAAY,YAAY,GAAG;QACzCgB,OAAO,EAAE,MAAM;QACfU,UAAU,EAAE,QAAQ;QACpBvB,MAAM,EAAE,GAAGT,IAAI,CAACK,KAAK,CAAC4B,qCAAqC,CAAC,IAAI;QAChEC,KAAK,EAAE7B,KAAK,CAAC8B,gBAAgB;QAC7BC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAEhC,KAAK,CAACiC,UAAU;QAC1BC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,QAAQ;QACnBrB,gBAAgB,EAAE,KAAKP,UAAU,EAAE;QACnC,qBAAqB,EAAE;UACrBQ,QAAQ,EAAE,UAAU;UACpBS,KAAK,EAAE,KAAK;UACZV,gBAAgB,EAAE,GAAGnB,IAAI,CAACa,SAAS,CAAC,oBAAoB;UACxD;UACA4B,qBAAqB,EAAE,SAAS;UAChCC,cAAc,EAAE,CAAC;UACjBC,SAAS,EAAE,iBAAiB;UAC5BC,OAAO,EAAE;QACX;MACF,CAAC;MACD,CAAC,eAAetC,YAAY,kBAAkB,GAAG;QAC/C,WAAW,EAAE;UACXuB,KAAK,EAAE,QAAQd,iBAAiB;QAClC,CAAC;QACD,UAAU,EAAE;UACVc,KAAK,EAAE,eAAed,iBAAiB;QACzC;MACF,CAAC;MACD,CAAC,eAAeT,YAAY,gBAAgB,GAAG;QAC7C,WAAW,EAAE;UACXuB,KAAK,EAAE,eAAed,iBAAiB;QACzC,CAAC;QACD,UAAU,EAAE;UACVc,KAAK,EAAE,QAAQd,iBAAiB;QAClC;MACF,CAAC;MACD,CAAC,GAAGT,YAAY,aAAa,GAAG;QAC9BgB,OAAO,EAAE,cAAc;QACvBuB,YAAY,EAAE,CAAC;QACfC,aAAa,EAAEhC;MACjB,CAAC;MACD,UAAU,EAAE;QACViC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAEpC,UAAU;QACvBqC,WAAW,EAAE,QAAQ;QACrBC,WAAW,EAAE,GAAGlD,IAAI,CAACa,SAAS,CAAC;MACjC,CAAC;MACD,CAAC,eAAeP,YAAY,aAAaA,YAAY,SAAS,GAAG;QAC/D,qBAAqB,EAAE;UACrB2C,WAAW,EAAE;QACf;MACF,CAAC;MACD,CAAC,aAAa3C,YAAY,SAAS,GAAG;QACpC6C,sBAAsB,EAAEtC,SAAS;QACjCuC,eAAe,EAAE,CAAC;QAClBjC,gBAAgB,EAAE,CAAC;QACnBuB,cAAc,EAAE;MAClB,CAAC;MACD,UAAU,EAAE;QACVK,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAEpC,UAAU;QACvBqC,WAAW,EAAE,QAAQ;QACrBC,WAAW,EAAE,GAAGlD,IAAI,CAACa,SAAS,CAAC;MACjC,CAAC;MACD,CAAC,eAAeP,YAAY,aAAaA,YAAY,SAAS,GAAG;QAC/D,qBAAqB,EAAE;UACrB2C,WAAW,EAAE;QACf;MACF,CAAC;MACD,CAAC,aAAa3C,YAAY,SAAS,GAAG;QACpC6C,sBAAsB,EAAEtC,SAAS;QACjCuC,eAAe,EAAE,CAAC;QAClBjC,gBAAgB,EAAE,CAAC;QACnBuB,cAAc,EAAE;MAClB,CAAC;MACD,CAAC,UAAUpC,YAAY,YAAY,GAAG;QACpC4B,KAAK,EAAE7B,KAAK,CAACgD,SAAS;QACtBjB,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAEhC,KAAK,CAACgC;MAClB,CAAC;MACD,CAAC,eAAe/B,YAAY,mBAAmBA,YAAY,sCAAsC,GAAG;QAClG,WAAW,EAAE;UACXuB,KAAK,EAAE;QACT,CAAC;QACD,UAAU,EAAE;UACVA,KAAK,EAAE;QACT,CAAC;QACD,CAAC,GAAGvB,YAAY,aAAa,GAAG;UAC9BgD,kBAAkB,EAAE3C;QACtB;MACF,CAAC;MACD,CAAC,eAAeL,YAAY,iBAAiBA,YAAY,oCAAoC,GAAG;QAC9F,WAAW,EAAE;UACXuB,KAAK,EAAE;QACT,CAAC;QACD,UAAU,EAAE;UACVA,KAAK,EAAE;QACT,CAAC;QACD,CAAC,GAAGvB,YAAY,aAAa,GAAG;UAC9BiD,gBAAgB,EAAE5C;QACpB;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAM6C,qBAAqB,GAAGnD,KAAK,KAAK;EAC7CS,iBAAiB,EAAE,KAAK;EACxBC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAEX,KAAK,CAACG;AAC9B,CAAC,CAAC;AACF;AACA,eAAeN,aAAa,CAAC,SAAS,EAAEG,KAAK,IAAI;EAC/C,MAAMoD,YAAY,GAAGtD,UAAU,CAACE,KAAK,EAAE;IACrC4B,qCAAqC,EAAE5B,KAAK,CAACI,MAAM;IACnDE,yBAAyB,EAAE;EAC7B,CAAC,CAAC;EACF,OAAO,CAACD,qBAAqB,CAAC+C,YAAY,CAAC,EAAErD,mBAAmB,CAACqD,YAAY,CAAC,CAAC;AACjF,CAAC,EAAED,qBAAqB,EAAE;EACxBE,QAAQ,EAAE;IACR3C,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}