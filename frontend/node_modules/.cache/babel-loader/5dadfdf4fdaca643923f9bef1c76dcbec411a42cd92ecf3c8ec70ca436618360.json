{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { ItemGroup } from 'rc-menu';\nimport { SiderContext } from '../layout/Sider';\nimport InternalMenu from './menu';\nimport MenuDivider from './MenuDivider';\nimport Item from './MenuItem';\nimport SubMenu from './SubMenu';\nconst Menu = /*#__PURE__*/forwardRef((props, ref) => {\n  const menuRef = useRef(null);\n  const context = React.useContext(SiderContext);\n  useImperativeHandle(ref, () => ({\n    menu: menuRef.current,\n    focus: options => {\n      var _a;\n      (_a = menuRef.current) === null || _a === void 0 ? void 0 : _a.focus(options);\n    }\n  }));\n  return /*#__PURE__*/React.createElement(InternalMenu, Object.assign({\n    ref: menuRef\n  }, props, context));\n});\nMenu.Item = Item;\nMenu.SubMenu = SubMenu;\nMenu.Divider = MenuDivider;\nMenu.ItemGroup = ItemGroup;\nif (process.env.NODE_ENV !== 'production') {\n  Menu.displayName = 'Menu';\n}\nexport default Menu;", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useRef", "ItemGroup", "SiderContext", "InternalMenu", "MenuDivider", "<PERSON><PERSON>", "SubMenu", "<PERSON><PERSON>", "props", "ref", "menuRef", "context", "useContext", "menu", "current", "focus", "options", "_a", "createElement", "Object", "assign", "Divider", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/menu/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { ItemGroup } from 'rc-menu';\nimport { SiderContext } from '../layout/Sider';\nimport InternalMenu from './menu';\nimport MenuDivider from './MenuDivider';\nimport Item from './MenuItem';\nimport SubMenu from './SubMenu';\nconst Menu = /*#__PURE__*/forwardRef((props, ref) => {\n  const menuRef = useRef(null);\n  const context = React.useContext(SiderContext);\n  useImperativeHandle(ref, () => ({\n    menu: menuRef.current,\n    focus: options => {\n      var _a;\n      (_a = menuRef.current) === null || _a === void 0 ? void 0 : _a.focus(options);\n    }\n  }));\n  return /*#__PURE__*/React.createElement(InternalMenu, Object.assign({\n    ref: menuRef\n  }, props, context));\n});\nMenu.Item = Item;\nMenu.SubMenu = SubMenu;\nMenu.Divider = MenuDivider;\nMenu.ItemGroup = ItemGroup;\nif (process.env.NODE_ENV !== 'production') {\n  Menu.displayName = 'Menu';\n}\nexport default Menu;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AAC/D,SAASC,SAAS,QAAQ,SAAS;AACnC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,YAAY,MAAM,QAAQ;AACjC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,MAAMC,IAAI,GAAG,aAAaT,UAAU,CAAC,CAACU,KAAK,EAAEC,GAAG,KAAK;EACnD,MAAMC,OAAO,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMW,OAAO,GAAGd,KAAK,CAACe,UAAU,CAACV,YAAY,CAAC;EAC9CH,mBAAmB,CAACU,GAAG,EAAE,OAAO;IAC9BI,IAAI,EAAEH,OAAO,CAACI,OAAO;IACrBC,KAAK,EAAEC,OAAO,IAAI;MAChB,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGP,OAAO,CAACI,OAAO,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,KAAK,CAACC,OAAO,CAAC;IAC/E;EACF,CAAC,CAAC,CAAC;EACH,OAAO,aAAanB,KAAK,CAACqB,aAAa,CAACf,YAAY,EAAEgB,MAAM,CAACC,MAAM,CAAC;IAClEX,GAAG,EAAEC;EACP,CAAC,EAAEF,KAAK,EAAEG,OAAO,CAAC,CAAC;AACrB,CAAC,CAAC;AACFJ,IAAI,CAACF,IAAI,GAAGA,IAAI;AAChBE,IAAI,CAACD,OAAO,GAAGA,OAAO;AACtBC,IAAI,CAACc,OAAO,GAAGjB,WAAW;AAC1BG,IAAI,CAACN,SAAS,GAAGA,SAAS;AAC1B,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,IAAI,CAACkB,WAAW,GAAG,MAAM;AAC3B;AACA,eAAelB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}