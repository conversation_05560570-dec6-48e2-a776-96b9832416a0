{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport useStyle from './style';\nimport unauthorized from './unauthorized';\nexport const IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport const ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n};\n// ExceptionImageMap keys\nconst ExceptionStatus = Object.keys(ExceptionMap);\nconst Icon = ({\n  prefixCls,\n  icon,\n  status\n}) => {\n  const className = classNames(`${prefixCls}-icon`);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Result');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  if (ExceptionStatus.includes(`${status}`)) {\n    const SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: `${className} ${prefixCls}-image`\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  const iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  if (icon === null || icon === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nconst Extra = ({\n  prefixCls,\n  extra\n}) => {\n  if (!extra) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-extra`\n  }, extra);\n};\nconst Result = ({\n  prefixCls: customizePrefixCls,\n  className: customizeClassName,\n  rootClassName,\n  subTitle,\n  title,\n  style,\n  children,\n  status = 'info',\n  icon,\n  extra\n}) => {\n  const {\n    getPrefixCls,\n    direction,\n    result\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('result', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, `${prefixCls}-${status}`, customizeClassName, result === null || result === void 0 ? void 0 : result.className, rootClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, result === null || result === void 0 ? void 0 : result.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: mergedStyle\n  }, /*#__PURE__*/React.createElement(Icon, {\n    prefixCls: prefixCls,\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-subtitle`\n  }, subTitle), /*#__PURE__*/React.createElement(Extra, {\n    prefixCls: prefixCls,\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, children)));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nif (process.env.NODE_ENV !== 'production') {\n  Result.displayName = 'Result';\n}\nexport default Result;", "map": {"version": 3, "names": ["React", "CheckCircleFilled", "CloseCircleFilled", "ExclamationCircleFilled", "WarningFilled", "classNames", "devUseW<PERSON>ning", "ConfigContext", "noFound", "serverError", "useStyle", "unauthorized", "IconMap", "success", "error", "info", "warning", "ExceptionMap", "ExceptionStatus", "Object", "keys", "Icon", "prefixCls", "icon", "status", "className", "process", "env", "NODE_ENV", "length", "includes", "SVGComponent", "createElement", "iconNode", "Extra", "extra", "Result", "customizePrefixCls", "customizeClassName", "rootClassName", "subTitle", "title", "style", "children", "getPrefixCls", "direction", "result", "useContext", "wrapCSSVar", "hashId", "cssVarCls", "mergedStyle", "assign", "PRESENTED_IMAGE_403", "PRESENTED_IMAGE_404", "PRESENTED_IMAGE_500", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/result/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport useStyle from './style';\nimport unauthorized from './unauthorized';\nexport const IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport const ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n};\n// ExceptionImageMap keys\nconst ExceptionStatus = Object.keys(ExceptionMap);\nconst Icon = ({\n  prefixCls,\n  icon,\n  status\n}) => {\n  const className = classNames(`${prefixCls}-icon`);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Result');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  if (ExceptionStatus.includes(`${status}`)) {\n    const SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: `${className} ${prefixCls}-image`\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  const iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  if (icon === null || icon === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nconst Extra = ({\n  prefixCls,\n  extra\n}) => {\n  if (!extra) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-extra`\n  }, extra);\n};\nconst Result = ({\n  prefixCls: customizePrefixCls,\n  className: customizeClassName,\n  rootClassName,\n  subTitle,\n  title,\n  style,\n  children,\n  status = 'info',\n  icon,\n  extra\n}) => {\n  const {\n    getPrefixCls,\n    direction,\n    result\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('result', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, `${prefixCls}-${status}`, customizeClassName, result === null || result === void 0 ? void 0 : result.className, rootClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, result === null || result === void 0 ? void 0 : result.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: mergedStyle\n  }, /*#__PURE__*/React.createElement(Icon, {\n    prefixCls: prefixCls,\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-subtitle`\n  }, subTitle), /*#__PURE__*/React.createElement(Extra, {\n    prefixCls: prefixCls,\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, children)));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nif (process.env.NODE_ENV !== 'production') {\n  Result.displayName = 'Result';\n}\nexport default Result;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,uBAAuB,MAAM,oDAAoD;AACxF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,MAAMC,OAAO,GAAG;EACrBC,OAAO,EAAEZ,iBAAiB;EAC1Ba,KAAK,EAAEZ,iBAAiB;EACxBa,IAAI,EAAEZ,uBAAuB;EAC7Ba,OAAO,EAAEZ;AACX,CAAC;AACD,OAAO,MAAMa,YAAY,GAAG;EAC1B,KAAK,EAAET,OAAO;EACd,KAAK,EAAEC,WAAW;EAClB,KAAK,EAAEE;AACT,CAAC;AACD;AACA,MAAMO,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC;AACjD,MAAMI,IAAI,GAAGA,CAAC;EACZC,SAAS;EACTC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAGpB,UAAU,CAAC,GAAGiB,SAAS,OAAO,CAAC;EACjD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMZ,OAAO,GAAGV,aAAa,CAAC,QAAQ,CAAC;IACvCoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,OAAO,CAAC,EAAE,OAAOO,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,8EAA8EN,IAAI,0CAA0C,CAAC,GAAG,KAAK,CAAC;EACpP;EACA,IAAIL,eAAe,CAACY,QAAQ,CAAC,GAAGN,MAAM,EAAE,CAAC,EAAE;IACzC,MAAMO,YAAY,GAAGd,YAAY,CAACO,MAAM,CAAC;IACzC,OAAO,aAAaxB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;MAC7CP,SAAS,EAAE,GAAGA,SAAS,IAAIH,SAAS;IACtC,CAAC,EAAE,aAAatB,KAAK,CAACgC,aAAa,CAACD,YAAY,EAAE,IAAI,CAAC,CAAC;EAC1D;EACA,MAAME,QAAQ,GAAG,aAAajC,KAAK,CAACgC,aAAa,CAACpB,OAAO,CAACY,MAAM,CAAC,CAAC;EAClE,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;IACnC,OAAO,IAAI;EACb;EACA,OAAO,aAAavB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7CP,SAAS,EAAEA;EACb,CAAC,EAAEF,IAAI,IAAIU,QAAQ,CAAC;AACtB,CAAC;AACD,MAAMC,KAAK,GAAGA,CAAC;EACbZ,SAAS;EACTa;AACF,CAAC,KAAK;EACJ,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,OAAO,aAAanC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7CP,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,EAAEa,KAAK,CAAC;AACX,CAAC;AACD,MAAMC,MAAM,GAAGA,CAAC;EACdd,SAAS,EAAEe,kBAAkB;EAC7BZ,SAAS,EAAEa,kBAAkB;EAC7BC,aAAa;EACbC,QAAQ;EACRC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRnB,MAAM,GAAG,MAAM;EACfD,IAAI;EACJY;AACF,CAAC,KAAK;EACJ,MAAM;IACJS,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAG9C,KAAK,CAAC+C,UAAU,CAACxC,aAAa,CAAC;EACnC,MAAMe,SAAS,GAAGsB,YAAY,CAAC,QAAQ,EAAEP,kBAAkB,CAAC;EAC5D;EACA,MAAM,CAACW,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAACY,SAAS,CAAC;EAC3D,MAAMG,SAAS,GAAGpB,UAAU,CAACiB,SAAS,EAAE,GAAGA,SAAS,IAAIE,MAAM,EAAE,EAAEc,kBAAkB,EAAEQ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACrB,SAAS,EAAEc,aAAa,EAAE;IACrK,CAAC,GAAGjB,SAAS,MAAM,GAAGuB,SAAS,KAAK;EACtC,CAAC,EAAEI,MAAM,EAAEC,SAAS,CAAC;EACrB,MAAMC,WAAW,GAAGhC,MAAM,CAACiC,MAAM,CAACjC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACJ,KAAK,CAAC,EAAEA,KAAK,CAAC;EACzH,OAAOM,UAAU,CAAC,aAAahD,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACxDP,SAAS,EAAEA,SAAS;IACpBiB,KAAK,EAAES;EACT,CAAC,EAAE,aAAanD,KAAK,CAACgC,aAAa,CAACX,IAAI,EAAE;IACxCC,SAAS,EAAEA,SAAS;IACpBE,MAAM,EAAEA,MAAM;IACdD,IAAI,EAAEA;EACR,CAAC,CAAC,EAAE,aAAavB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC1CP,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,EAAEmB,KAAK,CAAC,EAAED,QAAQ,IAAI,aAAaxC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7DP,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,EAAEkB,QAAQ,CAAC,EAAE,aAAaxC,KAAK,CAACgC,aAAa,CAACE,KAAK,EAAE;IACpDZ,SAAS,EAAEA,SAAS;IACpBa,KAAK,EAAEA;EACT,CAAC,CAAC,EAAEQ,QAAQ,IAAI,aAAa3C,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACtDP,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,EAAEqB,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC;AACDP,MAAM,CAACiB,mBAAmB,GAAGpC,YAAY,CAAC,KAAK,CAAC;AAChDmB,MAAM,CAACkB,mBAAmB,GAAGrC,YAAY,CAAC,KAAK,CAAC;AAChDmB,MAAM,CAACmB,mBAAmB,GAAGtC,YAAY,CAAC,KAAK,CAAC;AAChD,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCQ,MAAM,CAACoB,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAepB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}