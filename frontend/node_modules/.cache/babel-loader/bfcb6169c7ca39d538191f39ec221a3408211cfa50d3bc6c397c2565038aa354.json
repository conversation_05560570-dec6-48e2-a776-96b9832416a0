{"ast": null, "code": "\"use client\";\n\nimport { Col } from '../grid';\nexport default Col;", "map": {"version": 3, "names": ["Col"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/col/index.js"], "sourcesContent": ["\"use client\";\n\nimport { Col } from '../grid';\nexport default Col;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,GAAG,QAAQ,SAAS;AAC7B,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}