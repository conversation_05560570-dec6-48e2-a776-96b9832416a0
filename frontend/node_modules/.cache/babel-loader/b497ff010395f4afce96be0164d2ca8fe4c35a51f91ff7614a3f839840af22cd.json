{"ast": null, "code": "\"use client\";\n\nimport App_ from './App';\nimport useApp from './useApp';\nconst App = App_;\nApp.useApp = useApp;\nexport default App;", "map": {"version": 3, "names": ["App_", "useApp", "App"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/app/index.js"], "sourcesContent": ["\"use client\";\n\nimport App_ from './App';\nimport useApp from './useApp';\nconst App = App_;\nApp.useApp = useApp;\nexport default App;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,IAAI,MAAM,OAAO;AACxB,OAAOC,MAAM,MAAM,UAAU;AAC7B,MAAMC,GAAG,GAAGF,IAAI;AAChBE,GAAG,CAACD,MAAM,GAAGA,MAAM;AACnB,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}