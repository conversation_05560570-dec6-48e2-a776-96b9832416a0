{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { List } from 'rc-field-form';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemPrefixContext } from './context';\nconst FormList = _a => {\n  var {\n      prefixCls: customizePrefixCls,\n      children\n    } = _a,\n    props = __rest(_a, [\"prefixCls\", \"children\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Form.List');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof props.name === 'number' || (Array.isArray(props.name) ? !!props.name.length : !!props.name), 'usage', 'Miss `name` prop.') : void 0;\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('form', customizePrefixCls);\n  const contextValue = React.useMemo(() => ({\n    prefixCls,\n    status: 'error'\n  }), [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, Object.assign({}, props), (fields, operation, meta) => (/*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: contextValue\n  }, children(fields.map(field => Object.assign(Object.assign({}, field), {\n    fieldKey: field.key\n  })), operation, {\n    errors: meta.errors,\n    warnings: meta.warnings\n  }))));\n};\nexport default FormList;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "List", "devUseW<PERSON>ning", "ConfigContext", "FormItemPrefixContext", "FormList", "_a", "prefixCls", "customizePrefixCls", "children", "props", "process", "env", "NODE_ENV", "warning", "name", "Array", "isArray", "getPrefixCls", "useContext", "contextValue", "useMemo", "status", "createElement", "assign", "fields", "operation", "meta", "Provider", "value", "map", "field", "<PERSON><PERSON><PERSON>", "key", "errors", "warnings"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/form/FormList.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { List } from 'rc-field-form';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemPrefixContext } from './context';\nconst FormList = _a => {\n  var {\n      prefixCls: customizePrefixCls,\n      children\n    } = _a,\n    props = __rest(_a, [\"prefixCls\", \"children\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Form.List');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof props.name === 'number' || (Array.isArray(props.name) ? !!props.name.length : !!props.name), 'usage', 'Miss `name` prop.') : void 0;\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('form', customizePrefixCls);\n  const contextValue = React.useMemo(() => ({\n    prefixCls,\n    status: 'error'\n  }), [prefixCls]);\n  return /*#__PURE__*/React.createElement(List, Object.assign({}, props), (fields, operation, meta) => (/*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: contextValue\n  }, children(fields.map(field => Object.assign(Object.assign({}, field), {\n    fieldKey: field.key\n  })), operation, {\n    errors: meta.errors,\n    warnings: meta.warnings\n  }))));\n};\nexport default FormList;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,MAAMC,QAAQ,GAAGC,EAAE,IAAI;EACrB,IAAI;MACAC,SAAS,EAAEC,kBAAkB;MAC7BC;IACF,CAAC,GAAGH,EAAE;IACNI,KAAK,GAAGxB,MAAM,CAACoB,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EAC/C,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGZ,aAAa,CAAC,WAAW,CAAC;IAC1CS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,OAAOJ,KAAK,CAACK,IAAI,KAAK,QAAQ,KAAKC,KAAK,CAACC,OAAO,CAACP,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC,CAACL,KAAK,CAACK,IAAI,CAACjB,MAAM,GAAG,CAAC,CAACY,KAAK,CAACK,IAAI,CAAC,EAAE,OAAO,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAC;EAC5L;EACA,MAAM;IACJG;EACF,CAAC,GAAGlB,KAAK,CAACmB,UAAU,CAAChB,aAAa,CAAC;EACnC,MAAMI,SAAS,GAAGW,YAAY,CAAC,MAAM,EAAEV,kBAAkB,CAAC;EAC1D,MAAMY,YAAY,GAAGpB,KAAK,CAACqB,OAAO,CAAC,OAAO;IACxCd,SAAS;IACTe,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,CAACf,SAAS,CAAC,CAAC;EAChB,OAAO,aAAaP,KAAK,CAACuB,aAAa,CAACtB,IAAI,EAAEV,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEd,KAAK,CAAC,EAAE,CAACe,MAAM,EAAEC,SAAS,EAAEC,IAAI,MAAM,aAAa3B,KAAK,CAACuB,aAAa,CAACnB,qBAAqB,CAACwB,QAAQ,EAAE;IACrKC,KAAK,EAAET;EACT,CAAC,EAAEX,QAAQ,CAACgB,MAAM,CAACK,GAAG,CAACC,KAAK,IAAIxC,MAAM,CAACiC,MAAM,CAACjC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEO,KAAK,CAAC,EAAE;IACtEC,QAAQ,EAAED,KAAK,CAACE;EAClB,CAAC,CAAC,CAAC,EAAEP,SAAS,EAAE;IACdQ,MAAM,EAAEP,IAAI,CAACO,MAAM;IACnBC,QAAQ,EAAER,IAAI,CAACQ;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,eAAe9B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}