{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport convertToTooltipProps from '../_util/convertToTooltipProps';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport Badge from '../badge';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Tooltip from '../tooltip';\nimport FloatButtonGroupContext from './context';\nimport Content from './FloatButtonContent';\nimport useStyle from './style';\nexport const floatButtonPrefixCls = 'float-btn';\nconst InternalFloatButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      type = 'default',\n      shape = 'circle',\n      icon,\n      description,\n      tooltip,\n      htmlType = 'button',\n      badge = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"type\", \"shape\", \"icon\", \"description\", \"tooltip\", \"htmlType\", \"badge\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const groupShape = useContext(FloatButtonGroupContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedShape = groupShape || shape;\n  const classString = classNames(hashId, cssVarCls, rootCls, prefixCls, className, rootClassName, `${prefixCls}-${type}`, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  // 虽然在 ts 中已经 omit 过了，但是为了防止多余的属性被透传进来，这里再 omit 一遍，以防万一\n  const badgeProps = omit(badge, ['title', 'children', 'status', 'text']);\n  let buttonNode = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-body`\n  }, /*#__PURE__*/React.createElement(Content, {\n    prefixCls: prefixCls,\n    description: description,\n    icon: icon\n  }));\n  if ('badge' in props) {\n    buttonNode = /*#__PURE__*/React.createElement(Badge, Object.assign({}, badgeProps), buttonNode);\n  }\n  // ============================ Tooltip ============================\n  const tooltipProps = convertToTooltipProps(tooltip);\n  if (tooltipProps) {\n    buttonNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), buttonNode);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton');\n    process.env.NODE_ENV !== \"production\" ? warning(!(shape === 'circle' && description), 'usage', 'supported only when `shape` is `square`. Due to narrow space for text, short sentence is recommended.') : void 0;\n  }\n  return wrapCSSVar(props.href ? (/*#__PURE__*/React.createElement(\"a\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle\n  }), buttonNode)) : (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle,\n    type: htmlType\n  }), buttonNode)));\n});\nconst FloatButton = InternalFloatButton;\nif (process.env.NODE_ENV !== 'production') {\n  FloatButton.displayName = 'FloatButton';\n}\nexport default FloatButton;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useContext", "classNames", "omit", "convertToTooltipProps", "useZIndex", "devUseW<PERSON>ning", "Badge", "ConfigContext", "useCSSVarCls", "<PERSON><PERSON><PERSON>", "FloatButtonGroupContext", "Content", "useStyle", "floatButtonPrefixCls", "InternalFloatButton", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "type", "shape", "icon", "description", "tooltip", "htmlType", "badge", "restProps", "getPrefixCls", "direction", "groupShape", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "mergedShape", "classString", "zIndex", "mergedStyle", "assign", "badgeProps", "buttonNode", "createElement", "tooltipProps", "process", "env", "NODE_ENV", "warning", "href", "FloatButton", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/float-button/FloatButton.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport convertToTooltipProps from '../_util/convertToTooltipProps';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport Badge from '../badge';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Tooltip from '../tooltip';\nimport FloatButtonGroupContext from './context';\nimport Content from './FloatButtonContent';\nimport useStyle from './style';\nexport const floatButtonPrefixCls = 'float-btn';\nconst InternalFloatButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      type = 'default',\n      shape = 'circle',\n      icon,\n      description,\n      tooltip,\n      htmlType = 'button',\n      badge = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"type\", \"shape\", \"icon\", \"description\", \"tooltip\", \"htmlType\", \"badge\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const groupShape = useContext(FloatButtonGroupContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedShape = groupShape || shape;\n  const classString = classNames(hashId, cssVarCls, rootCls, prefixCls, className, rootClassName, `${prefixCls}-${type}`, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  // 虽然在 ts 中已经 omit 过了，但是为了防止多余的属性被透传进来，这里再 omit 一遍，以防万一\n  const badgeProps = omit(badge, ['title', 'children', 'status', 'text']);\n  let buttonNode = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-body`\n  }, /*#__PURE__*/React.createElement(Content, {\n    prefixCls: prefixCls,\n    description: description,\n    icon: icon\n  }));\n  if ('badge' in props) {\n    buttonNode = /*#__PURE__*/React.createElement(Badge, Object.assign({}, badgeProps), buttonNode);\n  }\n  // ============================ Tooltip ============================\n  const tooltipProps = convertToTooltipProps(tooltip);\n  if (tooltipProps) {\n    buttonNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), buttonNode);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton');\n    process.env.NODE_ENV !== \"production\" ? warning(!(shape === 'circle' && description), 'usage', 'supported only when `shape` is `square`. Due to narrow space for text, short sentence is recommended.') : void 0;\n  }\n  return wrapCSSVar(props.href ? (/*#__PURE__*/React.createElement(\"a\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle\n  }), buttonNode)) : (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle,\n    type: htmlType\n  }), buttonNode)));\n});\nconst FloatButton = InternalFloatButton;\nif (process.env.NODE_ENV !== 'production') {\n  FloatButton.displayName = 'FloatButton';\n}\nexport default FloatButton;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,uBAAuB,MAAM,WAAW;AAC/C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAO,MAAMC,oBAAoB,GAAG,WAAW;AAC/C,MAAMC,mBAAmB,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACxE,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,aAAa;MACbC,KAAK;MACLC,IAAI,GAAG,SAAS;MAChBC,KAAK,GAAG,QAAQ;MAChBC,IAAI;MACJC,WAAW;MACXC,OAAO;MACPC,QAAQ,GAAG,QAAQ;MACnBC,KAAK,GAAG,CAAC;IACX,CAAC,GAAGb,KAAK;IACTc,SAAS,GAAG7C,MAAM,CAAC+B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;EACzJ,MAAM;IACJe,YAAY;IACZC;EACF,CAAC,GAAGhC,UAAU,CAACO,aAAa,CAAC;EAC7B,MAAM0B,UAAU,GAAGjC,UAAU,CAACU,uBAAuB,CAAC;EACtD,MAAMQ,SAAS,GAAGa,YAAY,CAAClB,oBAAoB,EAAEM,kBAAkB,CAAC;EACxE,MAAMe,OAAO,GAAG1B,YAAY,CAACU,SAAS,CAAC;EACvC,MAAM,CAACiB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAACM,SAAS,EAAEgB,OAAO,CAAC;EACpE,MAAMI,WAAW,GAAGL,UAAU,IAAIT,KAAK;EACvC,MAAMe,WAAW,GAAGtC,UAAU,CAACmC,MAAM,EAAEC,SAAS,EAAEH,OAAO,EAAEhB,SAAS,EAAEE,SAAS,EAAEC,aAAa,EAAE,GAAGH,SAAS,IAAIK,IAAI,EAAE,EAAE,GAAGL,SAAS,IAAIoB,WAAW,EAAE,EAAE;IACrJ,CAAC,GAAGpB,SAAS,MAAM,GAAGc,SAAS,KAAK;EACtC,CAAC,CAAC;EACF;EACA,MAAM,CAACQ,MAAM,CAAC,GAAGpC,SAAS,CAAC,aAAa,EAAEkB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkB,MAAM,CAAC;EACrG,MAAMC,WAAW,GAAGnD,MAAM,CAACoD,MAAM,CAACpD,MAAM,CAACoD,MAAM,CAAC,CAAC,CAAC,EAAEpB,KAAK,CAAC,EAAE;IAC1DkB;EACF,CAAC,CAAC;EACF;EACA,MAAMG,UAAU,GAAGzC,IAAI,CAAC2B,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;EACvE,IAAIe,UAAU,GAAG,aAAa7C,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;IACvDzB,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAE,aAAanB,KAAK,CAAC8C,aAAa,CAAClC,OAAO,EAAE;IAC3CO,SAAS,EAAEA,SAAS;IACpBQ,WAAW,EAAEA,WAAW;IACxBD,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC;EACH,IAAI,OAAO,IAAIT,KAAK,EAAE;IACpB4B,UAAU,GAAG,aAAa7C,KAAK,CAAC8C,aAAa,CAACvC,KAAK,EAAEhB,MAAM,CAACoD,MAAM,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,EAAEC,UAAU,CAAC;EACjG;EACA;EACA,MAAME,YAAY,GAAG3C,qBAAqB,CAACwB,OAAO,CAAC;EACnD,IAAImB,YAAY,EAAE;IAChBF,UAAU,GAAG,aAAa7C,KAAK,CAAC8C,aAAa,CAACpC,OAAO,EAAEnB,MAAM,CAACoD,MAAM,CAAC,CAAC,CAAC,EAAEI,YAAY,CAAC,EAAEF,UAAU,CAAC;EACrG;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG7C,aAAa,CAAC,aAAa,CAAC;IAC5C0C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAE1B,KAAK,KAAK,QAAQ,IAAIE,WAAW,CAAC,EAAE,OAAO,EAAE,uGAAuG,CAAC,GAAG,KAAK,CAAC;EAClN;EACA,OAAOS,UAAU,CAACnB,KAAK,CAACmC,IAAI,IAAI,aAAapD,KAAK,CAAC8C,aAAa,CAAC,GAAG,EAAEvD,MAAM,CAACoD,MAAM,CAAC;IAClFzB,GAAG,EAAEA;EACP,CAAC,EAAEa,SAAS,EAAE;IACZV,SAAS,EAAEmB,WAAW;IACtBjB,KAAK,EAAEmB;EACT,CAAC,CAAC,EAAEG,UAAU,CAAC,KAAK,aAAa7C,KAAK,CAAC8C,aAAa,CAAC,QAAQ,EAAEvD,MAAM,CAACoD,MAAM,CAAC;IAC3EzB,GAAG,EAAEA;EACP,CAAC,EAAEa,SAAS,EAAE;IACZV,SAAS,EAAEmB,WAAW;IACtBjB,KAAK,EAAEmB,WAAW;IAClBlB,IAAI,EAAEK;EACR,CAAC,CAAC,EAAEgB,UAAU,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC;AACF,MAAMQ,WAAW,GAAGtC,mBAAmB;AACvC,IAAIiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCG,WAAW,CAACC,WAAW,GAAG,aAAa;AACzC;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}