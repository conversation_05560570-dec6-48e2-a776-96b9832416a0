{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { ConfigContext } from '../config-provider';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nexport const SpaceCompactItemContext = /*#__PURE__*/React.createContext(null);\nexport const useCompactItemContext = (prefixCls, direction) => {\n  const compactItemContext = React.useContext(SpaceCompactItemContext);\n  const compactItemClassnames = React.useMemo(() => {\n    if (!compactItemContext) {\n      return '';\n    }\n    const {\n      compactDirection,\n      isFirstItem,\n      isLastItem\n    } = compactItemContext;\n    const separator = compactDirection === 'vertical' ? '-vertical-' : '-';\n    return classNames(`${prefixCls}-compact${separator}item`, {\n      [`${prefixCls}-compact${separator}first-item`]: isFirstItem,\n      [`${prefixCls}-compact${separator}last-item`]: isLastItem,\n      [`${prefixCls}-compact${separator}item-rtl`]: direction === 'rtl'\n    });\n  }, [prefixCls, direction, compactItemContext]);\n  return {\n    compactSize: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactSize,\n    compactDirection: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactDirection,\n    compactItemClassnames\n  };\n};\nexport const NoCompactStyle = props => {\n  const {\n    children\n  } = props;\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: null\n  }, children);\n};\nconst CompactItem = props => {\n  const {\n      children\n    } = props,\n    others = __rest(props, [\"children\"]);\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: React.useMemo(() => others, [others])\n  }, children);\n};\nconst Compact = props => {\n  const {\n    getPrefixCls,\n    direction: directionConfig\n  } = React.useContext(ConfigContext);\n  const {\n      size,\n      direction,\n      block,\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      children\n    } = props,\n    restProps = __rest(props, [\"size\", \"direction\", \"block\", \"prefixCls\", \"className\", \"rootClassName\", \"children\"]);\n  const mergedSize = useSize(ctx => size !== null && size !== void 0 ? size : ctx);\n  const prefixCls = getPrefixCls('space-compact', customizePrefixCls);\n  const [wrapCSSVar, hashId] = useStyle(prefixCls);\n  const clx = classNames(prefixCls, hashId, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-block`]: block,\n    [`${prefixCls}-vertical`]: direction === 'vertical'\n  }, className, rootClassName);\n  const compactItemContext = React.useContext(SpaceCompactItemContext);\n  const childNodes = toArray(children);\n  const nodes = React.useMemo(() => childNodes.map((child, i) => {\n    const key = (child === null || child === void 0 ? void 0 : child.key) || `${prefixCls}-item-${i}`;\n    return /*#__PURE__*/React.createElement(CompactItem, {\n      key: key,\n      compactSize: mergedSize,\n      compactDirection: direction,\n      isFirstItem: i === 0 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isFirstItem)),\n      isLastItem: i === childNodes.length - 1 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isLastItem))\n    }, child);\n  }), [size, childNodes, compactItemContext]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: clx\n  }, restProps), nodes));\n};\nexport default Compact;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "toArray", "ConfigContext", "useSize", "useStyle", "SpaceCompactItemContext", "createContext", "useCompactItemContext", "prefixCls", "direction", "compactItemContext", "useContext", "compactItemClassnames", "useMemo", "compactDirection", "isFirstItem", "isLastItem", "separator", "compactSize", "NoCompactStyle", "props", "children", "createElement", "Provider", "value", "CompactItem", "others", "Compact", "getPrefixCls", "directionConfig", "size", "block", "customizePrefixCls", "className", "rootClassName", "restProps", "mergedSize", "ctx", "wrapCSSVar", "hashId", "clx", "childNodes", "nodes", "map", "child", "key", "assign"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/space/Compact.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { ConfigContext } from '../config-provider';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nexport const SpaceCompactItemContext = /*#__PURE__*/React.createContext(null);\nexport const useCompactItemContext = (prefixCls, direction) => {\n  const compactItemContext = React.useContext(SpaceCompactItemContext);\n  const compactItemClassnames = React.useMemo(() => {\n    if (!compactItemContext) {\n      return '';\n    }\n    const {\n      compactDirection,\n      isFirstItem,\n      isLastItem\n    } = compactItemContext;\n    const separator = compactDirection === 'vertical' ? '-vertical-' : '-';\n    return classNames(`${prefixCls}-compact${separator}item`, {\n      [`${prefixCls}-compact${separator}first-item`]: isFirstItem,\n      [`${prefixCls}-compact${separator}last-item`]: isLastItem,\n      [`${prefixCls}-compact${separator}item-rtl`]: direction === 'rtl'\n    });\n  }, [prefixCls, direction, compactItemContext]);\n  return {\n    compactSize: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactSize,\n    compactDirection: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactDirection,\n    compactItemClassnames\n  };\n};\nexport const NoCompactStyle = props => {\n  const {\n    children\n  } = props;\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: null\n  }, children);\n};\nconst CompactItem = props => {\n  const {\n      children\n    } = props,\n    others = __rest(props, [\"children\"]);\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: React.useMemo(() => others, [others])\n  }, children);\n};\nconst Compact = props => {\n  const {\n    getPrefixCls,\n    direction: directionConfig\n  } = React.useContext(ConfigContext);\n  const {\n      size,\n      direction,\n      block,\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      children\n    } = props,\n    restProps = __rest(props, [\"size\", \"direction\", \"block\", \"prefixCls\", \"className\", \"rootClassName\", \"children\"]);\n  const mergedSize = useSize(ctx => size !== null && size !== void 0 ? size : ctx);\n  const prefixCls = getPrefixCls('space-compact', customizePrefixCls);\n  const [wrapCSSVar, hashId] = useStyle(prefixCls);\n  const clx = classNames(prefixCls, hashId, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-block`]: block,\n    [`${prefixCls}-vertical`]: direction === 'vertical'\n  }, className, rootClassName);\n  const compactItemContext = React.useContext(SpaceCompactItemContext);\n  const childNodes = toArray(children);\n  const nodes = React.useMemo(() => childNodes.map((child, i) => {\n    const key = (child === null || child === void 0 ? void 0 : child.key) || `${prefixCls}-item-${i}`;\n    return /*#__PURE__*/React.createElement(CompactItem, {\n      key: key,\n      compactSize: mergedSize,\n      compactDirection: direction,\n      isFirstItem: i === 0 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isFirstItem)),\n      isLastItem: i === childNodes.length - 1 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isLastItem))\n    }, child);\n  }), [size, childNodes, compactItemContext]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: clx\n  }, restProps), nodes));\n};\nexport default Compact;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAO,MAAMC,uBAAuB,GAAG,aAAaN,KAAK,CAACO,aAAa,CAAC,IAAI,CAAC;AAC7E,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK;EAC7D,MAAMC,kBAAkB,GAAGX,KAAK,CAACY,UAAU,CAACN,uBAAuB,CAAC;EACpE,MAAMO,qBAAqB,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAM;IAChD,IAAI,CAACH,kBAAkB,EAAE;MACvB,OAAO,EAAE;IACX;IACA,MAAM;MACJI,gBAAgB;MAChBC,WAAW;MACXC;IACF,CAAC,GAAGN,kBAAkB;IACtB,MAAMO,SAAS,GAAGH,gBAAgB,KAAK,UAAU,GAAG,YAAY,GAAG,GAAG;IACtE,OAAOd,UAAU,CAAC,GAAGQ,SAAS,WAAWS,SAAS,MAAM,EAAE;MACxD,CAAC,GAAGT,SAAS,WAAWS,SAAS,YAAY,GAAGF,WAAW;MAC3D,CAAC,GAAGP,SAAS,WAAWS,SAAS,WAAW,GAAGD,UAAU;MACzD,CAAC,GAAGR,SAAS,WAAWS,SAAS,UAAU,GAAGR,SAAS,KAAK;IAC9D,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,SAAS,EAAEC,SAAS,EAAEC,kBAAkB,CAAC,CAAC;EAC9C,OAAO;IACLQ,WAAW,EAAER,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACQ,WAAW;IACnHJ,gBAAgB,EAAEJ,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACI,gBAAgB;IAC7HF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMO,cAAc,GAAGC,KAAK,IAAI;EACrC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO,aAAarB,KAAK,CAACuB,aAAa,CAACjB,uBAAuB,CAACkB,QAAQ,EAAE;IACxEC,KAAK,EAAE;EACT,CAAC,EAAEH,QAAQ,CAAC;AACd,CAAC;AACD,MAAMI,WAAW,GAAGL,KAAK,IAAI;EAC3B,MAAM;MACFC;IACF,CAAC,GAAGD,KAAK;IACTM,MAAM,GAAGzC,MAAM,CAACmC,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC;EACtC,OAAO,aAAarB,KAAK,CAACuB,aAAa,CAACjB,uBAAuB,CAACkB,QAAQ,EAAE;IACxEC,KAAK,EAAEzB,KAAK,CAACc,OAAO,CAAC,MAAMa,MAAM,EAAE,CAACA,MAAM,CAAC;EAC7C,CAAC,EAAEL,QAAQ,CAAC;AACd,CAAC;AACD,MAAMM,OAAO,GAAGP,KAAK,IAAI;EACvB,MAAM;IACJQ,YAAY;IACZnB,SAAS,EAAEoB;EACb,CAAC,GAAG9B,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;EACnC,MAAM;MACF4B,IAAI;MACJrB,SAAS;MACTsB,KAAK;MACLvB,SAAS,EAAEwB,kBAAkB;MAC7BC,SAAS;MACTC,aAAa;MACbb;IACF,CAAC,GAAGD,KAAK;IACTe,SAAS,GAAGlD,MAAM,CAACmC,KAAK,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EAClH,MAAMgB,UAAU,GAAGjC,OAAO,CAACkC,GAAG,IAAIP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGO,GAAG,CAAC;EAChF,MAAM7B,SAAS,GAAGoB,YAAY,CAAC,eAAe,EAAEI,kBAAkB,CAAC;EACnE,MAAM,CAACM,UAAU,EAAEC,MAAM,CAAC,GAAGnC,QAAQ,CAACI,SAAS,CAAC;EAChD,MAAMgC,GAAG,GAAGxC,UAAU,CAACQ,SAAS,EAAE+B,MAAM,EAAE;IACxC,CAAC,GAAG/B,SAAS,MAAM,GAAGqB,eAAe,KAAK,KAAK;IAC/C,CAAC,GAAGrB,SAAS,QAAQ,GAAGuB,KAAK;IAC7B,CAAC,GAAGvB,SAAS,WAAW,GAAGC,SAAS,KAAK;EAC3C,CAAC,EAAEwB,SAAS,EAAEC,aAAa,CAAC;EAC5B,MAAMxB,kBAAkB,GAAGX,KAAK,CAACY,UAAU,CAACN,uBAAuB,CAAC;EACpE,MAAMoC,UAAU,GAAGxC,OAAO,CAACoB,QAAQ,CAAC;EACpC,MAAMqB,KAAK,GAAG3C,KAAK,CAACc,OAAO,CAAC,MAAM4B,UAAU,CAACE,GAAG,CAAC,CAACC,KAAK,EAAEhD,CAAC,KAAK;IAC7D,MAAMiD,GAAG,GAAG,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,GAAG,KAAK,GAAGrC,SAAS,SAASZ,CAAC,EAAE;IACjG,OAAO,aAAaG,KAAK,CAACuB,aAAa,CAACG,WAAW,EAAE;MACnDoB,GAAG,EAAEA,GAAG;MACR3B,WAAW,EAAEkB,UAAU;MACvBtB,gBAAgB,EAAEL,SAAS;MAC3BM,WAAW,EAAEnB,CAAC,KAAK,CAAC,KAAK,CAACc,kBAAkB,KAAKA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACK,WAAW,CAAC,CAAC;MACzJC,UAAU,EAAEpB,CAAC,KAAK6C,UAAU,CAAC5C,MAAM,GAAG,CAAC,KAAK,CAACa,kBAAkB,KAAKA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACM,UAAU,CAAC;IAC5K,CAAC,EAAE4B,KAAK,CAAC;EACX,CAAC,CAAC,EAAE,CAACd,IAAI,EAAEW,UAAU,EAAE/B,kBAAkB,CAAC,CAAC;EAC3C;EACA,IAAI+B,UAAU,CAAC5C,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,OAAOyC,UAAU,CAAC,aAAavC,KAAK,CAACuB,aAAa,CAAC,KAAK,EAAEhC,MAAM,CAACwD,MAAM,CAAC;IACtEb,SAAS,EAAEO;EACb,CAAC,EAAEL,SAAS,CAAC,EAAEO,KAAK,CAAC,CAAC;AACxB,CAAC;AACD,eAAef,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}