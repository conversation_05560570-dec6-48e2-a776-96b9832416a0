{"ast": null, "code": "function mergeProps(...items) {\n  const ret = {};\n  items.forEach(item => {\n    if (item) {\n      Object.keys(item).forEach(key => {\n        if (item[key] !== undefined) {\n          ret[key] = item[key];\n        }\n      });\n    }\n  });\n  return ret;\n}\nexport default mergeProps;", "map": {"version": 3, "names": ["mergeProps", "items", "ret", "for<PERSON>ach", "item", "Object", "keys", "key", "undefined"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/extendsObject.js"], "sourcesContent": ["function mergeProps(...items) {\n  const ret = {};\n  items.forEach(item => {\n    if (item) {\n      Object.keys(item).forEach(key => {\n        if (item[key] !== undefined) {\n          ret[key] = item[key];\n        }\n      });\n    }\n  });\n  return ret;\n}\nexport default mergeProps;"], "mappings": "AAAA,SAASA,UAAUA,CAAC,GAAGC,KAAK,EAAE;EAC5B,MAAMC,GAAG,GAAG,CAAC,CAAC;EACdD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;IACpB,IAAIA,IAAI,EAAE;MACRC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACD,OAAO,CAACI,GAAG,IAAI;QAC/B,IAAIH,IAAI,CAACG,GAAG,CAAC,KAAKC,SAAS,EAAE;UAC3BN,GAAG,CAACK,GAAG,CAAC,GAAGH,IAAI,CAACG,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOL,GAAG;AACZ;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}