{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { SubMenu as RcSubMenu, useFullPath } from 'rc-menu';\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { cloneElement } from '../_util/reactNode';\nimport MenuContext from './MenuContext';\nconst SubMenu = props => {\n  var _a;\n  const {\n    popupClassName,\n    icon,\n    title,\n    theme: customTheme\n  } = props;\n  const context = React.useContext(MenuContext);\n  const {\n    prefixCls,\n    inlineCollapsed,\n    theme: contextTheme\n  } = context;\n  const parentPath = useFullPath();\n  let titleNode;\n  if (!icon) {\n    titleNode = inlineCollapsed && !parentPath.length && title && typeof title === 'string' ? (/*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-inline-collapsed-noicon`\n    }, title.charAt(0))) : (/*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-title-content`\n    }, title));\n  } else {\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    const titleIsSpan = /*#__PURE__*/React.isValidElement(title) && title.type === 'span';\n    titleNode = /*#__PURE__*/React.createElement(React.Fragment, null, cloneElement(icon, {\n      className: classNames(/*#__PURE__*/React.isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', `${prefixCls}-item-icon`)\n    }), titleIsSpan ? title : /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-title-content`\n    }, title));\n  }\n  const contextValue = React.useMemo(() => Object.assign(Object.assign({}, context), {\n    firstLevel: false\n  }), [context]);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('Menu');\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(RcSubMenu, Object.assign({}, omit(props, ['icon']), {\n    title: titleNode,\n    popupClassName: classNames(prefixCls, popupClassName, `${prefixCls}-${customTheme || contextTheme}`),\n    popupStyle: Object.assign({\n      zIndex\n    }, props.popupStyle)\n  })));\n};\nexport default SubMenu;", "map": {"version": 3, "names": ["React", "classNames", "SubMenu", "RcSubMenu", "useFullPath", "omit", "useZIndex", "cloneElement", "MenuContext", "props", "_a", "popupClassName", "icon", "title", "theme", "customTheme", "context", "useContext", "prefixCls", "inlineCollapsed", "contextTheme", "parentPath", "titleNode", "length", "createElement", "className", "char<PERSON>t", "titleIsSpan", "isValidElement", "type", "Fragment", "contextValue", "useMemo", "Object", "assign", "firstLevel", "zIndex", "Provider", "value", "popupStyle"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/menu/SubMenu.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { SubMenu as RcSubMenu, useFullPath } from 'rc-menu';\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { cloneElement } from '../_util/reactNode';\nimport MenuContext from './MenuContext';\nconst SubMenu = props => {\n  var _a;\n  const {\n    popupClassName,\n    icon,\n    title,\n    theme: customTheme\n  } = props;\n  const context = React.useContext(MenuContext);\n  const {\n    prefixCls,\n    inlineCollapsed,\n    theme: contextTheme\n  } = context;\n  const parentPath = useFullPath();\n  let titleNode;\n  if (!icon) {\n    titleNode = inlineCollapsed && !parentPath.length && title && typeof title === 'string' ? (/*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-inline-collapsed-noicon`\n    }, title.charAt(0))) : (/*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-title-content`\n    }, title));\n  } else {\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    const titleIsSpan = /*#__PURE__*/React.isValidElement(title) && title.type === 'span';\n    titleNode = /*#__PURE__*/React.createElement(React.Fragment, null, cloneElement(icon, {\n      className: classNames(/*#__PURE__*/React.isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', `${prefixCls}-item-icon`)\n    }), titleIsSpan ? title : /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-title-content`\n    }, title));\n  }\n  const contextValue = React.useMemo(() => Object.assign(Object.assign({}, context), {\n    firstLevel: false\n  }), [context]);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('Menu');\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(RcSubMenu, Object.assign({}, omit(props, ['icon']), {\n    title: titleNode,\n    popupClassName: classNames(prefixCls, popupClassName, `${prefixCls}-${customTheme || contextTheme}`),\n    popupStyle: Object.assign({\n      zIndex\n    }, props.popupStyle)\n  })));\n};\nexport default SubMenu;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,IAAIC,SAAS,EAAEC,WAAW,QAAQ,SAAS;AAC3D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,MAAMN,OAAO,GAAGO,KAAK,IAAI;EACvB,IAAIC,EAAE;EACN,MAAM;IACJC,cAAc;IACdC,IAAI;IACJC,KAAK;IACLC,KAAK,EAAEC;EACT,CAAC,GAAGN,KAAK;EACT,MAAMO,OAAO,GAAGhB,KAAK,CAACiB,UAAU,CAACT,WAAW,CAAC;EAC7C,MAAM;IACJU,SAAS;IACTC,eAAe;IACfL,KAAK,EAAEM;EACT,CAAC,GAAGJ,OAAO;EACX,MAAMK,UAAU,GAAGjB,WAAW,CAAC,CAAC;EAChC,IAAIkB,SAAS;EACb,IAAI,CAACV,IAAI,EAAE;IACTU,SAAS,GAAGH,eAAe,IAAI,CAACE,UAAU,CAACE,MAAM,IAAIV,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,aAAab,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;MACjIC,SAAS,EAAE,GAAGP,SAAS;IACzB,CAAC,EAAEL,KAAK,CAACa,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,aAAa1B,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;MAC/DC,SAAS,EAAE,GAAGP,SAAS;IACzB,CAAC,EAAEL,KAAK,CAAC,CAAC;EACZ,CAAC,MAAM;IACL;IACA;IACA,MAAMc,WAAW,GAAG,aAAa3B,KAAK,CAAC4B,cAAc,CAACf,KAAK,CAAC,IAAIA,KAAK,CAACgB,IAAI,KAAK,MAAM;IACrFP,SAAS,GAAG,aAAatB,KAAK,CAACwB,aAAa,CAACxB,KAAK,CAAC8B,QAAQ,EAAE,IAAI,EAAEvB,YAAY,CAACK,IAAI,EAAE;MACpFa,SAAS,EAAExB,UAAU,CAAC,aAAaD,KAAK,CAAC4B,cAAc,CAAChB,IAAI,CAAC,GAAG,CAACF,EAAE,GAAGE,IAAI,CAACH,KAAK,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,SAAS,GAAG,EAAE,EAAE,GAAGP,SAAS,YAAY;IACpK,CAAC,CAAC,EAAES,WAAW,GAAGd,KAAK,GAAG,aAAab,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;MACjEC,SAAS,EAAE,GAAGP,SAAS;IACzB,CAAC,EAAEL,KAAK,CAAC,CAAC;EACZ;EACA,MAAMkB,YAAY,GAAG/B,KAAK,CAACgC,OAAO,CAAC,MAAMC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,OAAO,CAAC,EAAE;IACjFmB,UAAU,EAAE;EACd,CAAC,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EACd;EACA,MAAM,CAACoB,MAAM,CAAC,GAAG9B,SAAS,CAAC,MAAM,CAAC;EAClC,OAAO,aAAaN,KAAK,CAACwB,aAAa,CAAChB,WAAW,CAAC6B,QAAQ,EAAE;IAC5DC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAa/B,KAAK,CAACwB,aAAa,CAACrB,SAAS,EAAE8B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7B,IAAI,CAACI,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;IACtFI,KAAK,EAAES,SAAS;IAChBX,cAAc,EAAEV,UAAU,CAACiB,SAAS,EAAEP,cAAc,EAAE,GAAGO,SAAS,IAAIH,WAAW,IAAIK,YAAY,EAAE,CAAC;IACpGmB,UAAU,EAAEN,MAAM,CAACC,MAAM,CAAC;MACxBE;IACF,CAAC,EAAE3B,KAAK,CAAC8B,UAAU;EACrB,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAerC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}