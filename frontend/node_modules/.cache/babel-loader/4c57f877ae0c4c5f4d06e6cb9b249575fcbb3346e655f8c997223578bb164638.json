{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport initCollapseMotion from '../_util/motion';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nfunction toErrorEntity(error, prefix, errorStatus, index = 0) {\n  return {\n    key: typeof error === 'string' ? error : `${prefix}-${index}`,\n    error,\n    errorStatus\n  };\n}\nconst ErrorList = ({\n  help,\n  helpStatus,\n  errors = EMPTY_LIST,\n  warnings = EMPTY_LIST,\n  className: rootClassName,\n  fieldId,\n  onVisibleChanged\n}) => {\n  const {\n    prefixCls\n  } = React.useContext(FormItemPrefixContext);\n  const baseClassName = `${prefixCls}-item-explain`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const collapseMotion = React.useMemo(() => initCollapseMotion(prefixCls), [prefixCls]);\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  const debounceErrors = useDebounce(errors);\n  const debounceWarnings = useDebounce(warnings);\n  const fullKeyList = React.useMemo(() => {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, 'help', helpStatus)];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map((error, index) => toErrorEntity(error, 'error', 'error', index))), _toConsumableArray(debounceWarnings.map((warning, index) => toErrorEntity(warning, 'warning', 'warning', index))));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  const filledKeyFullKeyList = React.useMemo(() => {\n    const keysCount = {};\n    fullKeyList.forEach(({\n      key\n    }) => {\n      keysCount[key] = (keysCount[key] || 0) + 1;\n    });\n    return fullKeyList.map((entity, index) => Object.assign(Object.assign({}, entity), {\n      key: keysCount[entity.key] > 1 ? `${entity.key}-fallback-${index}` : entity.key\n    }));\n  }, [fullKeyList]);\n  const helpProps = {};\n  if (fieldId) {\n    helpProps.id = `${fieldId}_help`;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: `${prefixCls}-show-help`,\n    visible: !!filledKeyFullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, holderProps => {\n    const {\n      className: holderClassName,\n      style: holderStyle\n    } = holderProps;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, cssVarCls, rootCls, rootClassName, hashId),\n      style: holderStyle\n    }), /*#__PURE__*/React.createElement(CSSMotionList, Object.assign({\n      keys: filledKeyFullKeyList\n    }, initCollapseMotion(prefixCls), {\n      motionName: `${prefixCls}-show-help-item`,\n      component: false\n    }), itemProps => {\n      const {\n        key,\n        error,\n        errorStatus,\n        className: itemClassName,\n        style: itemStyle\n      } = itemProps;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, {\n          [`${baseClassName}-${errorStatus}`]: errorStatus\n        }),\n        style: itemStyle\n      }, error);\n    }));\n  }));\n};\nexport default ErrorList;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "CSSMotion", "CSSMotionList", "initCollapseMotion", "useCSSVarCls", "FormItemPrefixContext", "useDebounce", "useStyle", "EMPTY_LIST", "toErrorEntity", "error", "prefix", "errorStatus", "index", "key", "ErrorList", "help", "helpStatus", "errors", "warnings", "className", "rootClassName", "fieldId", "onVisibleChanged", "prefixCls", "useContext", "baseClassName", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "collapseMotion", "useMemo", "debounceErrors", "debounce<PERSON><PERSON><PERSON>s", "fullKeyList", "undefined", "concat", "map", "warning", "filledKeyFullKeyList", "keysCount", "for<PERSON>ach", "entity", "Object", "assign", "helpProps", "id", "createElement", "motionDeadline", "motionName", "visible", "length", "holderProps", "holderClassName", "style", "holder<PERSON>tyle", "keys", "component", "itemProps", "itemClassName", "itemStyle"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/form/ErrorList.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport initCollapseMotion from '../_util/motion';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nfunction toErrorEntity(error, prefix, errorStatus, index = 0) {\n  return {\n    key: typeof error === 'string' ? error : `${prefix}-${index}`,\n    error,\n    errorStatus\n  };\n}\nconst ErrorList = ({\n  help,\n  helpStatus,\n  errors = EMPTY_LIST,\n  warnings = EMPTY_LIST,\n  className: rootClassName,\n  fieldId,\n  onVisibleChanged\n}) => {\n  const {\n    prefixCls\n  } = React.useContext(FormItemPrefixContext);\n  const baseClassName = `${prefixCls}-item-explain`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const collapseMotion = React.useMemo(() => initCollapseMotion(prefixCls), [prefixCls]);\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  const debounceErrors = useDebounce(errors);\n  const debounceWarnings = useDebounce(warnings);\n  const fullKeyList = React.useMemo(() => {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, 'help', helpStatus)];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map((error, index) => toErrorEntity(error, 'error', 'error', index))), _toConsumableArray(debounceWarnings.map((warning, index) => toErrorEntity(warning, 'warning', 'warning', index))));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  const filledKeyFullKeyList = React.useMemo(() => {\n    const keysCount = {};\n    fullKeyList.forEach(({\n      key\n    }) => {\n      keysCount[key] = (keysCount[key] || 0) + 1;\n    });\n    return fullKeyList.map((entity, index) => Object.assign(Object.assign({}, entity), {\n      key: keysCount[entity.key] > 1 ? `${entity.key}-fallback-${index}` : entity.key\n    }));\n  }, [fullKeyList]);\n  const helpProps = {};\n  if (fieldId) {\n    helpProps.id = `${fieldId}_help`;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: `${prefixCls}-show-help`,\n    visible: !!filledKeyFullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, holderProps => {\n    const {\n      className: holderClassName,\n      style: holderStyle\n    } = holderProps;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, cssVarCls, rootCls, rootClassName, hashId),\n      style: holderStyle\n    }), /*#__PURE__*/React.createElement(CSSMotionList, Object.assign({\n      keys: filledKeyFullKeyList\n    }, initCollapseMotion(prefixCls), {\n      motionName: `${prefixCls}-show-help-item`,\n      component: false\n    }), itemProps => {\n      const {\n        key,\n        error,\n        errorStatus,\n        className: itemClassName,\n        style: itemStyle\n      } = itemProps;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, {\n          [`${baseClassName}-${errorStatus}`]: errorStatus\n        }),\n        style: itemStyle\n      }, error);\n    }));\n  }));\n};\nexport default ErrorList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,IAAIC,aAAa,QAAQ,WAAW;AACpD,OAAOC,kBAAkB,MAAM,iBAAiB;AAChD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,SAASC,qBAAqB,QAAQ,WAAW;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC5D,OAAO;IACLC,GAAG,EAAE,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,GAAGC,MAAM,IAAIE,KAAK,EAAE;IAC7DH,KAAK;IACLE;EACF,CAAC;AACH;AACA,MAAMG,SAAS,GAAGA,CAAC;EACjBC,IAAI;EACJC,UAAU;EACVC,MAAM,GAAGV,UAAU;EACnBW,QAAQ,GAAGX,UAAU;EACrBY,SAAS,EAAEC,aAAa;EACxBC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC;EACF,CAAC,GAAGzB,KAAK,CAAC0B,UAAU,CAACpB,qBAAqB,CAAC;EAC3C,MAAMqB,aAAa,GAAG,GAAGF,SAAS,eAAe;EACjD,MAAMG,OAAO,GAAGvB,YAAY,CAACoB,SAAS,CAAC;EACvC,MAAM,CAACI,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAACiB,SAAS,EAAEG,OAAO,CAAC;EACpE,MAAMI,cAAc,GAAGhC,KAAK,CAACiC,OAAO,CAAC,MAAM7B,kBAAkB,CAACqB,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACtF;EACA;EACA,MAAMS,cAAc,GAAG3B,WAAW,CAACY,MAAM,CAAC;EAC1C,MAAMgB,gBAAgB,GAAG5B,WAAW,CAACa,QAAQ,CAAC;EAC9C,MAAMgB,WAAW,GAAGpC,KAAK,CAACiC,OAAO,CAAC,MAAM;IACtC,IAAIhB,IAAI,KAAKoB,SAAS,IAAIpB,IAAI,KAAK,IAAI,EAAE;MACvC,OAAO,CAACP,aAAa,CAACO,IAAI,EAAE,MAAM,EAAEC,UAAU,CAAC,CAAC;IAClD;IACA,OAAO,EAAE,CAACoB,MAAM,CAACvC,kBAAkB,CAACmC,cAAc,CAACK,GAAG,CAAC,CAAC5B,KAAK,EAAEG,KAAK,KAAKJ,aAAa,CAACC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAEG,KAAK,CAAC,CAAC,CAAC,EAAEf,kBAAkB,CAACoC,gBAAgB,CAACI,GAAG,CAAC,CAACC,OAAO,EAAE1B,KAAK,KAAKJ,aAAa,CAAC8B,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE1B,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9O,CAAC,EAAE,CAACG,IAAI,EAAEC,UAAU,EAAEgB,cAAc,EAAEC,gBAAgB,CAAC,CAAC;EACxD,MAAMM,oBAAoB,GAAGzC,KAAK,CAACiC,OAAO,CAAC,MAAM;IAC/C,MAAMS,SAAS,GAAG,CAAC,CAAC;IACpBN,WAAW,CAACO,OAAO,CAAC,CAAC;MACnB5B;IACF,CAAC,KAAK;MACJ2B,SAAS,CAAC3B,GAAG,CAAC,GAAG,CAAC2B,SAAS,CAAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC,CAAC;IACF,OAAOqB,WAAW,CAACG,GAAG,CAAC,CAACK,MAAM,EAAE9B,KAAK,KAAK+B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAE;MACjF7B,GAAG,EAAE2B,SAAS,CAACE,MAAM,CAAC7B,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG6B,MAAM,CAAC7B,GAAG,aAAaD,KAAK,EAAE,GAAG8B,MAAM,CAAC7B;IAC9E,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACqB,WAAW,CAAC,CAAC;EACjB,MAAMW,SAAS,GAAG,CAAC,CAAC;EACpB,IAAIxB,OAAO,EAAE;IACXwB,SAAS,CAACC,EAAE,GAAG,GAAGzB,OAAO,OAAO;EAClC;EACA,OAAOM,UAAU,CAAC,aAAa7B,KAAK,CAACiD,aAAa,CAAC/C,SAAS,EAAE;IAC5DgD,cAAc,EAAElB,cAAc,CAACkB,cAAc;IAC7CC,UAAU,EAAE,GAAG1B,SAAS,YAAY;IACpC2B,OAAO,EAAE,CAAC,CAACX,oBAAoB,CAACY,MAAM;IACtC7B,gBAAgB,EAAEA;EACpB,CAAC,EAAE8B,WAAW,IAAI;IAChB,MAAM;MACJjC,SAAS,EAAEkC,eAAe;MAC1BC,KAAK,EAAEC;IACT,CAAC,GAAGH,WAAW;IACf,OAAO,aAAatD,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAE;MAC1E1B,SAAS,EAAEpB,UAAU,CAAC0B,aAAa,EAAE4B,eAAe,EAAExB,SAAS,EAAEH,OAAO,EAAEN,aAAa,EAAEQ,MAAM,CAAC;MAChG0B,KAAK,EAAEC;IACT,CAAC,CAAC,EAAE,aAAazD,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE0C,MAAM,CAACC,MAAM,CAAC;MAChEY,IAAI,EAAEjB;IACR,CAAC,EAAErC,kBAAkB,CAACqB,SAAS,CAAC,EAAE;MAChC0B,UAAU,EAAE,GAAG1B,SAAS,iBAAiB;MACzCkC,SAAS,EAAE;IACb,CAAC,CAAC,EAAEC,SAAS,IAAI;MACf,MAAM;QACJ7C,GAAG;QACHJ,KAAK;QACLE,WAAW;QACXQ,SAAS,EAAEwC,aAAa;QACxBL,KAAK,EAAEM;MACT,CAAC,GAAGF,SAAS;MACb,OAAO,aAAa5D,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;QAC7ClC,GAAG,EAAEA,GAAG;QACRM,SAAS,EAAEpB,UAAU,CAAC4D,aAAa,EAAE;UACnC,CAAC,GAAGlC,aAAa,IAAId,WAAW,EAAE,GAAGA;QACvC,CAAC,CAAC;QACF2C,KAAK,EAAEM;MACT,CAAC,EAAEnD,KAAK,CAAC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}