{"ast": null, "code": "const getBuiltInPlacements = popupOverflow => {\n  const htmlRegion = popupOverflow === 'scroll' ? 'scroll' : 'visible';\n  const sharedConfig = {\n    overflow: {\n      adjustX: true,\n      adjustY: true,\n      shiftY: true\n    },\n    htmlRegion,\n    dynamicInset: true\n  };\n  return {\n    bottomLeft: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['tl', 'bl'],\n      offset: [0, 4]\n    }),\n    bottomRight: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['tr', 'br'],\n      offset: [0, 4]\n    }),\n    topLeft: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['bl', 'tl'],\n      offset: [0, -4]\n    }),\n    topRight: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['br', 'tr'],\n      offset: [0, -4]\n    })\n  };\n};\nfunction mergedBuiltinPlacements(buildInPlacements, popupOverflow) {\n  return buildInPlacements || getBuiltInPlacements(popupOverflow);\n}\nexport default mergedBuiltinPlacements;", "map": {"version": 3, "names": ["getBuiltInPlacements", "popupOverflow", "htmlRegion", "sharedConfig", "overflow", "adjustX", "adjustY", "shiftY", "dynamicInset", "bottomLeft", "Object", "assign", "points", "offset", "bottomRight", "topLeft", "topRight", "mergedBuiltinPlacements", "buildInPlacements"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/select/mergedBuiltinPlacements.js"], "sourcesContent": ["const getBuiltInPlacements = popupOverflow => {\n  const htmlRegion = popupOverflow === 'scroll' ? 'scroll' : 'visible';\n  const sharedConfig = {\n    overflow: {\n      adjustX: true,\n      adjustY: true,\n      shiftY: true\n    },\n    htmlRegion,\n    dynamicInset: true\n  };\n  return {\n    bottomLeft: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['tl', 'bl'],\n      offset: [0, 4]\n    }),\n    bottomRight: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['tr', 'br'],\n      offset: [0, 4]\n    }),\n    topLeft: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['bl', 'tl'],\n      offset: [0, -4]\n    }),\n    topRight: Object.assign(Object.assign({}, sharedConfig), {\n      points: ['br', 'tr'],\n      offset: [0, -4]\n    })\n  };\n};\nfunction mergedBuiltinPlacements(buildInPlacements, popupOverflow) {\n  return buildInPlacements || getBuiltInPlacements(popupOverflow);\n}\nexport default mergedBuiltinPlacements;"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,aAAa,IAAI;EAC5C,MAAMC,UAAU,GAAGD,aAAa,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;EACpE,MAAME,YAAY,GAAG;IACnBC,QAAQ,EAAE;MACRC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;IACV,CAAC;IACDL,UAAU;IACVM,YAAY,EAAE;EAChB,CAAC;EACD,OAAO;IACLC,UAAU,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,YAAY,CAAC,EAAE;MACzDS,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACFC,WAAW,EAAEJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,YAAY,CAAC,EAAE;MAC1DS,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACFE,OAAO,EAAEL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,YAAY,CAAC,EAAE;MACtDS,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC;IACFG,QAAQ,EAAEN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,YAAY,CAAC,EAAE;MACvDS,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;AACH,CAAC;AACD,SAASI,uBAAuBA,CAACC,iBAAiB,EAAEjB,aAAa,EAAE;EACjE,OAAOiB,iBAAiB,IAAIlB,oBAAoB,CAACC,aAAa,CAAC;AACjE;AACA,eAAegB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}