{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-picker/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../../time-picker/locale/zh_CN\"));\n// 统一合并为完整的 Locale\nconst locale = {\n  lang: Object.assign({\n    placeholder: '请选择日期',\n    yearPlaceholder: '请选择年份',\n    quarterPlaceholder: '请选择季度',\n    monthPlaceholder: '请选择月份',\n    weekPlaceholder: '请选择周',\n    rangePlaceholder: ['开始日期', '结束日期'],\n    rangeYearPlaceholder: ['开始年份', '结束年份'],\n    rangeMonthPlaceholder: ['开始月份', '结束月份'],\n    rangeQuarterPlaceholder: ['开始季度', '结束季度'],\n    rangeWeekPlaceholder: ['开始周', '结束周']\n  }, _zh_CN.default),\n  timePickerLocale: Object.assign({}, _zh_CN2.default)\n};\n// should add whitespace between char in Button\nlocale.lang.ok = '确定';\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nvar _default = exports.default = locale;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "_zh_CN", "_zh_CN2", "locale", "lang", "assign", "placeholder", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "rangePlaceholder", "rangeYearPlaceholder", "rangeMonthPlaceholder", "rangeQuarterPlaceholder", "rangeWeekPlaceholder", "timePickerLocale", "ok", "_default"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/lib/date-picker/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-picker/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../../time-picker/locale/zh_CN\"));\n// 统一合并为完整的 Locale\nconst locale = {\n  lang: Object.assign({\n    placeholder: '请选择日期',\n    yearPlaceholder: '请选择年份',\n    quarterPlaceholder: '请选择季度',\n    monthPlaceholder: '请选择月份',\n    weekPlaceholder: '请选择周',\n    rangePlaceholder: ['开始日期', '结束日期'],\n    rangeYearPlaceholder: ['开始年份', '结束年份'],\n    rangeMonthPlaceholder: ['开始月份', '结束月份'],\n    rangeQuarterPlaceholder: ['开始季度', '结束季度'],\n    rangeWeekPlaceholder: ['开始周', '结束周']\n  }, _zh_CN.default),\n  timePickerLocale: Object.assign({}, _zh_CN2.default)\n};\n// should add whitespace between char in Button\nlocale.lang.ok = '确定';\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nvar _default = exports.default = locale;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIK,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAC1E,IAAIO,OAAO,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAC/E;AACA,MAAMQ,MAAM,GAAG;EACbC,IAAI,EAAEP,MAAM,CAACQ,MAAM,CAAC;IAClBC,WAAW,EAAE,OAAO;IACpBC,eAAe,EAAE,OAAO;IACxBC,kBAAkB,EAAE,OAAO;IAC3BC,gBAAgB,EAAE,OAAO;IACzBC,eAAe,EAAE,MAAM;IACvBC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAClCC,oBAAoB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACtCC,qBAAqB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACvCC,uBAAuB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACzCC,oBAAoB,EAAE,CAAC,KAAK,EAAE,KAAK;EACrC,CAAC,EAAEd,MAAM,CAACL,OAAO,CAAC;EAClBoB,gBAAgB,EAAEnB,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACN,OAAO;AACrD,CAAC;AACD;AACAO,MAAM,CAACC,IAAI,CAACa,EAAE,GAAG,IAAI;AACrB;AACA;AACA,IAAIC,QAAQ,GAAGnB,OAAO,CAACH,OAAO,GAAGO,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}