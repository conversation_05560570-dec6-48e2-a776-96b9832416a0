{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport defaultLocale from '../../locale/en_US';\nimport extendsObject from '../extendsObject';\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection, fallbackCloseCollection = EmptyFallbackCloseCollection) {\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const [contextLocale] = useLocale('global', defaultLocale.global);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled, {}];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    // Wrap the closeIcon with aria props\n    const ariaOrDataProps = pickAttrs(mergedClosableConfig, true);\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, Object.assign({\n        'aria-label': contextLocale.close\n      }, ariaOrDataProps))) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({\n        \"aria-label\": contextLocale.close\n      }, ariaOrDataProps), mergedCloseIcon));\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled, ariaOrDataProps];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}", "map": {"version": 3, "names": ["React", "CloseOutlined", "pickAttrs", "useLocale", "defaultLocale", "extendsObject", "pickClosable", "context", "undefined", "closable", "closeIcon", "useClosableConfig", "closableCollection", "useMemo", "closableConfig", "Object", "assign", "EmptyFallbackCloseCollection", "useClosable", "propCloseCollection", "contextCloseCollection", "fallbackCloseCollection", "propCloseConfig", "contextCloseConfig", "contextLocale", "global", "closeBtnIsDisabled", "disabled", "mergedFallbackCloseCollection", "createElement", "mergedClosableConfig", "closeIconRender", "mergedCloseIcon", "ariaOrDataProps", "isValidElement", "cloneElement", "close"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/hooks/useClosable.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport defaultLocale from '../../locale/en_US';\nimport extendsObject from '../extendsObject';\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection, fallbackCloseCollection = EmptyFallbackCloseCollection) {\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const [contextLocale] = useLocale('global', defaultLocale.global);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled, {}];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    // Wrap the closeIcon with aria props\n    const ariaOrDataProps = pickAttrs(mergedClosableConfig, true);\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, Object.assign({\n        'aria-label': contextLocale.close\n      }, ariaOrDataProps))) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({\n        \"aria-label\": contextLocale.close\n      }, ariaOrDataProps), mergedCloseIcon));\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled, ariaOrDataProps];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOC,SAAS;EAClB;EACA,OAAO;IACLC,QAAQ,EAAEF,OAAO,CAACE,QAAQ;IAC1BC,SAAS,EAAEH,OAAO,CAACG;EACrB,CAAC;AACH;AACA;AACA,SAASC,iBAAiBA,CAACC,kBAAkB,EAAE;EAC7C,MAAM;IACJH,QAAQ;IACRC;EACF,CAAC,GAAGE,kBAAkB,IAAI,CAAC,CAAC;EAC5B,OAAOZ,KAAK,CAACa,OAAO,CAAC,MAAM;IACzB;IACA;IACA,CAACJ,QAAQ,KAAKA,QAAQ,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,IAAI,CAAC,EAAE;MAC9E,OAAO,KAAK;IACd;IACA,IAAID,QAAQ,KAAKD,SAAS,IAAIE,SAAS,KAAKF,SAAS,EAAE;MACrD,OAAO,IAAI;IACb;IACA,IAAIM,cAAc,GAAG;MACnBJ,SAAS,EAAE,OAAOA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAGF;IAChF,CAAC;IACD,IAAIC,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5CK,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,cAAc,CAAC,EAAEL,QAAQ,CAAC;IAC7E;IACA,OAAOK,cAAc;EACvB,CAAC,EAAE,CAACL,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAC3B;AACA;AACA,MAAMO,4BAA4B,GAAG,CAAC,CAAC;AACvC,eAAe,SAASC,WAAWA,CAACC,mBAAmB,EAAEC,sBAAsB,EAAEC,uBAAuB,GAAGJ,4BAA4B,EAAE;EACvI;EACA,MAAMK,eAAe,GAAGX,iBAAiB,CAACQ,mBAAmB,CAAC;EAC9D,MAAMI,kBAAkB,GAAGZ,iBAAiB,CAACS,sBAAsB,CAAC;EACpE,MAAM,CAACI,aAAa,CAAC,GAAGrB,SAAS,CAAC,QAAQ,EAAEC,aAAa,CAACqB,MAAM,CAAC;EACjE,MAAMC,kBAAkB,GAAG,OAAOJ,eAAe,KAAK,SAAS,GAAG,CAAC,EAAEA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACK,QAAQ,CAAC,GAAG,KAAK;EACxK,MAAMC,6BAA6B,GAAG5B,KAAK,CAACa,OAAO,CAAC,MAAME,MAAM,CAACC,MAAM,CAAC;IACtEN,SAAS,EAAE,aAAaV,KAAK,CAAC6B,aAAa,CAAC5B,aAAa,EAAE,IAAI;EACjE,CAAC,EAAEoB,uBAAuB,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;EACvD;EACA,MAAMS,oBAAoB,GAAG9B,KAAK,CAACa,OAAO,CAAC,MAAM;IAC/C;IACA;IACA,IAAIS,eAAe,KAAK,KAAK,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,IAAIA,eAAe,EAAE;MACnB,OAAOjB,aAAa,CAACuB,6BAA6B,EAAEL,kBAAkB,EAAED,eAAe,CAAC;IAC1F;IACA;IACA;IACA,IAAIC,kBAAkB,KAAK,KAAK,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAIA,kBAAkB,EAAE;MACtB,OAAOlB,aAAa,CAACuB,6BAA6B,EAAEL,kBAAkB,CAAC;IACzE;IACA;IACA,OAAO,CAACK,6BAA6B,CAACnB,QAAQ,GAAG,KAAK,GAAGmB,6BAA6B;EACxF,CAAC,EAAE,CAACN,eAAe,EAAEC,kBAAkB,EAAEK,6BAA6B,CAAC,CAAC;EACxE;EACA,OAAO5B,KAAK,CAACa,OAAO,CAAC,MAAM;IACzB,IAAIiB,oBAAoB,KAAK,KAAK,EAAE;MAClC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAEJ,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,MAAM;MACJK;IACF,CAAC,GAAGH,6BAA6B;IACjC,MAAM;MACJlB;IACF,CAAC,GAAGoB,oBAAoB;IACxB,IAAIE,eAAe,GAAGtB,SAAS;IAC/B;IACA,MAAMuB,eAAe,GAAG/B,SAAS,CAAC4B,oBAAoB,EAAE,IAAI,CAAC;IAC7D,IAAIE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKxB,SAAS,EAAE;MAC7D;MACA,IAAIuB,eAAe,EAAE;QACnBC,eAAe,GAAGD,eAAe,CAACrB,SAAS,CAAC;MAC9C;MACAsB,eAAe,GAAG,aAAahC,KAAK,CAACkC,cAAc,CAACF,eAAe,CAAC,IAAI,aAAahC,KAAK,CAACmC,YAAY,CAACH,eAAe,EAAEjB,MAAM,CAACC,MAAM,CAAC;QACrI,YAAY,EAAEQ,aAAa,CAACY;MAC9B,CAAC,EAAEH,eAAe,CAAC,CAAC,KAAK,aAAajC,KAAK,CAAC6B,aAAa,CAAC,MAAM,EAAEd,MAAM,CAACC,MAAM,CAAC;QAC9E,YAAY,EAAEQ,aAAa,CAACY;MAC9B,CAAC,EAAEH,eAAe,CAAC,EAAED,eAAe,CAAC,CAAC;IACxC;IACA,OAAO,CAAC,IAAI,EAAEA,eAAe,EAAEN,kBAAkB,EAAEO,eAAe,CAAC;EACrE,CAAC,EAAE,CAACH,oBAAoB,EAAEF,6BAA6B,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}