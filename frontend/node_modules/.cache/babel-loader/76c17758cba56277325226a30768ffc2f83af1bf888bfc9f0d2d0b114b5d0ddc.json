{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color, includeInverse = true) {\n  if (includeInverse) {\n    return [].concat(_toConsumableArray(inverseColors), _toConsumableArray(PresetColors)).includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "PresetColors", "inverseColors", "map", "color", "PresetStatusColorTypes", "isPresetColor", "includeInverse", "concat", "includes", "isPresetStatusColor"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/colors.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color, includeInverse = true) {\n  if (includeInverse) {\n    return [].concat(_toConsumableArray(inverseColors), _toConsumableArray(PresetColors)).includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,YAAY,QAAQ,oBAAoB;AACjD,MAAMC,aAAa,GAAGD,YAAY,CAACE,GAAG,CAACC,KAAK,IAAI,GAAGA,KAAK,UAAU,CAAC;AACnE,OAAO,MAAMC,sBAAsB,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACF,KAAK,EAAEG,cAAc,GAAG,IAAI,EAAE;EAC1D,IAAIA,cAAc,EAAE;IAClB,OAAO,EAAE,CAACC,MAAM,CAACR,kBAAkB,CAACE,aAAa,CAAC,EAAEF,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAACQ,QAAQ,CAACL,KAAK,CAAC;EACvG;EACA,OAAOH,YAAY,CAACQ,QAAQ,CAACL,KAAK,CAAC;AACrC;AACA,OAAO,SAASM,mBAAmBA,CAACN,KAAK,EAAE;EACzC,OAAOC,sBAAsB,CAACI,QAAQ,CAACL,KAAK,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}