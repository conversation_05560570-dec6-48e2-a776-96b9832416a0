{"ast": null, "code": "const toList = (candidate, skipEmpty = false) => {\n  if (skipEmpty && (candidate === undefined || candidate === null)) {\n    return [];\n  }\n  return Array.isArray(candidate) ? candidate : [candidate];\n};\nexport default toList;", "map": {"version": 3, "names": ["toList", "candidate", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Array", "isArray"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/_util/toList.js"], "sourcesContent": ["const toList = (candidate, skipEmpty = false) => {\n  if (skipEmpty && (candidate === undefined || candidate === null)) {\n    return [];\n  }\n  return Array.isArray(candidate) ? candidate : [candidate];\n};\nexport default toList;"], "mappings": "AAAA,MAAMA,MAAM,GAAGA,CAACC,SAAS,EAAEC,SAAS,GAAG,KAAK,KAAK;EAC/C,IAAIA,SAAS,KAAKD,SAAS,KAAKE,SAAS,IAAIF,SAAS,KAAK,IAAI,CAAC,EAAE;IAChE,OAAO,EAAE;EACX;EACA,OAAOG,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC3D,CAAC;AACD,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}