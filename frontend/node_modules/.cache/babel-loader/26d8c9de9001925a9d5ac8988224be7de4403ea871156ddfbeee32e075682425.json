{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport { ConfigContext } from '../config-provider';\nconst MenuDivider = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      dashed\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"dashed\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('menu', customizePrefixCls);\n  const classString = classNames({\n    [`${prefixCls}-item-divider-dashed`]: !!dashed\n  }, className);\n  return /*#__PURE__*/React.createElement(Divider, Object.assign({\n    className: classString\n  }, restProps));\n};\nexport default MenuDivider;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "Divider", "ConfigContext", "MenuDivider", "props", "prefixCls", "customizePrefixCls", "className", "dashed", "restProps", "getPrefixCls", "useContext", "classString", "createElement", "assign"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/menu/MenuDivider.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport { ConfigContext } from '../config-provider';\nconst MenuDivider = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      dashed\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"dashed\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('menu', customizePrefixCls);\n  const classString = classNames({\n    [`${prefixCls}-item-divider-dashed`]: !!dashed\n  }, className);\n  return /*#__PURE__*/React.createElement(Divider, Object.assign({\n    className: classString\n  }, restProps));\n};\nexport default MenuDivider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC;IACF,CAAC,GAAGJ,KAAK;IACTK,SAAS,GAAGxB,MAAM,CAACmB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;EACjE,MAAM;IACJM;EACF,CAAC,GAAGX,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;EACnC,MAAMG,SAAS,GAAGK,YAAY,CAAC,MAAM,EAAEJ,kBAAkB,CAAC;EAC1D,MAAMM,WAAW,GAAGZ,UAAU,CAAC;IAC7B,CAAC,GAAGK,SAAS,sBAAsB,GAAG,CAAC,CAACG;EAC1C,CAAC,EAAED,SAAS,CAAC;EACb,OAAO,aAAaR,KAAK,CAACc,aAAa,CAACZ,OAAO,EAAEX,MAAM,CAACwB,MAAM,CAAC;IAC7DP,SAAS,EAAEK;EACb,CAAC,EAAEH,SAAS,CAAC,CAAC;AAChB,CAAC;AACD,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}