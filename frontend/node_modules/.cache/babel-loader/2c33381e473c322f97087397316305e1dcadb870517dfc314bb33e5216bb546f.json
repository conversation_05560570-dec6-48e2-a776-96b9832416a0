{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { FastColor } from '@ant-design/fast-color';\nimport { useLocale } from '../locale';\nimport { useToken } from '../theme/internal';\nconst Empty = () => {\n  const [, token] = useToken();\n  const [locale] = useLocale('Empty');\n  const bgColor = new FastColor(token.colorBgBase);\n  // Dark Theme need more dark of this\n  const themeStyle = bgColor.toHsl().l < 0.5 ? {\n    opacity: 0.65\n  } : {};\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    style: themeStyle,\n    width: \"184\",\n    height: \"152\",\n    viewBox: \"0 0 184 152\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"title\", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(24 31.67)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    fillOpacity: \".8\",\n    fill: \"#F5F5F7\",\n    cx: \"67.797\",\n    cy: \"106.89\",\n    rx: \"67.797\",\n    ry: \"12.668\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\",\n    fill: \"#AEB8C2\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\",\n    fill: \"url(#linearGradient-1)\",\n    transform: \"translate(13.56)\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\",\n    fill: \"#F5F5F7\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\",\n    fill: \"#DCE0E6\"\n  })), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\",\n    fill: \"#DCE0E6\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(149.65 15.383)\",\n    fill: \"#FFF\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    cx: \"20.654\",\n    cy: \"3.167\",\n    rx: \"2.849\",\n    ry: \"2.815\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\"\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Empty.displayName = 'EmptyImage';\n}\nexport default Empty;", "map": {"version": 3, "names": ["React", "FastColor", "useLocale", "useToken", "Empty", "token", "locale", "bgColor", "colorBgBase", "themeStyle", "toHsl", "l", "opacity", "createElement", "style", "width", "height", "viewBox", "xmlns", "description", "fill", "fillRule", "transform", "fillOpacity", "cx", "cy", "rx", "ry", "d", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/empty/empty.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { FastColor } from '@ant-design/fast-color';\nimport { useLocale } from '../locale';\nimport { useToken } from '../theme/internal';\nconst Empty = () => {\n  const [, token] = useToken();\n  const [locale] = useLocale('Empty');\n  const bgColor = new FastColor(token.colorBgBase);\n  // Dark Theme need more dark of this\n  const themeStyle = bgColor.toHsl().l < 0.5 ? {\n    opacity: 0.65\n  } : {};\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    style: themeStyle,\n    width: \"184\",\n    height: \"152\",\n    viewBox: \"0 0 184 152\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"title\", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(24 31.67)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    fillOpacity: \".8\",\n    fill: \"#F5F5F7\",\n    cx: \"67.797\",\n    cy: \"106.89\",\n    rx: \"67.797\",\n    ry: \"12.668\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\",\n    fill: \"#AEB8C2\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\",\n    fill: \"url(#linearGradient-1)\",\n    transform: \"translate(13.56)\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\",\n    fill: \"#F5F5F7\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\",\n    fill: \"#DCE0E6\"\n  })), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\",\n    fill: \"#DCE0E6\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(149.65 15.383)\",\n    fill: \"#FFF\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    cx: \"20.654\",\n    cy: \"3.167\",\n    rx: \"2.849\",\n    ry: \"2.815\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\"\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Empty.displayName = 'EmptyImage';\n}\nexport default Empty;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,MAAM,GAAGC,KAAK,CAAC,GAAGF,QAAQ,CAAC,CAAC;EAC5B,MAAM,CAACG,MAAM,CAAC,GAAGJ,SAAS,CAAC,OAAO,CAAC;EACnC,MAAMK,OAAO,GAAG,IAAIN,SAAS,CAACI,KAAK,CAACG,WAAW,CAAC;EAChD;EACA,MAAMC,UAAU,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAACC,CAAC,GAAG,GAAG,GAAG;IAC3CC,OAAO,EAAE;EACX,CAAC,GAAG,CAAC,CAAC;EACN,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAEL,UAAU;IACjBM,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC,EAAE,aAAalB,KAAK,CAACa,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,CAACP,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACa,WAAW,KAAK,OAAO,CAAC,EAAE,aAAanB,KAAK,CAACa,aAAa,CAAC,GAAG,EAAE;IACzKO,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACa,aAAa,CAAC,GAAG,EAAE;IACvCS,SAAS,EAAE;EACb,CAAC,EAAE,aAAatB,KAAK,CAACa,aAAa,CAAC,SAAS,EAAE;IAC7CU,WAAW,EAAE,IAAI;IACjBH,IAAI,EAAE,SAAS;IACfI,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAa3B,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC3Ce,CAAC,EAAE,8IAA8I;IACjJR,IAAI,EAAE;EACR,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC3Ce,CAAC,EAAE,0IAA0I;IAC7IR,IAAI,EAAE,wBAAwB;IAC9BE,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC3Ce,CAAC,EAAE,yFAAyF;IAC5FR,IAAI,EAAE;EACR,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC3Ce,CAAC,EAAE,sjBAAsjB;IACzjBR,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC5Ce,CAAC,EAAE,wOAAwO;IAC3OR,IAAI,EAAE;EACR,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACa,aAAa,CAAC,GAAG,EAAE;IACxCS,SAAS,EAAE,0BAA0B;IACrCF,IAAI,EAAE;EACR,CAAC,EAAE,aAAapB,KAAK,CAACa,aAAa,CAAC,SAAS,EAAE;IAC7CW,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAa3B,KAAK,CAACa,aAAa,CAAC,MAAM,EAAE;IAC3Ce,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC3B,KAAK,CAAC4B,WAAW,GAAG,YAAY;AAClC;AACA,eAAe5B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}