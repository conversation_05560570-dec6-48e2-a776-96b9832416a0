{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Select, message } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst Login = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const onFinish = async values => {\n    setLoading(true);\n    try {\n      await login(values);\n      message.success('登录成功');\n      navigate('/dashboard');\n    } catch (error) {\n      message.error(error.message || '登录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"login-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-title\",\n        children: \"\\u963F\\u535A\\u56FE\\u4E66\\u9986\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        name: \"login\",\n        onFinish: onFinish,\n        autoComplete: \"off\",\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          rules: [{\n            required: true,\n            message: '请输入用户名!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: '请输入密码!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          rules: [{\n            required: true,\n            message: '请选择角色!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7BA1\\u7406\\u5458\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7528\\u6237\",\n              children: \"\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            style: {\n              width: '100%'\n            },\n            children: \"\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"9lHeDqpq+8/te3s+w4w+jcjcxvs=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Select", "message", "UserOutlined", "LockOutlined", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Option", "<PERSON><PERSON>", "_s", "loading", "setLoading", "login", "navigate", "onFinish", "values", "success", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "autoComplete", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "value", "type", "htmlType", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Form, Input, Button, Card, Select, message } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoginParams } from '@/types';\n\nconst { Option } = Select;\n\nconst Login: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const onFinish = async (values: LoginParams) => {\n    setLoading(true);\n    try {\n      await login(values);\n      message.success('登录成功');\n      navigate('/dashboard');\n    } catch (error: any) {\n      message.error(error.message || '登录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <Card className=\"login-form\">\n        <div className=\"login-title\">\n          阿博图书馆管理系统\n        </div>\n        <Form\n          name=\"login\"\n          onFinish={onFinish}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[{ required: true, message: '请输入用户名!' }]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"用户名\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[{ required: true, message: '请输入密码!' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"role\"\n            rules={[{ required: true, message: '请选择角色!' }]}\n          >\n            <Select placeholder=\"请选择角色\">\n              <Option value=\"管理员\">管理员</Option>\n              <Option value=\"用户\">用户</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              style={{ width: '100%' }}\n            >\n              登录\n            </Button>\n          </Form.Item>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AAEzB,MAAMS,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEkB;EAAM,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,QAAQ,GAAG,MAAOC,MAAmB,IAAK;IAC9CJ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMC,KAAK,CAACG,MAAM,CAAC;MACnBf,OAAO,CAACgB,OAAO,CAAC,MAAM,CAAC;MACvBH,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOI,KAAU,EAAE;MACnBjB,OAAO,CAACiB,KAAK,CAACA,KAAK,CAACjB,OAAO,IAAI,MAAM,CAAC;IACxC,CAAC,SAAS;MACRW,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEL,OAAA;IAAKY,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9Bb,OAAA,CAACR,IAAI;MAACoB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC1Bb,OAAA;QAAKY,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjB,OAAA,CAACX,IAAI;QACH6B,IAAI,EAAC,OAAO;QACZV,QAAQ,EAAEA,QAAS;QACnBW,YAAY,EAAC,KAAK;QAClBC,IAAI,EAAC,OAAO;QAAAP,QAAA,gBAEZb,OAAA,CAACX,IAAI,CAACgC,IAAI;UACRH,IAAI,EAAC,UAAU;UACfI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7B,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAmB,QAAA,eAEhDb,OAAA,CAACV,KAAK;YACJkC,MAAM,eAAExB,OAAA,CAACL,YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC;UAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZjB,OAAA,CAACX,IAAI,CAACgC,IAAI;UACRH,IAAI,EAAC,UAAU;UACfI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7B,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAmB,QAAA,eAE/Cb,OAAA,CAACV,KAAK,CAACoC,QAAQ;YACbF,MAAM,eAAExB,OAAA,CAACJ,YAAY;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZjB,OAAA,CAACX,IAAI,CAACgC,IAAI;UACRH,IAAI,EAAC,MAAM;UACXI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7B,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAmB,QAAA,eAE/Cb,OAAA,CAACP,MAAM;YAACgC,WAAW,EAAC,gCAAO;YAAAZ,QAAA,gBACzBb,OAAA,CAACC,MAAM;cAAC0B,KAAK,EAAC,oBAAK;cAAAd,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCjB,OAAA,CAACC,MAAM;cAAC0B,KAAK,EAAC,cAAI;cAAAd,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZjB,OAAA,CAACX,IAAI,CAACgC,IAAI;UAAAR,QAAA,eACRb,OAAA,CAACT,MAAM;YACLqC,IAAI,EAAC,SAAS;YACdC,QAAQ,EAAC,QAAQ;YACjBzB,OAAO,EAAEA,OAAQ;YACjB0B,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAlB,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACd,EAAA,CA1EID,KAAe;EAAA,QAEDJ,OAAO,EACRD,WAAW;AAAA;AAAAmC,EAAA,GAHxB9B,KAAe;AA4ErB,eAAeA,KAAK;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}