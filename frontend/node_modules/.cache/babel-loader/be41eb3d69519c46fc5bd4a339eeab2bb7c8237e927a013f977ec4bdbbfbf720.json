{"ast": null, "code": "import * as React from 'react';\nexport default function useMemo(getValue, condition, shouldUpdate) {\n  var cacheRef = React.useRef({});\n  if (!('value' in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {\n    cacheRef.current.value = getValue();\n    cacheRef.current.condition = condition;\n  }\n  return cacheRef.current.value;\n}", "map": {"version": 3, "names": ["React", "useMemo", "getValue", "condition", "shouldUpdate", "cacheRef", "useRef", "current", "value"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-util/es/hooks/useMemo.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useMemo(getValue, condition, shouldUpdate) {\n  var cacheRef = React.useRef({});\n  if (!('value' in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {\n    cacheRef.current.value = getValue();\n    cacheRef.current.condition = condition;\n  }\n  return cacheRef.current.value;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,OAAOA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAE;EACjE,IAAIC,QAAQ,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAI,EAAE,OAAO,IAAID,QAAQ,CAACE,OAAO,CAAC,IAAIH,YAAY,CAACC,QAAQ,CAACE,OAAO,CAACJ,SAAS,EAAEA,SAAS,CAAC,EAAE;IACzFE,QAAQ,CAACE,OAAO,CAACC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACnCG,QAAQ,CAACE,OAAO,CAACJ,SAAS,GAAGA,SAAS;EACxC;EACA,OAAOE,QAAQ,CAACE,OAAO,CAACC,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}