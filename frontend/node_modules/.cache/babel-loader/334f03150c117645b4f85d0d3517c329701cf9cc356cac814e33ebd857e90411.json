{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport { default as useLocale } from './useLocale';\nexport const ANT_MARK = 'internalMark';\nconst LocaleProvider = props => {\n  const {\n    locale = {},\n    children,\n    _ANT_MARK__\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('LocaleProvider');\n    process.env.NODE_ENV !== \"production\" ? warning(_ANT_MARK__ === ANT_MARK, 'deprecated', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale') : void 0;\n  }\n  React.useEffect(() => {\n    const clearLocale = changeConfirmLocale(locale === null || locale === void 0 ? void 0 : locale.Modal);\n    return clearLocale;\n  }, [locale]);\n  const getMemoizedContextValue = React.useMemo(() => Object.assign(Object.assign({}, locale), {\n    exist: true\n  }), [locale]);\n  return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    value: getMemoizedContextValue\n  }, children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  LocaleProvider.displayName = 'LocaleProvider';\n}\nexport default LocaleProvider;", "map": {"version": 3, "names": ["React", "devUseW<PERSON>ning", "changeConfirmLocale", "LocaleContext", "default", "useLocale", "ANT_MARK", "LocaleProvider", "props", "locale", "children", "_ANT_MARK__", "process", "env", "NODE_ENV", "warning", "useEffect", "clearLocale", "Modal", "getMemoizedContextValue", "useMemo", "Object", "assign", "exist", "createElement", "Provider", "value", "displayName"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/locale/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport { default as useLocale } from './useLocale';\nexport const ANT_MARK = 'internalMark';\nconst LocaleProvider = props => {\n  const {\n    locale = {},\n    children,\n    _ANT_MARK__\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('LocaleProvider');\n    process.env.NODE_ENV !== \"production\" ? warning(_ANT_MARK__ === ANT_MARK, 'deprecated', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale') : void 0;\n  }\n  React.useEffect(() => {\n    const clearLocale = changeConfirmLocale(locale === null || locale === void 0 ? void 0 : locale.Modal);\n    return clearLocale;\n  }, [locale]);\n  const getMemoizedContextValue = React.useMemo(() => Object.assign(Object.assign({}, locale), {\n    exist: true\n  }), [locale]);\n  return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    value: getMemoizedContextValue\n  }, children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  LocaleProvider.displayName = 'LocaleProvider';\n}\nexport default LocaleProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,OAAOC,aAAa,MAAM,WAAW;AACrC,SAASC,OAAO,IAAIC,SAAS,QAAQ,aAAa;AAClD,OAAO,MAAMC,QAAQ,GAAG,cAAc;AACtC,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,MAAM,GAAG,CAAC,CAAC;IACXC,QAAQ;IACRC;EACF,CAAC,GAAGH,KAAK;EACT,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGd,aAAa,CAAC,gBAAgB,CAAC;IAC/CW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAACJ,WAAW,KAAKL,QAAQ,EAAE,YAAY,EAAE,+GAA+G,CAAC,GAAG,KAAK,CAAC;EACnN;EACAN,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGf,mBAAmB,CAACO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACS,KAAK,CAAC;IACrG,OAAOD,WAAW;EACpB,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EACZ,MAAMU,uBAAuB,GAAGnB,KAAK,CAACoB,OAAO,CAAC,MAAMC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,MAAM,CAAC,EAAE;IAC3Fc,KAAK,EAAE;EACT,CAAC,CAAC,EAAE,CAACd,MAAM,CAAC,CAAC;EACb,OAAO,aAAaT,KAAK,CAACwB,aAAa,CAACrB,aAAa,CAACsB,QAAQ,EAAE;IAC9DC,KAAK,EAAEP;EACT,CAAC,EAAET,QAAQ,CAAC;AACd,CAAC;AACD,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCP,cAAc,CAACoB,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}