{"ast": null, "code": "import React from 'react';\nexport const AppConfigContext = /*#__PURE__*/React.createContext({});\nconst AppContext = /*#__PURE__*/React.createContext({\n  message: {},\n  notification: {},\n  modal: {}\n});\nexport default AppContext;", "map": {"version": 3, "names": ["React", "AppConfigContext", "createContext", "AppContext", "message", "notification", "modal"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/antd/es/app/context.js"], "sourcesContent": ["import React from 'react';\nexport const AppConfigContext = /*#__PURE__*/React.createContext({});\nconst AppContext = /*#__PURE__*/React.createContext({\n  message: {},\n  notification: {},\n  modal: {}\n});\nexport default AppContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACpE,MAAMC,UAAU,GAAG,aAAaH,KAAK,CAACE,aAAa,CAAC;EAClDE,OAAO,EAAE,CAAC,CAAC;EACXC,YAAY,EAAE,CAAC,CAAC;EAChBC,KAAK,EAAE,CAAC;AACV,CAAC,CAAC;AACF,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}