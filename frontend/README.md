# 阿博图书馆管理系统 - React + TypeScript 前端

这是一个使用 React + TypeScript + Ant Design 构建的图书馆管理系统前端应用。

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - UI组件库
- **React Router** - 路由管理
- **Axios** - HTTP客户端
- **React Scripts** - 构建工具

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── Auth/          # 认证相关组件
│   │   └── Layout/        # 布局组件
│   ├── contexts/          # React Context
│   ├── pages/             # 页面组件
│   ├── services/          # API服务
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── App.tsx            # 主应用组件
│   └── index.tsx          # 应用入口
├── package.json
├── tsconfig.json          # TypeScript配置
└── README.md
```

## 功能特性

### 已实现功能
- ✅ 用户认证（登录/退出）
- ✅ 响应式布局
- ✅ 路由管理
- ✅ 仪表板
- ✅ 用户管理（完整CRUD）
- ✅ 图书分类管理（完整CRUD）

### 待完善功能
- 🚧 图书信息管理
- 🚧 借阅管理
- 🚧 归还管理
- 🚧 罚金管理
- 🚧 留言板管理
- 🚧 收藏管理
- 🚧 公告管理
- 🚧 轮播图管理

## 安装和运行

### 前提条件
- Node.js >= 14.0.0
- npm 或 yarn

### 安装依赖
```bash
cd frontend
npm install
# 或
yarn install
```

### 开发环境运行
```bash
npm start
# 或
yarn start
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 运行测试
```bash
npm test
# 或
yarn test
```

## 配置说明

### 后端API配置
在 `package.json` 中配置了代理：
```json
"proxy": "http://localhost:8080"
```

API请求会自动代理到Spring Boot后端服务。

### 环境变量
可以创建 `.env` 文件来配置环境变量：
```
REACT_APP_API_BASE_URL=http://localhost:8080
```

## API接口说明

### 认证接口
- `POST /springboot33dng/login` - 用户登录
- `POST /springboot33dng/logout` - 用户退出

### 用户管理接口
- `GET /springboot33dng/yonghu/page` - 获取用户列表
- `GET /springboot33dng/yonghu/info/{id}` - 获取用户详情
- `POST /springboot33dng/yonghu/save` - 创建用户
- `POST /springboot33dng/yonghu/update` - 更新用户
- `POST /springboot33dng/yonghu/delete` - 删除用户

### 图书分类接口
- `GET /springboot33dng/tushufenlei/page` - 获取分类列表
- `POST /springboot33dng/tushufenlei/save` - 创建分类
- `POST /springboot33dng/tushufenlei/update` - 更新分类
- `POST /springboot33dng/tushufenlei/delete` - 删除分类

## 开发指南

### 添加新页面
1. 在 `src/pages/` 目录下创建新的页面组件
2. 在 `src/components/Layout/MainLayout.tsx` 中添加路由配置
3. 在菜单配置中添加新的菜单项

### 添加新的API服务
1. 在 `src/services/` 目录下创建新的服务文件
2. 使用 `src/utils/request.ts` 中的request实例发送HTTP请求
3. 在 `src/types/index.ts` 中定义相关的TypeScript类型

### 样式定制
- 全局样式：修改 `src/index.css`
- 组件样式：修改 `src/App.css` 或创建新的CSS模块
- Ant Design主题：可以通过craco或eject来自定义主题

## 部署说明

### 构建优化
- 代码分割已启用
- 生产构建会自动进行代码压缩和优化
- 支持PWA特性（可选）

### 部署到生产环境
1. 运行 `npm run build` 构建生产版本
2. 将 `build/` 目录部署到Web服务器
3. 配置Web服务器代理API请求到后端服务

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
