<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="utf-8">
<title>图书借阅</title>
<meta name="keywords" content="" />
<meta name="description" content="" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="stylesheet" href="../../layui/css/layui.css">
<link rel="stylesheet" href="../../xznstatic/css/common.css"/>
<link rel="stylesheet" href="../../xznstatic/css/style.css"/>
<script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
</head>
<style>
	html::after {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		content: '';
		display: block;
		background-attachment: fixed;
		background-size: cover;
		background-position: center;
			}
	#test1 {
		overflow: hidden;
	}
	#test1 .layui-carousel-ind li {
		width: 16px;
		height: 2px;
		border-width: 1px;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 3px;
		background-color: #f7f7f7;
		box-shadow: 0 0 6px rgba(255,0,0,.8);
	}
	#test1 .layui-carousel-ind li.layui-this {
		width: 60px;
		height: 1px;
		border-width: 1px;
		border-style: solid;
		border-color: rgba(127, 78, 22, 1);
		border-radius: 0;
		background-color: rgba(127, 78, 22, 1);
		box-shadow: 0 0 6px rgba(255,0,0,.8);
	}

	// 列表
	.recommend {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
	}
	.recommend .box {
	    width: 1002px;
		margin: 0 auto;
	}
	.recommend .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
	}
	.recommend .box .title span {
		padding: 0 10px;
		font-size: 16px;
		line-height: 1.4;
	}

	.recommend .box .filter {
		padding: 0 10px;
		display: flex;
		align-items: center;
		box-sizing: border-box;
		width: 100%;
		flex-wrap: wrap;
	}
	.recommend .box .filter .item-list {
		display: flex;
		align-items: center;
	}
	.recommend .box .filter .item-list .lable {
		font-size: 14px;
		color: #333;
		box-sizing: border-box;
	}
	.recommend .box .filter .item-list input {
		padding: 0 10px;
		box-sizing: border-box;
		outline: none;
	}
	.recommend .box .filter button {
		display: flex;
		padding: 0 10px;
		box-sizing: border-box;
		align-items: center;
		justify-content: center;
		outline: none;
	}
	.recommend .box .filter button i {
		margin-right: 4px;
	}

	.recommend .box .list {
		display: flex;
		flex-wrap: wrap;
	}

        	.recommend .box .list .list-item {
		flex: 0 0 25%;
		padding: 0 5px;
		box-sizing: border-box;
	}
	.recommend .box .list .list-item .list-item-body {
		cursor: pointer;
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 5px;
		box-sizing: border-box;
	}
	.recommend .box .list .list-item-body img {
		width: 100%;
		height: 100px;
		display: block;
		margin: 0 auto;
	}

	.recommend .box .list .list-item-body .info {
		display: flex;
		flex-wrap: wrap;
	}
	.recommend .box .list .list-item-body .info .price {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		text-align: center;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item-body .info .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		text-align: center;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item3 {
		flex: 0 0 33.33%;
	}

	.recommend .box .list .list-item5 {
		flex: 0 0 20%;
	}

	.recommend .box .news {
		display: flex;
		flex-wrap: wrap;
		padding: 0;
width: 100%;
	}

        	.recommend .box .news .list-item {
		flex: 0 0 50%;
		padding: 0 10px;
		box-sizing: border-box;
	}
	.recommend .box .news .list-item .list-item-body {
		cursor: pointer;
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 10px;
		box-sizing: border-box;
		display: flex;
	}

	.recommend .box .news .list-item .list-item-body img {
		width: 120px;
		height: 100%;
		display: block;
		margin: 0 auto;
	}

	.recommend .box .news .list-item .list-item-body .item-info {
		flex: 1;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding-left: 10px;
		box-sizing: border-box;
	}

	.recommend .box .news .list-item .list-item-body .item-info .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		box-sizing: border-box;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.recommend .box .news .list-item .list-item-body .item-info .time {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		box-sizing: border-box;
	}

	.recommend .box .news .list-item1 {
		flex: 0 0 100%;
	}

	.recommend .box .news .list-item3 {
		flex: 0 0 33.33%;
	}

	.index-pv1 .animation-box:hover {
		transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(0.6) rotate(0deg) skew(0deg, 0deg);
		transition: all 0.3s;
	}
  
  .layui-laypage .layui-laypage-count {
    padding: 0 10px;
  }
  .layui-laypage .layui-laypage-skip {
    padding-left: 10px;
  }
</style>
<body>
  <div id="app">
    <div class="banner">
      <div class="layui-carousel" id="test1" :style='{"boxShadow":"0 0 6px rgba(127, 78, 22, 1)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
      		  <div carousel-item>
      			<div v-for="(item,index) in swiperList" :key="index">
      				<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
      			</div>
      		  </div>
      		</div>
    </div>
	<div class="recommend index-pv1" :style='{"padding":"10px 0 10px 0","boxShadow":"0","margin":"10px 0px 0px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(238, 238, 238, 1)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
		<div class="box" style='width:80%'>
	    <div class="title" :style='{"padding":"10px 0 10px 0","boxShadow":"5px 5px 0px rgba(69,69,69, 1)  ","margin":"10px 0 10px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(127, 78, 22, 1)","borderRadius":"8px","borderWidth":"0","borderStyle":"solid","justifyContent":"space-between","height":"54px"}'>
	      <span :style='{"padding":"3px 40px 3px 30px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(255, 0, 0, 0)","backgroundColor":"rgba(171, 49, 4, 1)","color":"rgba(255, 255, 255, 1)","borderRadius":"0 18px 18px 0","borderWidth":"0","fontSize":"18px","borderStyle":"solid"}'>图书借阅</span><span :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"rgba(0,0,0,0)","color":"rgba(255, 255, 255, 1)","borderRadius":"0","borderWidth":"0","fontSize":"14px","borderStyle":"solid"}'>您现在的位置：图书借阅</span>
	    </div>
		<form class="layui-form filter" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 10px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(238, 238, 238, 1)","borderRadius":"4px","alignItems":"center","borderWidth":"0","borderStyle":"solid","justifyContent":"flex-end","height":"54px"}'>
									<div class="item-list">
				<div class="lable" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0, 0, 0, 0.78)","backgroundColor":"transparent","color":"rgba(127, 78, 22, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0px","width":"auto","fontSize":"16px","borderStyle":"solid"}'>借阅单号</div>
				<input type="text" :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(50, 47, 47, 1)","backgroundColor":"#fff","color":"rgba(13, 12, 12, 1)","borderRadius":"8px","textAlign":"center","borderWidth":"1px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"48px"}' name="jieyuedanhao" id="jieyuedanhao" placeholder="借阅单号" autocomplete="off" class="layui-input">
			</div>
																		<div class="item-list">
				<div class="lable" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0, 0, 0, 0.78)","backgroundColor":"transparent","color":"rgba(127, 78, 22, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0px","width":"auto","fontSize":"16px","borderStyle":"solid"}'>图书名称</div>
				<input type="text" :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(50, 47, 47, 1)","backgroundColor":"#fff","color":"rgba(13, 12, 12, 1)","borderRadius":"8px","textAlign":"center","borderWidth":"1px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"48px"}' name="tushumingcheng" id="tushumingcheng" placeholder="图书名称" autocomplete="off" class="layui-input">
			</div>
																																																												<div class="item-list">
				<div class="lable" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0, 0, 0, 0.78)","backgroundColor":"transparent","color":"rgba(127, 78, 22, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0px","width":"auto","fontSize":"16px","borderStyle":"solid"}'>用户名</div>
				<input type="text" :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(50, 47, 47, 1)","backgroundColor":"#fff","color":"rgba(13, 12, 12, 1)","borderRadius":"8px","textAlign":"center","borderWidth":"1px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"48px"}' name="yonghuming" id="yonghuming" placeholder="用户名" autocomplete="off" class="layui-input">
			</div>
																																							<button :style='{"padding":"0 15px","boxShadow":"2px 2px 0px rgba(69,69,69, 1) ","margin":"0 0 0 10px","borderColor":"#409EFF","backgroundColor":"rgba(127, 78, 22, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}' id="btn-search" type="button" class="layui-btn layui-btn-normal">
			  <i v-if="true" class="layui-icon layui-icon-search"></i>搜索
			</button>
			<button :style='{"padding":"0 15px","boxShadow":"2px 2px 0px rgba(69,69,69, 1) ","margin":"0 0 0 10px","borderColor":"#409EFF","backgroundColor":"rgba(127, 78, 22, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}' v-if="isAuth('tushujieyue','新增')" @click="jump('../tushujieyue/add.html')" type="button" class="layui-btn btn-theme">
			  <i v-if="true" class="layui-icon">&#xe654;</i>添加
			</button>
		</form>
	    <!-- 样式一 -->
	    <div class="list" style="position: relative;">
	      <div @click="jump('../tushujieyue/detail.html?id='+item.id)" v-for="(item,index) in dataList" :key="index" class="list-item" :class="4=='3'?'list-item3':4=='5'?'list-item5':''">
	        <div class="list-item-body animation-box" :style='{"padding":"0px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0 0 20px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(238, 238, 238, 1)","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
																																																																																																																																																<div class="info">
					<div v-if="item.price" :style='{"padding":"0","margin":"10px 0 0 0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(184, 93, 11, 1)","borderRadius":"0","textAlign":"right","width":"100%","fontSize":"16px"}' class="price">{{item.price}} RMB</div>
																																																																																																																																																																																			</div>
	        </div>
	      </div>
	    </div>
		<div class="pager" id="pager" :style="{textAlign:1==1?'left':1==2?'center':'right'}"></div>
	  </div>
	</div>


  </div>

  <script src="../../layui/layui.js"></script>
  <script src="../../js/vue.js"></script>
  <script src="../../js/config.js"></script>
  <script src="../../modules/config.js"></script>
  <script src="../../js/utils.js"></script>

  <script type="text/javascript">
    var vue = new Vue({
      el: '#app',
      data: {
        swiperList: [],
        dataList: [],
		swiperIndex: '-1'
      },
      filters: {
        newsDesc: function(val) {
          if (val) {
            if (val.length > 60) {
              return val.substring(0, 60).replace(/<[^>]*>/g).replace(/undefined/g, '');
            } else {
              return val.replace(/<[^>]*>/g).replace(/undefined/g, '');
            }
          }
          return '';
        }
      },
      methods: {
      	isAuth(tablename, button) {
          return isFrontAuth(tablename, button)
        },
        jump(url) {
          jump(url)
        }
      }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery'], function() {
      var layer = layui.layer;
      var element = layui.element;
      var carousel = layui.carousel;
      var laypage = layui.laypage;
      var http = layui.http;
      var jquery = layui.jquery;

			var limit = 8;

      // 获取轮播图 数据
      http.request('config/list', 'get', {
        page: 1,
        limit: 5
      }, function(res) {
        if (res.data.list.length > 0) {
          let swiperList = [];
          res.data.list.forEach(element => {
            if (element.value != null) {
              swiperList.push({
                img: element.value
              });
            }
          });
          vue.swiperList = swiperList;

		  vue.$nextTick(() => {
		    carousel.render({
		    	elem: '#test1',
		  				width: '100%',
		    	height: '500px',
		    	arrow: 'hover',
		    	anim: 'fade',
		    	autoplay: 'true',
		    	interval: '3000',
		    	indicator: 'inside'
		    });

		  })
          // vue.$nextTick(()=>{
          //   window.xznSlide();
          // });
        }
      });


      // 分页列表
      pageList();

      // 搜索按钮
      jquery('#btn-search').click(function(e) {
        pageList();
      });

      function pageList() {
        var param = {
          page: 1,
          limit: limit
        }


        if (jquery('#jieyuedanhao').val()) {
          param['jieyuedanhao'] = jquery('#jieyuedanhao').val() ? '%' + jquery('#jieyuedanhao').val() + '%' : '';
        }
        if (jquery('#tushumingcheng').val()) {
          param['tushumingcheng'] = jquery('#tushumingcheng').val() ? '%' + jquery('#tushumingcheng').val() + '%' : '';
        }
        if (jquery('#yonghuming').val()) {
          param['yonghuming'] = jquery('#yonghuming').val() ? '%' + jquery('#yonghuming').val() + '%' : '';
        }

	param['sfsh'] = '是';


        // 获取列表数据
        http.request('tushujieyue/list', 'get', param, function(res) {
          vue.dataList = res.data.list
          // 分页
          laypage.render({
            elem: 'pager',
            count: res.data.total,
            limit: limit,
            groups: 9,
            layout: ["prev","page","next"],
            theme: '#7F4E16',
            jump: function(obj, first) {
            	param.page = obj.curr;
              //首次不执行
              if (!first) {
                http.request('tushujieyue/list', 'get', param, function(res) {
                  vue.dataList = res.data.list
                })
              }
            }
          });
        })
      }
    });

    window.xznSlide = function() {
      jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
      jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_piclist ul",effect:"leftLoop", delayTime:200, autoPlay:true,triggerTime:0});
      jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_tx ul",delayTime:0, autoPlay:true});
      jQuery(".product_list").slide({mainCell:".bd ul",autoPage:true,effect:"leftLoop",autoPlay:true,vis:5,trigger:"click",interTime:4000});
    };
  </script>
</body>
</html>
