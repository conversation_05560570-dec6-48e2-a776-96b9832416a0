<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.JiaonafajinDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.JiaonafajinEntity" id="jiaonafajinMap">
        <result property="jieyuedanhao" column="jieyuedanhao"/>
        <result property="tushubianhao" column="tushubianhao"/>
        <result property="tushumingcheng" column="tushumingcheng"/>
        <result property="fakuanshuoming" column="fakuanshuoming"/>
        <result property="fakuanjine" column="fakuanjine"/>
        <result property="fakuanriqi" column="fakuanriqi"/>
        <result property="yonghuming" column="yonghuming"/>
        <result property="shouji" column="shouji"/>
        <result property="ispay" column="ispay"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.JiaonafajinVO" >
		SELECT * FROM jiaonafajin  jiaonafajin         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.JiaonafajinVO" >
		SELECT  jiaonafajin.* FROM jiaonafajin  jiaonafajin 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.JiaonafajinView" >

		SELECT  jiaonafajin.* FROM jiaonafajin  jiaonafajin 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.JiaonafajinView" >
		SELECT * FROM jiaonafajin  jiaonafajin <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
</mapper>