package com.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)  // 当使用 "*" 作为 origin 时，不能设置为 true
                .maxAge(3600);
    }

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 处理上传文件的静态资源
        registry.addResourceHandler("/upload/**")
                .addResourceLocations("classpath:/static/upload/", "file:static/upload/");
        
        // 处理前端静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/static/");
                
        // 处理其他静态资源
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
    }

    /**
     * 配置SPA路由支持
     * 将所有非API请求转发到index.html，支持前端路由
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 将根路径转发到index.html
        registry.addViewController("/").setViewName("forward:/index.html");

        // 将所有不是API的路径都转发到index.html，支持前端路由
        // 注意：这里简化了路由配置，避免复杂的正则表达式
        registry.addViewController("/{spring:[\\w\\-]+}")
                .setViewName("forward:/index.html");
        registry.addViewController("/**/{spring:[\\w\\-]+}")
                .setViewName("forward:/index.html");
    }
}
