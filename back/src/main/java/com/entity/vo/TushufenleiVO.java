package com.entity.vo;

import com.entity.TushufenleiEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
 

/**
 * 图书分类
 * 手机端接口返回实体辅助类 
 * （主要作用去除一些不必要的字段）
 * <AUTHOR> @email 
 * @date 2021-03-20 13:31:53
 */
public class TushufenleiVO  implements Serializable {
	private static final long serialVersionUID = 1L;

	 				
}
