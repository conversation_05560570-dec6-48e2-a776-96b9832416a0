<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.TushuxinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.TushuxinxiEntity" id="tushuxinxiMap">
        <result property="tushubianhao" column="tushubianhao"/>
        <result property="tushumingcheng" column="tushumingcheng"/>
        <result property="tushufenlei" column="tushufenlei"/>
        <result property="tupian" column="tupian"/>
        <result property="zuozhe" column="zuozhe"/>
        <result property="chubanshe" column="chubanshe"/>
        <result property="tushuzhuangtai" column="tushuzhuangtai"/>
        <result property="kejietianshu" column="kejietianshu"/>
        <result property="kejieshuliang" column="kejieshuliang"/>
        <result property="tushujianjie" column="tushujianjie"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.TushuxinxiVO" >
		SELECT * FROM tushuxinxi  tushuxinxi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.TushuxinxiVO" >
		SELECT  tushuxinxi.* FROM tushuxinxi  tushuxinxi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.TushuxinxiView" >

		SELECT  tushuxinxi.* FROM tushuxinxi  tushuxinxi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.TushuxinxiView" >
		SELECT * FROM tushuxinxi  tushuxinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
</mapper>