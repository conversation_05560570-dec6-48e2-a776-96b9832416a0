<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.TushufenleiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.TushufenleiEntity" id="tushufenleiMap">
        <result property="tushufenlei" column="tushufenlei"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.TushufenleiVO" >
		SELECT * FROM tushufenlei  tushufenlei         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.TushufenleiVO" >
		SELECT  tushufenlei.* FROM tushufenlei  tushufenlei 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.TushufenleiView" >

		SELECT  tushufenlei.* FROM tushufenlei  tushufenlei 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.TushufenleiView" >
		SELECT * FROM tushufenlei  tushufenlei <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
</mapper>