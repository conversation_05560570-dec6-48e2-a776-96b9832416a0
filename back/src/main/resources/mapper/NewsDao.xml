<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.NewsDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.NewsEntity" id="newsMap">
        <result property="title" column="title"/>
        <result property="introduction" column="introduction"/>
        <result property="picture" column="picture"/>
        <result property="content" column="content"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.NewsVO" >
		SELECT * FROM news  news         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.NewsVO" >
		SELECT  news.* FROM news  news 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.NewsView" >

		SELECT  news.* FROM news  news 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.NewsView" >
		SELECT * FROM news  news <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
</mapper>