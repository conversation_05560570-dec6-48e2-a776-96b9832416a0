# Tomcat
server:
    tomcat:
        uri-encoding: UTF-8
    port: 8080
    servlet:
        context-path: /springboot33dng

spring:
    datasource:
        driverClassName: com.mysql.cj.jdbc.Driver
        url: **************************************************************************************************************************************************************
        username: root
        password: hou153632001
    servlet:
      multipart:
        max-file-size: 10MB
        max-request-size: 10MB
    resources:
      static-locations: classpath:static/,file:static/

#mybatis
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.entity
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 1
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #逻辑删除配置
    logic-delete-value: -1
    logic-not-delete-value: 0
    #自定义SQL注入器
    sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    #springboot 项目mybatis plus 设置 jdbcTypeForNull (oracle数据库需配置JdbcType.NULL, 默认是Other)
    jdbc-type-for-null: 'null' 
