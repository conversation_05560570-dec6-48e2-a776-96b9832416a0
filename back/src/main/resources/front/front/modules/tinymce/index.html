<!DOCTYPE html>
<html lang="cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Layui-Tinymce</title>
</head>
<link rel="stylesheet" href="../layui/css/layui.css">
<body>
<div style="width: 90%;margin: 15px auto;">
    <textarea name="" id="edit">这里是<span style="color: #e03e2d;"><em>edit初始化</em></span>的内容</textarea>
    <hr />
    <div class="layui-btn-group" style="margin-top: 10px">
        <button class="layui-btn" lay-event="setContent">设置内容</button>
        <button class="layui-btn" lay-event="insertContent">插入内容</button>
        <button class="layui-btn" lay-event="getContent">获取内容</button>
        <button class="layui-btn" lay-event="getText">获取文本</button>
        <button class="layui-btn" lay-event="clearContent">清空内容</button>
        <button class="layui-btn" lay-event="reload">重载编辑器</button>
        <button class="layui-btn" lay-event="destroy">销毁编辑器</button>
        <button class="layui-btn" lay-event="render">加载编辑器</button>
    </div>
    <br /><hr />
    同时插入第二个编辑器，配置相互独立
    <hr />
    <textarea name="" id="edit2">这里是<span style="color: #e03e2d;"><em>edit2初始化</em></span>的内容</textarea>

</div>

</body>
<script src="../layui/layui.js"></script>
<script>
    /^http(s*):\/\//.test(location.href) || alert('请先部署到 localhost 下再访问');
    layui.extend({
        tinymce: '{/}./tinymce/tinymce'
    }).use(['tinymce', 'util', 'layer'], function () {
        var tinymce = layui.tinymce

        var util = layui.util;

        var edit = tinymce.render({
            elem: "#edit"
            , height: 600
            // ...
            // 其余配置可参考官方文档
        },(opt)=>{
            //加载完成后回调
        });

        tinymce.render({
            elem: "#edit2"
            , height: 200
        });

        util.event('lay-event', {
            getContent:() => {
                var content = tinymce.get('#edit').getContent()
                layer.alert(content)
            },
            setContent:() => {
                tinymce.get('#edit').setContent('点击按钮设置的内容：<span style="color: #e03e2d;">' + new Date()+'</span>')
            },
            clearContent:() => {
                tinymce.get('#edit').setContent('')
            },
            insertContent:() => {
                tinymce.get('#edit').insertContent('<b>插入内容</b>')
            },
            getText:() => {
                var text = tinymce.get('#edit').getContent({format:'text'})
                layer.alert(text)
            },
            reload:() => {
                tinymce.reload({
                    elem:'#edit'
                    // 所有参数都可以重新设置 ...
                },(opt) => {
                    //重载完成后回调函数，会把所有参数回传，
                    //重载仅仅重新渲染编辑器，不会清空textarea，可手动清空或设置
                    tinymce.get('#edit').setContent('')
                })
            },
            destroy:()=>{
                tinymce.get('#edit').destroy()
            },
            render:()=>{
                tinymce.render({elem:'#edit',height:'500px'})
            }
        });


    });
</script>
</html>
