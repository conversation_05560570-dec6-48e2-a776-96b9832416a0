
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="utf-8">
<title>首页</title>
<meta name="description" content="" />
<meta name="keywords" content="" />
<meta name="author" content="order by mobanxiu.cn" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="stylesheet" href="../../layui/css/layui.css">
<link href="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/css/swiper.min.css" rel="stylesheet">
<link rel="stylesheet" href="../../xznstatic/css/common.css"/>
<link rel="stylesheet" href="../../xznstatic/css/style.css"/>
<script src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
<script src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
</head>
<style>
	html::after {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		content: '';
		display: block;
		background-attachment: fixed;
		background-size: cover;
		background-position: center;
			}
	#test1 {
		overflow: hidden;
	}
	#test1 .layui-carousel-ind li {
		width: 16px;
		height: 2px;
		border-width: 1px;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 3px;
		background-color: #f7f7f7;
		box-shadow: 0 0 6px rgba(255,0,0,.8);
	}
	#test1 .layui-carousel-ind li.layui-this {
		width: 60px;
		height: 1px;
		border-width: 1px;
		border-style: solid;
		border-color: rgba(127, 78, 22, 1);
		border-radius: 0;
		background-color: rgba(127, 78, 22, 1);
		box-shadow: 0 0 6px rgba(255,0,0,.8);
	}

	.recommend {
	  padding: 10px 0;
	  display: flex;
	  justify-content: center;
	  background-repeat: no-repeat;
	  background-position: center center;
	  background-size: cover;
	}

	.recommend .box {
	    width: 1014px;
	}

	.recommend .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.recommend .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.recommend .box .list {
		display: flex;
		flex-wrap: wrap;
	}
    	.index-pv1 .box .list .list-item {
		flex: 0 0 ${var1}%;
		padding: 0 5px;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item-body {
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 5px;
		box-sizing: border-box;
		cursor: pointer;
	}

	.recommend .box .list img {
		width: 100%;
		height: 100px;
		display: block;
		margin: 0 auto;
    object-fit: cover;
    max-width: 100%;
	}

	.recommend .box .list .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		text-align: center;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item3 {
		flex: 0 0 33.33%;
	}

	.recommend .box .list .list-item5 {
		flex: 0 0 20%;
	}
	
	/* 商品推荐-样式4-开始 */
	.recommend .list-4{
	  display: flex;
	  flex-wrap: wrap;
	  justify-content: center;
	}
	.recommend .list-4 .list-4-body {
	    display: flex;
	    flex-direction: column;
	}
	
	.recommend .list-4 .list-4-item {
	    position: relative;
	    z-index: 1;
	}
	.recommend .list-4 .list-4-item.item-1 {
	    width: 400px;
	    height: 400px;
	    margin-right: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-2 {
	    width: 220px;
	    height: 190px;
	    margin-right: 20px;
	    margin-bottom: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-3 {
	    width: 220px;
	    height: 190px;
	    margin-right: 20px;
	    margin-bottom: 0;
	}
	
	.recommend .list-4 .list-4-item.item-4 {
	    width: 190px;
	    height: 190px;
	    margin-right: 0;
	    margin-bottom: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-5 {
	    width: 190px;
	    height: 190px;
	    margin-right: 0;
	    margin-bottom: 0;
	}
	
	.recommend .list-4 .list-4-item img {
	      width: 100%;
	      height: 100%;
	      object-fit: cover;
	      display: block;
	    }
	
	.recommend .list-4 .list-4-item .list-4-item-center {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: auto;
		display: flex;
		flex-wrap: wrap;
		background-color: rgba(0,0,0,.3);
	}
	.recommend .list-4 .list-4-item .list-4-item-center .list-4-item-title {
		width: 50%;
		text-align: left;
		line-height: 44px;
		color: #fff;
		font-size: 14px;
		padding: 0 6px;
	}
	
	.recommend .list-4 .list-4-item .list-4-item-center .list-4-item-price {
		width: 50%;
		text-align: right;
		line-height: 44px;
		color: #fff;
		font-size: 14px;
		padding: 0 6px;
	}
	/* 商品推荐-样式4-结束 */
	/* 商品推荐-样式5-开始 */
	.recommend #recommend-five-swiper.swiper-container-horizontal>.swiper-pagination-bullets {
	    line-height: 1;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-prev {
		z-index: 5;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-next {
		z-index: 5;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-active {
		z-index: 9;
	}
	
	.recommend #lists-five-swiper.swiper-container-horizontal>.swiper-pagination-bullets {
	    line-height: 1;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-prev {
		z-index: 5;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-next {
		z-index: 5;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-active {
		z-index: 9;
	}
	/* 商品推荐-样式5-结束 */

	.news {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
		width: 100%;
	}

	.news .box {
	    width: 1014px;
	}

	.news .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.news .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.news .box .list {
		display: flex;
		flex-wrap: wrap;
	}
      	.index-pv2 .box .list .list-item {
		flex: 0 0 50%;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.news .box .list .list-item .list-item-body {
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 10px;
		box-sizing: border-box;
		display: flex;
		cursor: pointer;
	}

	.news .box .list .list-item .list-item-body img {
		width: 120px;
		height: 100%;
		display: block;
		margin: 0 auto;
    object-fit: cover;
    max-width: 100%;
	}

	.news .box .list .list-item .list-item-body .item-info {
		flex: 1;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding-left: 10px;
		box-sizing: border-box;
	}

	.news .box .list .list-item .list-item-body .item-info .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		box-sizing: border-box;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.news .box .list .list-item .list-item-body .item-info .time {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.news .box .list .list-item1 {
		flex: 0 0 100%;
	}

	.news .box .list .list-item3 {
		flex: 0 0 33.33%;
	}

	.lists {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
	}

	.lists .box {
	    width: 1014px;
	    overflow: hidden;
	}

	.lists .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.lists .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.lists .box .swiper-slide {
		box-sizing: border-box;
		cursor: pointer;
	}

	.lists .box .swiper-slide .img-box {
		width: 100%;
		overflow: hidden;
	}

	.lists .box .swiper-slide .img-box img {
		width: 100%;
		height: 100%;
		object-fit: cover;
    max-width: 100%;
	}

                	.index-pv3 .box .list .list-item {
  		flex: 0 0 25%;
  		padding: 0 10px;
  		box-sizing: border-box;
  	}

	.index-pv1 .animation-box:hover {
		transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.06) rotate(0deg) skew(0deg, 0deg);
		transition: all 0.3s;
		z-index: 2;
		position: relative;
	}
  .index-pv2 .animation-box:hover {
  	transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
  	transition: all 0.3s;
	z-index: 2;
	position: relative;
  }
  .index-pv3 .animation-box:hover {
  	transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(0.6) rotate(0deg) skew(0deg, 0deg);
  	transition: all 0.3s;
	z-index: 2;
	position: relative;
  }
  
	#new-list-6 .swiper-wrapper {
		-webkit-transition-timing-function: linear;
		-moz-transition-timing-function: linear;
		-ms-transition-timing-function: linear;
		-o-transition-timing-function: linear;
		transition-timing-function: linear;
	}
</style>
<body>
  <div id="app">
    <div class="banner">
		<div class="layui-carousel" id="test1" :style='{"boxShadow":"0 0 6px rgba(127, 78, 22, 1)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
		  <div carousel-item>
			<div v-for="(item,index) in swiperList" :key="index">
				<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
			</div>
		  </div>
		</div>
      <!-- <div class="bd">
        <ul>
          <li v-for="(item,index) in swiperList" v-bind:key="index"><img style="width: 1920px;height: 492px;" :src="item.img" /></li>
        </ul>
        <div class="hd">
          <ul>
            <li v-for="(item,index) in swiperList" v-bind:key="index"></li>
          </ul>
        </div>
      </div> -->
    </div>

	<div :style='{"display": "flex","justify-content": "center","padding":"20px"}'>
		<select v-if="queryList.length>1" @change="queryChange($event)" name="queryName" id="queryName">
			<option v-for="(item,index) in queryList" v-bind:key="index" :value="index">{{item.queryName}}</option>
		</select>
		<div v-if="queryIndex==0" class="item-list">
			<input type="text" :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(50, 47, 47, 1)","backgroundColor":"#fff","color":"rgba(13, 12, 12, 1)","borderRadius":"8px","textAlign":"center","borderWidth":"1px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"48px"}'
			 name="tushuxinxitushumingcheng" id="tushuxinxitushumingcheng" placeholder="图书名称" autocomplete="off" class="layui-input">
		</div>
		<button v-if="queryIndex==0" :style='{"padding":"0 15px","boxShadow":"2px 2px 0px rgba(69,69,69, 1) ","margin":"0 0 0 10px","borderColor":"#409EFF","backgroundColor":"rgba(127, 78, 22, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}'
		 id="btn-search" @click="jump('../tushuxinxi/list.html')" type="button" class="layui-btn layui-btn-normal">
			<i v-if="true" class="layui-icon layui-icon-search"></i>搜索
		</button>
	</div>

    <div id="content">
	<!-- 商品推荐 -->
	<div class="recommend index-pv1" :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0px","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 255, 255, 1)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
		<div class="box" style='width:90%'>
			<div class="title" :style='{"padding":"10px 0 10px 0","boxShadow":"10px 10px  0 rgba(127, 78, 22, 1) ","margin":"10px 0 30px 0","borderColor":"rgba(255, 69, 0, 1)","backgroundColor":"rgba(69, 69, 69, 1)","color":"rgba(250, 212, 0, 1)","borderRadius":"0px","alignItems":"center","borderWidth":"0   0 2px 10px","fontSize":"16px","borderStyle":"solid"}'>
				<span>Recommend</span><span>图书信息推荐</span>
			</div>
			<div id="recommend-five-swiper" class="swiper-container" style="box-sizing: border-box;" :style='{"padding":"50px 0","boxShadow":"0 0 6px rgba(0,0,0,.3)","margin":"0","borderColor":"#ccc","backgroundColor":"#fff","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"400px"}'>
			  <div class="swiper-wrapper">
			    <div class="swiper-slide" v-for="(item,index) in tushuxinxiRecommend" class="list-item" :key="index" @click="jump('../tushuxinxi/detail.html?id='+item.id)">
																																													<img style="object-fit: cover;position:absolute;" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#fff","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="item.tupian?item.tupian.split(',')[0]:''" alt="" />
																																																																																														</div>
			  </div>
			  <!-- Add Pagination -->
			  <div class="swiper-pagination"></div>
			  <!-- Add Arrows -->
			  <div class="swiper-button-next"></div>
			  <div class="swiper-button-prev"></div>
			</div>
			<div class="clear"></div>
			<div style="text-align: center;">
			<button @click="jump('../tushuxinxi/list.html')" style="display: block;cursor: pointer;" type="button" :style='{"padding":"0 15px","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"4px auto","borderColor":"#ccc","backgroundColor":"rgba(127, 78, 22, 1)","color":"rgba(255, 255, 255, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"34px"}'>查看更多<i v-if="true" :style='{"isshow":true,"padding":"0 0 0 1px","fontSize":"14px","color":"rgba(255, 255, 255, 1)"}' class="layui-icon layui-icon-next"></i></el-button>
			</div>
		</div>
	</div>

	<!-- 公告信息 -->
	<!-- 样式二 -->
	<div class="news index-pv2" style="display: flex;justify-content: center;width:100%" :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(238, 238, 238, 1)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
	  <div class="box" style='width:90%'>
	    <div class="title" :style='{"padding":"10px 0 10px 20px","boxShadow":"15px 15px 0px rgba(69,69,69, 1)","margin":"10px 0 40px 0","borderColor":"rgba(255, 120, 0, 1)","backgroundColor":"rgba(127, 78, 22, 1)","color":"rgba(255, 255, 255, 1)","borderRadius":"0px","alignItems":"flex-start","borderWidth":"0 0 2px 15px","fontSize":"18px","borderStyle":"solid"}'>
	      <span>HOME NEWS</span><span>公告信息</span>
	    </div>
	    <div class="list">
	      <div v-for="(item,index) in newsList" :key="index" class="list-item" @click="jump('../news/detail.html?id='+item.id)" :class="2=='1'?'list-item1':2=='3'?'list-item3':''">
	        <div class="list-item-body animation-box" :style='{"padding":"20px","boxShadow":"5px 5px 0px rgba(127, 78, 22, 1)","margin":"0 0 20px 0","borderColor":"rgba(200, 200, 200, 1)","backgroundColor":"#fff","borderRadius":"6px","borderWidth":"1px","borderStyle":"solid"}'>
			  <img :style='{"boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(0,0,0,.3)","borderRadius":"130px","borderWidth":"0","width":"130px","borderStyle":"solid","height":"130px"}' :src="item.picture" alt="" />
	          <div class="item-info">
				<div v-if='true' :style='{"isshow":true,"padding":"0","margin":"5px 0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(127, 78, 22, 1)","borderRadius":"0","textAlign":"left","fontSize":"16px"}' class="name">{{item.title}}</div>
				<div :style='{"padding":"0","margin":"0","backgroundColor":"rgba(0,0,0,0)","color":"#999","borderRadius":"0","textAlign":"right","fontSize":"12px"}' class="time">{{item.addtime}}</div>
	          </div>
	        </div>
	      </div>
	    </div>
	    <div class="clear"></div>
	    <div style="text-align: center;">
		<button @click="jump('../news/list.html')" style="display: block;cursor: pointer;" type="button" :style='{"padding":"0 15px","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"4px auto","borderColor":"#ccc","backgroundColor":"rgba(127, 78, 22, 1)","color":"rgba(255, 255, 255, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"34px"}'>查看更多<i v-if="true" :style='{"isshow":true,"padding":"0 0 0 1px","fontSize":"14px","color":"rgba(255, 255, 255, 1)"}' class="layui-icon layui-icon-next"></i></el-button>
	    </div>
	  </div>
	</div>

	<!-- 特价商品展示 -->

    </div>
  </div>

  <script src="../../layui/layui.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/js/swiper.min.js"></script>
  <script src="../../js/vue.js"></script>
  <script src="../../js/config.js"></script>
  <script src="../../modules/config.js"></script>
  <script src="../../js/utils.js"></script>
  <script type="text/javascript">
    var vue = new Vue({
      el: '#app',
      data: {
            queryList:[
                {
                    queryName:"图书名称",
                },
            ],
            queryIndex: 0,

        tushuxinxiRecommend: [],

        swiperList: [],
        // 公告信息
        newsList: [],
        leftNewsList: [],
        rightNewsList: []
      },
      filters: {
        newsDesc: function(val) {
          if (val) {
            val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
            if (val.length > 60) {
              val = val.substring(0, 60);
            }

            return val;
          }
          return '';
        }
      },
      methods: {
        jump(url) {
		if (this.queryIndex == 0) {
			localStorage.setItem('indexQueryCondition', document.getElementById("tushuxinxitushumingcheng").value);
		}
          	jump(url)
        },
	queryChange(event) {
		this.queryIndex = event.target.value;
		if (this.queryIndex == 0) {
			this.tushuxinxitushumingcheng = this.queryList[event.target.value].queryName;
		}
	}
      }
    });

    layui.use(['layer', 'form', 'element', 'carousel', 'http', 'jquery'], function() {
		var layer = layui.layer;
		var element = layui.element;
		var form = layui.form;
		var carousel = layui.carousel;
		var http = layui.http;
		var jquery = layui.jquery;

      // 获取轮播图 数据
      http.request('config/list', 'get', {
        page: 1,
        limit: 5
      }, function(res) {
        if (res.data.list.length > 0) {
          let swiperList = [];
          res.data.list.forEach(element => {
            if (element.value != null) {
              swiperList.push({
                img: element.value
              });
            }
          });

		  vue.swiperList = swiperList;

		  vue.$nextTick(() => {
		    carousel.render({
		    	elem: '#test1',
				width: '100%',
		    	height: '500px',
		    	arrow: 'hover',
		    	anim: 'fade',
		    	autoplay: 'true',
		    	interval: '3000',
		    	indicator: 'inside'
		    });

		  })

          // vue.$nextTick(()=>{
          //   window.xznSlide();
          // });
        }
      });

      // 公告信息
      http.request('news/list', 'get', {
        page: 1,
	limit: 2 * 2,
        order: 'desc'
      }, function(res) {
        var newsList = res.data.list;
		for(var i = 0; i<newsList.length; i++) {
			let d = newsList[i].addtime.split(' ')
			d = d[0].split('-')
			newsList[i].year = d[0]
			newsList[i].month = d[1] + '-' + d[2]
		}
		
        vue.newsList = newsList;
        if (newsList.length > 0 && newsList.length <= 2) {
          vue.leftNewsList = res.data.list
        } else {
          var leftNewsList = []
          for (let i = 0; i <= 2; i++) {
            leftNewsList.push(newsList[i]);
          }
          vue.leftNewsList = leftNewsList
        }
        if (newsList.length > 2 && newsList.length <= 8) {
          var rightNewsList = []
          for (let i = 3; i <= newsList.length; i++) {
            rightNewsList.push(newsList[i]);
          }
          vue.rightNewsList = rightNewsList
        }

		let flag = 2;
		let options = {"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"slidesPerView":5,"loop":true,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
		options.pagination = {el:'null'}
		if(flag == 3) {
			vue.$nextTick(() => {
                                                                                                                                				new Swiper ('#newsnews', options)
                                                			})
		}
		
		if(flag == 6) {
			let sixSwiper = {
				loop: true,
				speed: 2500,
				slidesPerView: 3,
				spaceBetween: 10,
				centeredSlides: true,
				watchSlidesProgress: true,
				autoplay: {
				  delay: 0,
				  stopOnLastSlide: false,
				  disableOnInteraction: false
				}
			}
			
			vue.$nextTick(() => {
				new Swiper('#new-list-6', sixSwiper)
			})
		}
      });

      // 获取推荐信息
      http.request(`tushuxinxi/autoSort`, 'get', {
        page: 1,
		}, function(res) {
			vue.tushuxinxiRecommend = res.data.list
			let flag = 5;
			let options = {"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"slidesPerView":5,"loop":true,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
			options.pagination = {el:'null'}
			if(flag == 3) {
				vue.$nextTick(() => {
																																				new Swiper ('#recommendtushuxinxi', options)
																																																																																				})
			}
			
			// 商品推荐样式五
			if(flag == 5) {
				vue.$nextTick(() => {
					var swiper = new Swiper('#recommend-five-swiper', {
						loop: true,
						speed: 500,
						slidesPerView: 5,
						spaceBetween: 10,
						autoplay: {"delay":3000,"disableOnInteraction":false},
						centeredSlides: true,
						watchSlidesProgress: true,
						on: {
							setTranslate: function() {
								slides = this.slides
								for (i = 0; i < slides.length; i++) {
									slide = slides.eq(i)
									progress = slides[i].progress
									// slide.html(progress.toFixed(2)); //看清楚progress是怎么变化的
									slide.css({
										'opacity': '',
										'background': ''
									});
									slide.transform(''); //清除样式
					
									slide.transform('scale(' + (1.5 - Math.abs(progress) / 4) + ')');
								}
							},
							setTransition: function(transition) {
								for (var i = 0; i < this.slides.length; i++) {
									var slide = this.slides.eq(i)
									slide.transition(transition);
								}
							},
						},
						navigation: {"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},
						pagination: {"el":".swiper-pagination","clickable":true},
					});
				})
			}
		});

  });

  window.xznSlide = function() {
    // jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
    jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_piclist ul",effect:"leftLoop", delayTime:200, autoPlay:true,triggerTime:0});
    jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_tx ul",delayTime:0, autoPlay:true});
    jQuery(".product_list").slide({mainCell:".bd ul",autoPage:true,effect:"leftLoop",autoPlay:true,vis:5,trigger:"click",interTime:4000});
  };
</script>
<script src="../../xznstatic/js/index.js"></script>
</body>
</html>
