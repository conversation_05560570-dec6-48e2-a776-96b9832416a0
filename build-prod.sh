#!/bin/bash

echo "构建图书馆管理系统生产版本..."

# 检查是否安装了Maven
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请先安装Maven"
    exit 1
fi

# 清理之前的构建
echo "清理之前的构建..."
cd back
mvn clean

# 构建项目（会自动构建前端并打包）
echo "构建项目..."
mvn package -DskipTests -Pproduction

if [ $? -eq 0 ]; then
    echo "构建成功!"
    echo "生产版本JAR文件位置: back/target/t044-0.0.1-SNAPSHOT.jar"
    echo ""
    echo "运行生产版本:"
    echo "java -jar back/target/t044-0.0.1-SNAPSHOT.jar"
    echo ""
    echo "访问地址: http://localhost:8080/springboot33dng"
else
    echo "构建失败!"
    exit 1
fi
