# 启动教程

- 项目录屏：https://www.bilibili.com/video/BV1AN4y1i7zo
- 启动教程：https://www.bilibili.com/video/BV1pW4y1P7GR
- 安装包在视频详情中已提供。


# 项目简介
基于Vue.js和SpringBoot的图书馆管理系统，分为管理后台和用户网页端，可以给管理员和普通用户角色使用，包括图书分类模块、图书信息模块、图书借阅模块、图书归还模块、缴纳罚金模块和系统基础模块，项目编号T044。

随着社会的发展，计算机的优势和普及使得阿博图书馆管理系统的开发成为必需。阿博图书馆管理系统主要是借助计算机，通过对图书借阅等信息进行管理。减少管理员的工作，同时也方便广大用户对所需图书借阅信息的及时查询以及管理。
阿博图书馆管理系统的开发过程中，采用B / S架构，主要使用Java技术进行开发，结合最新流行的springboot框架。使用Mysql数据库和Eclipse开发环境。该阿博图书馆管理系统包括用户和管理员。其主要功能包括管理员：首页、个人中心、用户管理、图书分类管理、图书信息管理、图书借阅管理、图书归还管理、缴纳罚金管理、留言板管理、系统管理，用户：首页、个人中心、图书借阅管理、图书归还管理、缴纳罚金管理、我的收藏管理，前台首页；首页、图书信息、公告信息、留言反馈、个人中心、后台管理等功能。
本论文对阿博图书馆管理系统的发展背景进行详细的介绍，并且对系统开发技术进行介绍，然后对系统进行需求分析，对阿博图书馆管理系统业务流程、系统结构以及数据都进行详细说明。用户可根据关键字进行查找自己想要的信息等。

关键词：阿博图书馆管理系统，Mysql数据库，Java技术 springboot框架
