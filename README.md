# 阿博图书馆管理系统

一个基于Spring Boot + React + TypeScript的现代化图书馆管理系统。

## 🚀 技术栈

### 后端
- **Spring Boot 2.2.2** - Java Web框架
- **MyBatis Plus** - ORM框架
- **MySQL** - 数据库
- **Maven** - 项目管理工具

### 前端
- **React 18** - 前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - UI组件库
- **React Router** - 路由管理
- **Axios** - HTTP客户端

## 📁 项目结构

```
LibrarySystem/
├── back/                   # Spring Boot后端
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/   # Java源码
│   │   │   └── resources/  # 配置文件和静态资源
│   │   └── test/           # 测试代码
│   └── pom.xml            # Maven配置
├── frontend/              # React前端
│   ├── public/           # 静态资源
│   ├── src/              # 源码
│   │   ├── components/   # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   ├── types/        # TypeScript类型
│   │   └── utils/        # 工具函数
│   ├── package.json      # npm配置
│   └── tsconfig.json     # TypeScript配置
├── t044.sql              # 数据库脚本
├── start-dev.sh/.bat     # 开发环境启动脚本
├── build-prod.sh/.bat    # 生产环境构建脚本
└── README.md             # 项目说明
```

## 🛠️ 开发环境搭建

### 前提条件
- Java 8+
- Node.js 14+
- Maven 3.6+
- MySQL 5.7+

### 1. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE t044 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据
mysql -u root -p t044 < t044.sql
```

### 2. 修改数据库配置
编辑 `back/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: **************************************************************************************************************************************************************
    username: root
    password: 你的密码
```

### 3. 启动开发环境

#### 方式一：使用启动脚本（推荐）
```bash
# Linux/Mac
chmod +x start-dev.sh
./start-dev.sh

# Windows
start-dev.bat
```

#### 方式二：手动启动
```bash
# 启动后端
cd back
mvn spring-boot:run

# 启动前端（新终端）
cd frontend
npm install
npm start
```

### 4. 访问系统
- 前端开发地址：http://localhost:3000
- 后端API地址：http://localhost:8080/springboot33dng

## 🚀 生产环境部署

### 构建生产版本
```bash
# Linux/Mac
chmod +x build-prod.sh
./build-prod.sh

# Windows
build-prod.bat

# 或者手动构建（使用production profile）
cd back
mvn clean package -DskipTests -Pproduction
```

### 运行生产版本
```bash
java -jar back/target/t044-0.0.1-SNAPSHOT.jar
```

访问地址：http://localhost:8080/springboot33dng

### 开发环境构建（仅后端）
```bash
cd back
mvn clean package -DskipTests
```
这样构建不会包含前端资源，适合开发时使用。

## 📋 功能特性

### 已实现功能
- ✅ 用户认证（管理员/普通用户）
- ✅ 用户管理（完整CRUD）
- ✅ 图书分类管理（完整CRUD）
- ✅ 图书信息管理（完整CRUD）
- ✅ 借阅管理（申请、审核、状态跟踪）
- ✅ 归还管理（归还流程、罚金计算）
- ✅ 罚金管理（统计、缴费确认）
- ✅ 留言板管理（查看、回复）
- ✅ 响应式布局
- ✅ 前后端分离开发
- ✅ 生产环境统一部署

### 技术特性
- ✅ **TypeScript支持** - 完整的类型安全
- ✅ **性能优化** - 缓存系统、性能监控
- ✅ **安全加固** - 输入验证、权限控制
- ✅ **错误处理** - 错误边界、优雅降级
- ✅ **用户体验** - 加载状态、操作反馈

## 🔧 开发指南

### API接口规范
- 基础路径：`/springboot33dng`
- 认证接口：`/auth/*`
- 业务接口：`/{模块名}/*`

### 前端开发
- 组件开发：使用TypeScript + React Hooks
- 状态管理：React Context
- UI组件：Ant Design
- 路由：React Router v6

### 后端开发
- 控制器：RESTful API设计
- 服务层：业务逻辑处理
- 数据层：MyBatis Plus

## 📝 默认账号

### 管理员
- 用户名：admin
- 密码：admin
- 角色：管理员

### 普通用户
- 用户名：user
- 密码：user
- 角色：用户

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 📺 原项目参考

- 项目录屏：https://www.bilibili.com/video/BV1AN4y1i7zo
- 启动教程：https://www.bilibili.com/video/BV1pW4y1P7GR
