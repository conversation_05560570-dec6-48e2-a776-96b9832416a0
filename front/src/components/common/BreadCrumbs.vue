<template>
  <el-breadcrumb class="app-breadcrumb" separator="/" style="height:80px;backgroundColor:rgba(255, 255, 255, 1);borderRadius:0px;padding:0px 20px 0px 20px;boxShadow:;borderWidth:0px;borderStyle:none;borderColor:rgba(255, 255, 255, 1);">
    <transition-group name="breadcrumb" class="box" :style="1==1?'justifyContent:flex-start;':1==2?'justifyContent:center;':'justifyContent:flex-end;'">
      <el-breadcrumb-item v-for="(item,index) in levelList" :key="item.path">
        <span v-if="item.redirect==='noRedirect'||index==levelList.length-1" class="no-redirect">{{ item.name }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.name }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import pathToRegexp from 'path-to-regexp'
import { generateTitle } from '@/utils/i18n'

interface RouteItem {
  path: string
  name?: string
  meta?: any
  redirect?: string
}

@Component({
  name: 'BreadCrumbs'
})
export default class BreadCrumbs extends Vue {
  levelList: RouteItem[] | null = null

  @Watch('$route')
  onRouteChange() {
    this.getBreadcrumb()
  }

  created() {
    this.getBreadcrumb()
    this.breadcrumbStyleChange()
  }

  generateTitle = generateTitle

  getBreadcrumb() {
    // only show routes with meta.title
    const route = this.$route
    let matched = route.matched.filter((item: any) => item.meta)
    const first = matched[0]
    matched = [{ path: '/index' }].concat(matched)

    this.levelList = matched.filter((item: any) => item.meta)
  }

  isDashboard(route: any) {
    const name = route && route.name
    if (!name) {
      return false
    }
    return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()
  }

  pathCompile(path: string) {
    // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561
    const { params } = this.$route
    const toPath = pathToRegexp.compile(path)
    return toPath(params)
  }

  handleLink(item: RouteItem) {
    const { redirect, path } = item
    if (redirect) {
      this.$router.push(redirect)
      return
    }
    this.$router.push(path)
  }

  breadcrumbStyleChange(val?: any) {
    this.$nextTick(() => {
      document.querySelectorAll('.app-breadcrumb .el-breadcrumb__separator').forEach((el: any) => {
        el.innerText = "/"
        el.style.color = "#C0C4CC"
      })
      document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner a').forEach((el: any) => {
        el.style.color = "rgba(140, 145, 171, 1)"
      })
      document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner .no-redirect').forEach((el: any) => {
        el.style.color = "rgba(162, 169, 212, 1)"
      })

      const str = "vertical"
      if ("vertical" === str) {
        let headHeight = "60px"
        headHeight = parseInt(headHeight) + 10 + 'px'
        document.querySelectorAll('.app-breadcrumb').forEach((el: any) => {
          el.style.marginTop = headHeight
        })
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: block;
  font-size: 14px;
  line-height: 50px;

  .box {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: flex-start;
    align-items: center;
  }

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
