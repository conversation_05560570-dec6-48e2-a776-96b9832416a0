interface BaseConfig {
    url: string
    name: string
    indexUrl: string
}

interface ProjectConfig {
    projectName: string
}

interface Base {
    get(): BaseConfig
    getProjectName(): ProjectConfig
}

const base: Base = {
    get(): BaseConfig {
        return {
            url: "http://localhost:8080/springboot33dng/",
            name: "springboot33dng",
            // 退出到首页链接
            indexUrl: 'http://localhost:8080/springboot33dng/front/index.html'
        }
    },
    getProjectName(): ProjectConfig {
        return {
            projectName: "阿博图书馆管理系统"
        }
    }
}

export default base
