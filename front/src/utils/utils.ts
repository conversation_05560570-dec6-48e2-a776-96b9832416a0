import storage from './storage'
import menu from './menu'

interface MenuChild {
    tableName: string
    buttons: string[]
}

interface BackMenu {
    child: MenuChild[]
}

interface MenuRole {
    roleName: string
    backMenu: BackMenu[]
}

/**
 * 是否有权限
 * @param tableName 表名
 * @param key 权限键
 */
export function isAuth(tableName: string, key: string): boolean {
    let role: string = storage.get("role")
    if (!role) {
        role = '管理员'
    }
    const menus: MenuRole[] = menu.list()
    for (let i = 0; i < menus.length; i++) {
        if (menus[i].roleName === role) {
            for (let j = 0; j < menus[i].backMenu.length; j++) {
                for (let k = 0; k < menus[i].backMenu[j].child.length; k++) {
                    if (tableName === menus[i].backMenu[j].child[k].tableName) {
                        const buttons: string = menus[i].backMenu[j].child[k].buttons.join(',')
                        return buttons.indexOf(key) !== -1 || false
                    }
                }
            }
        }
    }
    // for(let i=0;i<menus.length;i++){
    //     if(menus[i].roleName==role){
    //         for(let j=0;j<menus[i].backMenu.length;j++){
    //             if(menus[i].backMenu[j].tableName==tableName){
    //                 let buttons = menus[i].backMenu[j].child[0].buttons.join(',');
    //                 return buttons.indexOf(key) !== -1 || false
    //             }
    //         }
    //     }
    // }
    return false
}

