interface Storage {
    set(key: string, value: any): void
    get(key: string): string
    getObj(key: string): any
    remove(key: string): void
}

const storage: Storage = {
    set(key: string, value: any): void {
        localStorage.setItem(key, JSON.stringify(value))
    },
    get(key: string): string {
        return localStorage.getItem(key) ? localStorage.getItem(key)!.replace('"', '').replace('"', '') : ""
    },
    getObj(key: string): any {
        return localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key)!) : null
    },
    remove(key: string): void {
        localStorage.removeItem(key)
    }
}

export default storage