declare module '*.vue' {
  import Vue from 'vue'
  export default Vue
}

declare module 'vue/types/vue' {
  interface Vue {
    $validate: any
    $http: any
    $echarts: any
    $base: any
    $project: string
    $storage: any
    $api: any
    $md5: any
    isAuth: (tableName: string, permission: string) => boolean
  }
}

// Shims for webpack require.context
declare const require: {
  context(directory: string, useSubdirectories: boolean, regExp: RegExp): {
    keys(): string[]
    <T>(id: string): T
  }
}
