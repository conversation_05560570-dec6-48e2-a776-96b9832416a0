// Type declarations for external libraries

declare module 'vue-amap' {
  import { PluginObject } from 'vue'
  const VueAMap: PluginObject<any>
  export default VueAMap
}

declare module 'vue-json-excel' {
  import { Component } from 'vue'
  const JsonExcel: Component
  export default JsonExcel
}

declare module 'print-js' {
  function printJS(options: any): void
  export default printJS
}

declare module 'js-md5' {
  function md5(message: string): string
  export default md5
}

declare module 'vue-quill-editor' {
  import { PluginObject } from 'vue'
  const VueQuillEditor: PluginObject<any>
  export default VueQuillEditor
}

declare module 'path-to-regexp' {
  function pathToRegexp(path: string): RegExp
  namespace pathToRegexp {
    function compile(path: string): (params: any) => string
  }
  export = pathToRegexp
}

// Global window extensions
declare global {
  interface Window {
    AMap: any
  }
}
